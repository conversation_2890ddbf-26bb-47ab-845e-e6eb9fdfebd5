<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>erp-contract</artifactId>
    <groupId>io.terminus.erp</groupId>
    <version>1.0.0.WQ.UAT-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>erp-contract-domain</artifactId>

  <dependencies>
    <dependency>
      <groupId>io.terminus.erp</groupId>
      <artifactId>erp-contract-infrastructure</artifactId>
      <exclusions>
        <exclusion>
          <groupId>io.terminus.common</groupId>
          <artifactId>terminus-spring-boot-starter-mybatis</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.terminus.erp</groupId>
      <artifactId>erp-contract-spi</artifactId>
    </dependency>

    <dependency>
      <groupId>io.terminus</groupId>
      <artifactId>third-party-common</artifactId>
    </dependency>

    <dependency>
      <groupId>io.terminus</groupId>
      <artifactId>third-party-sign-api</artifactId>
    </dependency>

    <dependency>
      <groupId>io.terminus</groupId>
      <artifactId>third-party-sign-esign-cloud</artifactId>
    </dependency>

    <dependency>
      <groupId>io.terminus</groupId>
      <artifactId>third-party-sign-qiyuesuo-private</artifactId>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>io.terminus.cloud</groupId>-->
<!--      <artifactId>spring-boot-starter-cloud-storage</artifactId>-->
<!--      <version>2.0.14.RELEASE</version>-->
<!--    </dependency>-->


    <dependency>
      <groupId>io.terminus.trantor</groupId>
      <artifactId>trantor-org-api</artifactId>
      <version>1.0.0.UAT-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>io.terminus.common</groupId>
          <artifactId>terminus-common-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.terminus.common</groupId>
      <artifactId>terminus-spring-boot-starter-mybatis</artifactId>
      <version>1.0.0.RELEASE</version>
    </dependency>

    <dependency>
      <groupId>io.terminus.common</groupId>
      <artifactId>terminus-spring-boot-starter-rocketmq</artifactId>
    </dependency>

    <dependency>
      <groupId>io.terminus.trantor.workflow</groupId>
      <artifactId>workflow-sdk</artifactId>
    </dependency>

    <!-- poi -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>ooxml-schemas</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <exclusions>
        <!-- surprisingly this artifect contains a subset of org.apache.poi:ooxml-schemas:1.1 -->
        <exclusion>
          <artifactId>poi-ooxml-schemas</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
      <version>4.1.2</version>
    </dependency>
    <!-- itext7html转pdf  -->
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>html2pdf</artifactId>
    </dependency>
    <!-- 中文字体支持 -->
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>font-asian</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.wxpay</groupId>
      <artifactId>wxpay-sdk</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>javase</artifactId>
    </dependency>

    <dependency>
      <groupId>io.terminus.common</groupId>
      <artifactId>terminus-spring-boot-starter-scheduler</artifactId>
      <exclusions>
        <exclusion>
          <groupId>io.terminus.common</groupId>
          <artifactId>terminus-common-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.terminus.erp</groupId>
      <artifactId>terminus-notice-sdk</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>io.terminus.common</groupId>
          <artifactId>terminus-common-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.terminus.common</groupId>
      <artifactId>terminus-common-api</artifactId>
      <version>1.0.1.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>io.terminus.trantor2</groupId>
      <artifactId>trantor-service-runtime-impl</artifactId>
    </dependency>
  </dependencies>
</project>
