-- 签署提醒记录表
CREATE TABLE `con_ct_sign_remind_record_tr` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_id` bigint(20) NOT NULL COMMENT '合同ID',
  `sign_task_id` bigint(20) NOT NULL COMMENT '签署任务ID',
  `signatory_id` bigint(20) NOT NULL COMMENT '签署方ID',
  `sms_send_time` datetime NOT NULL COMMENT '短信发送时间',
  `sms_content` varchar(500) NOT NULL COMMENT '短信内容',
  `mobile` varchar(20) NOT NULL COMMENT '手机号码',
  `remind_type` int(11) NOT NULL COMMENT '提醒类型：1-每2天提醒，2-每天提醒',
  `party_a_sign_time` datetime DEFAULT NULL COMMENT '甲方签署时间',
  `days_since_party_a_sign` int(11) DEFAULT NULL COMMENT '距离甲方签署天数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_sign_task_id` (`sign_task_id`),
  KEY `idx_signatory_id` (`signatory_id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_sms_send_time` (`sms_send_time`),
  KEY `idx_remind_type` (`remind_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='签署提醒记录表'; 