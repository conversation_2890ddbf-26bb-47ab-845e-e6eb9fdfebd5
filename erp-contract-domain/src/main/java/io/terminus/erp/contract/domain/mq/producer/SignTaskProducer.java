package io.terminus.erp.contract.domain.mq.producer;

import io.terminus.common.rocketmq.common.TerminusMessage;
import io.terminus.common.rocketmq.common.TerminusSendResult;
import io.terminus.common.rocketmq.producer.TerminusMQProducer;
import io.terminus.erp.contract.domain.mq.dto.SignTaskMsg;
import io.terminus.thirdparty.common.util.JacksonUtils;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 签署任务消息
 *
 * @className: SignTaskProducer
 * @author: charl
 * @date: 2023/9/6 11:37
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SignTaskProducer {

    @Value("${terminus.topic}")
    private String signTaskTopic;

    @Value("${terminus.signTaskTag:SIGN_TASK_TAG}")
    private String signTaskTag;

    private final TerminusMQProducer producer;

    public void sendMsg(SignTaskMsg signTaskMsg) {
        sendMsg(signTaskMsg, signTaskTopic, signTaskTag);
    }

    private void sendMsg(Object body, String topic, String tag) {
        TerminusMessage terminusMessage = new TerminusMessage();
        terminusMessage.setTopic(topic);
        terminusMessage.setBody(body);
        terminusMessage.setTags(tag);
        if (log.isDebugEnabled()) {
            log.debug("签署任务发送消息到mq，topic:{},tag:{},body:{}", topic, tag, JacksonUtils.obj2json(body));
        }
        TerminusSendResult result = producer.send(terminusMessage);
        log.info("签署模块发送消息到mq，result:{}", result);
        if (Objects.isNull(result.getMessageId())) {
            throw new RuntimeException("消息发送失败");
        }
    }

}
