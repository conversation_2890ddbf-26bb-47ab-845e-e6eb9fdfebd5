package io.terminus.erp.contract.domain.util;

import com.google.common.collect.Sets;
import java.lang.reflect.Field;
import java.util.Objects;
import java.util.Set;

/**
 * 模板字段类型
 *
 * <AUTHOR>
 */
public class ContractFieldType {

    private static Set<String> fieldSet = Sets.newHashSet();

    static {
        ContractFieldType contractFieldType = new ContractFieldType();
        Field[] fields = contractFieldType.getClass().getFields();
        for (Field field : fields) {
            try {
                fieldSet.add((String) field.get(contractFieldType));
            } catch (IllegalAccessException e) {
            }
        }
    }

    /**
     * 文本
     **/
    public static final String TEXT = "TEXT";
    /**
     * 数字
     **/
    public static final String NUMBER = "NUMBER";
    /**
     * 单选
     **/
    public static final String RADIO = "RADIO";
    /**
     * 复选
     **/
    public static final String CHECK = "CHECK";
    /**
     * 日期
     **/
    public static final String DATE = "DATE";
    /**
     * 表格
     **/
    public static final String TABLE = "TABLE";
    /**
     * 图片
     **/
    public static final String IMAGE = "IMAGE";
    /**
     * 公司
     **/
    public static final String PARTNER = "PARTNER";
    /**
     * 采购商
     **/
    public static final String PURCHASE = "PURCHASE";
    /**
     * 供应商
     **/
    public static final String SUPPLIER = "SUPPLIER";

    public static Set<String> getFields() {
        return fieldSet;
    }

    public static String getFieldsByType(String fieldType) {
        if (Objects.nonNull(fieldType)) {
            for (String field : fieldSet) {
                if (Objects.equals(field, fieldType)) {
                    return field;
                }
            }
        } else {
            return ContractFieldType.TEXT;
        }
        return ContractFieldType.TEXT;
    }

}
