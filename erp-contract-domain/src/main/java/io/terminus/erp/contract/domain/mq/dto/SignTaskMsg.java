package io.terminus.erp.contract.domain.mq.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: SignTaskMsg
 * @author: charl
 * @date: 2023/9/6 13:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignTaskMsg implements Serializable {

    /**
     * 单据类型
     */
    private String signBillType;

    /**
     *
     * 废弃：通过订单创建合同，会有合同编码重复的情况。
     *
     * 签署单据编码
     */
    @Deprecated
    private String signBillCode;
    /**
     * 签署单据ID
     */
    private long signBillId;

    /**
     * 签署任务编码
     */
    private Long signTaskId;

    /**
     * 签署状态
     */
    private String signStatus;

}
