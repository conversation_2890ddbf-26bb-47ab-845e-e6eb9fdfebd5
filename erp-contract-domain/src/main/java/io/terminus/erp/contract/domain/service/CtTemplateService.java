package io.terminus.erp.contract.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.api.model.Paging;
import io.terminus.erp.contract.infrastructure.repo.tp.CtTplDraftConfigRepo;
import io.terminus.erp.contract.infrastructure.repo.tpl.CtTemplatePlaceholderConfigRepo;
import io.terminus.erp.contract.spi.convert.tp.CtTplDraftConfigConverter;
import io.terminus.erp.contract.spi.convert.tpl.CtTemplatePlaceholderConfigConverter;
import io.terminus.erp.contract.spi.model.tp.dto.CtTplDraftConfigDTO;
import io.terminus.erp.contract.spi.model.tp.dto.CtTplGangcaiDtoDTO;
import io.terminus.erp.contract.spi.model.tp.po.CtTplDraftConfigPO;
import io.terminus.erp.contract.spi.model.tpl.dto.CtTemplatePlaceholderConfigDTO;
import io.terminus.erp.contract.spi.model.tpl.dto.CtTplPlaceHolderDtoDTO;
import io.terminus.erp.contract.spi.model.tpl.po.CtTemplatePlaceholderConfigPO;
import io.terminus.thirdparty.common.util.IteratorTool;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 合同模版service
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CtTemplateService {

    private final CtTemplatePlaceholderConfigRepo ctTemplatePlaceholderConfigRepo;
    private final CtTemplatePlaceholderConfigConverter ctTemplatePlaceholderConfigConverter;

    private final CtTplDraftConfigRepo ctTplDraftConfigRepo;
    private final CtTplDraftConfigConverter ctTplDraftConfigConverter;


    public Paging<CtTplPlaceHolderDtoDTO> queryTplPrefix(CtTemplatePlaceholderConfigDTO request) {
        LambdaQueryWrapper<CtTemplatePlaceholderConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CtTemplatePlaceholderConfigPO::getBizType, request.getBizType().getId());
        List<CtTemplatePlaceholderConfigPO> ctTemplatePlaceholderConfigPOS = ctTemplatePlaceholderConfigRepo.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(ctTemplatePlaceholderConfigPOS)) {
            return Paging.empty();
        }
        List<CtTemplatePlaceholderConfigPO> sortedList = ctTemplatePlaceholderConfigPOS.stream()
                .sorted(Comparator.comparingInt(CtTemplatePlaceholderConfigPO::getSort))
                .collect(Collectors.toList());
        Map<String, List<CtTemplatePlaceholderConfigPO>> stringListMap = IteratorTool.groupBy(sortedList, CtTemplatePlaceholderConfigPO::getType);
        Map<String, List<CtTemplatePlaceholderConfigPO>> sortedMap = new LinkedHashMap<>();
        for (CtTemplatePlaceholderConfigPO ctTemplatePlaceholderConfigPO : sortedList) {
            List<CtTemplatePlaceholderConfigPO> ctTemplatePlaceholderConfigPOS1 = stringListMap.get(ctTemplatePlaceholderConfigPO.getType());
            sortedMap.putIfAbsent(ctTemplatePlaceholderConfigPO.getType(), ctTemplatePlaceholderConfigPOS1);
        }
        List<CtTplPlaceHolderDtoDTO> resultList = new ArrayList<>();
        for (Entry<String, List<CtTemplatePlaceholderConfigPO>> entry : sortedMap.entrySet()) {
            String type = entry.getKey();
            List<CtTemplatePlaceholderConfigPO> configPOS = entry.getValue();
            List<CtTemplatePlaceholderConfigDTO> ctTemplatePlaceholderConfigDTOS = ctTemplatePlaceholderConfigConverter.po2DtoList(configPOS);
            CtTplPlaceHolderDtoDTO ctTplPlaceHolderDtoDTO = new CtTplPlaceHolderDtoDTO();
            ctTplPlaceHolderDtoDTO.setType(type);
            ctTplPlaceHolderDtoDTO.setTplPrefixList(ctTemplatePlaceholderConfigDTOS);
            resultList.add(ctTplPlaceHolderDtoDTO);
        }
        return Paging.of(resultList.size(), resultList);
    }


    public Paging<CtTplGangcaiDtoDTO> queryDraftPrefix(CtTplDraftConfigDTO request) {
        LambdaQueryWrapper<CtTplDraftConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        List<CtTplDraftConfigPO> ctTplDraftConfigPOS = ctTplDraftConfigRepo.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(ctTplDraftConfigPOS)) {
            return Paging.empty();
        }
        Map<String, List<CtTplDraftConfigPO>> stringListMap = IteratorTool.groupBy(ctTplDraftConfigPOS, CtTplDraftConfigPO::getPlaceHolder);
        List<CtTplGangcaiDtoDTO> resultList = new ArrayList<>();
        for (Entry<String, List<CtTplDraftConfigPO>> stringListEntry : stringListMap.entrySet()) {
            CtTplGangcaiDtoDTO gangcaiDtoDTO = new CtTplGangcaiDtoDTO();
            gangcaiDtoDTO.setPlaceHolder(stringListEntry.getKey());
            List<CtTplDraftConfigPO> value = stringListEntry.getValue();
            List<CtTplDraftConfigDTO> ctTplDraftConfigDTOS = ctTplDraftConfigConverter.po2DtoList(value);
            CtTplDraftConfigPO ctTplDraftConfigPO = value.get(0);
            gangcaiDtoDTO.setPlaceHolderName(ctTplDraftConfigPO.getPlaceHolderName());
            gangcaiDtoDTO.setDraftConfig(ctTplDraftConfigDTOS);
            resultList.add(gangcaiDtoDTO);
        }
        return Paging.of(resultList.size(), resultList);
    }


}
