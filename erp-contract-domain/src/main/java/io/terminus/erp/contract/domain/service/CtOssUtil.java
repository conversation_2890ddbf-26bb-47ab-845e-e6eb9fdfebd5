package io.terminus.erp.contract.domain.service;

import io.terminus.cloud.storage.core.client.ClientType;
import io.terminus.cloud.storage.core.client.CloudClient;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.contract.domain.util.FileHelper;
import io.terminus.trantor2.module.oss.OssConfig;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import io.terminus.trantor2.module.service.OSSService;
import jodd.net.URLDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

/**
 * @className: CtOssUtil
 * @author: charl
 * @date: 2024/1/23 14:00
 */
@Slf4j
@Component
@DependsOn({"ossConfig"})
public class CtOssUtil {

    @Autowired
    private OssConfig ossConfig;

    @Autowired
    private OSSService ossService;

    @Autowired
    private CloudClient cloudClient;

    public String getFileUrl(String objectName, CloudClient cloudClient, Boolean isPrivate) {
        if (StringUtils.isBlank(objectName)) {
            return null;
        } else {
            if (objectName.startsWith("http://") || objectName.startsWith("https://")) {
                String endpoint = this.getEndpoint(null);
                objectName = objectName.replaceFirst(endpoint, "");
            }

            if (objectName.startsWith("/")) {
                objectName = objectName.replaceFirst("/", "");
            }

            return this.getFileURL(objectName, cloudClient, isPrivate);
        }
    }

    public String getFileURL(String fileName, CloudClient cloudClient, Boolean isPrivate) {
        if (isPrivate == null) {
            isPrivate = this.ossConfig.isPrivateRead();
        }

        String aimBucket = this.getBucket(isPrivate);

        try {
            if (isPrivate) {
                String url = cloudClient.getTempFileUrl(aimBucket, fileName, this.ossConfig.getUrlExpireTime());
                return URLDecoder.decode(url);
            } else {
                return this.getEndpoint(isPrivate) + "/" + fileName;
            }
        } catch (Exception var5) {
            var5.printStackTrace();
            log.warn("fail to getFileURL, bucket name:{}, objectName:{}", aimBucket, fileName);
            return null;
        }

    }

    public String getEndpoint(Boolean isPrivate) {
        ClientType type = (ClientType) EnumUtils.getEnumIgnoreCase(ClientType.class, this.ossConfig.getFileStoreType());
        String bucket = this.getBucket(isPrivate);
        String endpoint = this.ossConfig.getEndpoint();
        String scheme = "";
        if (endpoint.startsWith("http://")) {
            endpoint = endpoint.replaceFirst("http://", "");
            scheme = "http://";
        }

        if (endpoint.startsWith("https://")) {
            endpoint = endpoint.replaceFirst("https://", "");
            scheme = "https://";
        }

        if (endpoint.endsWith("/")) {
            endpoint = endpoint.substring(0, endpoint.length() - 1);
        }

        if (!Objects.equals(type, ClientType.MINIO) && !Objects.equals(type, ClientType.MINIO_GATEWAY)) {
            endpoint = bucket + "." + endpoint;
        } else {
            endpoint = endpoint + "/" + bucket;
        }

        return scheme + endpoint;
    }


    public String getBucket(Boolean isPrivate) {
        if (isPrivate == null) {
            isPrivate = this.ossConfig.isPrivateRead();
        }
        return isPrivate ? this.ossConfig.getPrivateBucket() : this.ossConfig.getPublicBucket();
    }

    /**
     * 从OSS下载文件，并且暂存在/tmp/CtOssUtil/目录下，返回File对象。
     * 其中File对象需要被调用方主动删除
     * @param url
     * @return
     */
    public File getFileFromOSS(URL url, String extension) {
        String path = url.getPath();
        String bucket = getBucket(true);
        String objectName = path.replaceAll("/" + bucket + "/", "");
        InputStream inputStream = ossService.migrateFileOut(objectName, true);
        try {
            if (inputStream == null) {
                log.error("从云存储读取文件失败：{}", url);
                return null;
            }
            Path target = Paths.get("/tmp/", "CtOssUtil_" + UUID.randomUUID() +"."+ extension);
            Files.copy(inputStream, target);
            inputStream.close();
            return target.toFile();
        } catch (IOException e) {
            throw new BusinessException("读取文件失败：{}" + url);
        }
    }


    /**
     * 上传文件
     *
     * @param inputStream
     * @param fileName
     * @param fileType
     * @return
     * @throws IOException
     */
    public String uploadWithFileName(InputStream inputStream, String fileName, String fileType) {
        String bucket = getBucket(null);
        cloudClient.uploadFile(bucket, fileName, inputStream, fileType);
        return ossService.getFileUrl(fileName, true);
    }

    /**
     * 上传文件，获取不带sts的访问参数
     *
     * @param inputStream
     * @param fileName
     * @param fileType
     * @return
     * @throws IOException
     */
    public String uploadWithFileNameAndGetPublicUrl(InputStream inputStream, String fileName, String fileType) {
        String bucket = getBucket(null);
        return ossService.uploadFileAndGetUrl(bucket, fileName, inputStream, fileType, false);
    }

    /**
     * 先尝试直接下载，下载不下来，再尝试通过对应环境的OSS（minio）下载。
     * 如果把生产环境的私有读连接，再非生产环境是无法成功下载的。
     *
     * @param fileUrl
     * @return
     */
    public  File downloadFileFromUrl(String fileUrl) {
        URL url = null;
        try {
            url = new URL(fileUrl);
        } catch (MalformedURLException e) {
            throw new BusinessException("URL不合法：" + fileUrl);
        }
        String path = url.getPath();
        String extension = path.substring(path.lastIndexOf('.') + 1);
        File file = FileHelper.transformUrlToAttachment(fileUrl, extension);
        if (file == null) {
            file = getFileFromOSS(url, extension);
        }
        if (file == null) {
            throw new BusinessException("文件下载失败");
        }
        return file;
    }


    public static void deleteFiles(List<File> files) {
        for (File file : files) {
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }


    public static void main(String[] args) throws MalformedURLException {
        URL url = new URL("http://minio-tenant.nonprod.hqzc.com/terminus-new-trantor/terminus-new-trantor/7bf33d75-51a4-4594-8f67-e4941085d3f6/合同:HT-WL-*********** 甲方:邹平县宏正新材料科技有限公司 乙方:邹平县汇盛新材料科技有限公司的签署文件.pdf#1234?a=bbb");
        System.out.println(url.getFile());
        System.out.println(url.getPath());
        System.out.println(url.getRef());
        System.out.println(url.getQuery());
    }
}
