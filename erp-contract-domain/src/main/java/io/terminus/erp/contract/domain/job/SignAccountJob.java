package io.terminus.erp.contract.domain.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.scheduler.annotation.Job;
import io.terminus.erp.contract.domain.service.CtBaseService;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSignatoryAccountTrRepo;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryAccountTrSignatoryAccountStatusDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryAccountTrTypeDict;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignatoryAccountTrPO;
import io.terminus.notice.sdk.req.task.NoticeTaskCreateReq0;
import io.terminus.notice.sdk.service.NoticeService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 签署账户任务
 *
 * @className: SignAccountJob
 * @author: charl
 * @date: 2023/11/8 14:07
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SignAccountJob {

    private final CtBaseService ctBaseService;
    private final NoticeService noticeService;
    private final ConCtSignatoryAccountTrRepo conCtSignatoryAccountTrRepo;

    @Value("${terminus.contract.notice.template.ukey:*****************}")
    private String ukeyNoticeTemplate;

    @Job(key = "UKEY_SIGN_ACCOUNT_JOB", name = "定时扫描UKEY签署账户，是否过期，并给出过期通知")
    public void ukeyScanJob() {
        log.info("开始执行UKEY过期账户扫描");
        LambdaQueryWrapper<ConCtSignatoryAccountTrPO> accountQuery = new LambdaQueryWrapper<>();
        accountQuery.eq(ConCtSignatoryAccountTrPO::getType, ConCtSignatoryAccountTrTypeDict.UKEY);
        accountQuery.eq(ConCtSignatoryAccountTrPO::getSignatoryAccountStatus, ConCtSignatoryAccountTrSignatoryAccountStatusDict.ACTIVE);
        List<ConCtSignatoryAccountTrPO> conCtSignatoryAccountTrPOS = conCtSignatoryAccountTrRepo.selectList(accountQuery);
        if (CollectionUtils.isEmpty(conCtSignatoryAccountTrPOS)) {
            return;
        }
        LocalDate nowDate = LocalDate.now();
        for (ConCtSignatoryAccountTrPO conCtSignatoryAccountTrPO : conCtSignatoryAccountTrPOS) {
            LocalDateTime signatoryAccountLoseTime = conCtSignatoryAccountTrPO.getSignatoryAccountLoseTime();
            LocalDate localDate = signatoryAccountLoseTime.toLocalDate();
            long between = ChronoUnit.DAYS.between(nowDate, localDate);
            if (between >= 0 && between <= 30) {
                NoticeTaskCreateReq0 req = new NoticeTaskCreateReq0();
                req.setTaskName("通知任务" + System.currentTimeMillis());
                Long userId = ctBaseService.getUserBySupplierCompany(conCtSignatoryAccountTrPO.getRelCompany());
                if (Objects.isNull(userId)) {
                    continue;
                }
                req.setNoticeTargetIds(Arrays.asList(userId));
                req.setNoticeBusinessCode(ukeyNoticeTemplate);
                noticeService.createTaskByTemplate(req);
            }
            // 更新为未激活
            if (between <= 0) {
                conCtSignatoryAccountTrPO.setSignatoryAccountStatus(ConCtSignatoryAccountTrSignatoryAccountStatusDict.INACTIVE);
                conCtSignatoryAccountTrRepo.updateById(conCtSignatoryAccountTrPO);
            }
        }

    }
}
