package io.terminus.erp.contract.domain.mq.producer;

import io.terminus.common.rocketmq.common.TerminusMessage;
import io.terminus.common.rocketmq.common.TerminusSendResult;
import io.terminus.common.rocketmq.producer.TerminusMQProducer;
import io.terminus.thirdparty.common.util.JacksonUtils;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @className: ContractProducer
 * @author: charl
 * @date: 2023/8/13 14:43
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContractProducer {

    @Value("${terminus.topic}")
    private String conTopic;

    //合同签署完成消息
    @Value("${terminus.conSignedTag:CON_SIGNED_TAG}")
    private String conSignedTag;

    @Value("${terminus.conQualitySignedTag:CON_QUALITY_SIGNED_TAG}")
    private String conQualitySignedTag;

    //合同失效消息
    @Value("${terminus.conInvalidTag:CON_INVALID_TAG}")
    private String conInvalidTag;


    private final TerminusMQProducer producer;

    /**
     * 合同签署完成发送消息
     * @param body
     */
    public void sendMsgOnConSigned(Object body) {
        sendMsg(body, conTopic, conSignedTag);
        sendMsg(body,conTopic,conQualitySignedTag);
    }

    /**
     * 合同签署完成发送消息
     * @param body
     */
    public void sendMsgOnConInvalid(Object body) {
        sendMsg(body, conTopic, conInvalidTag);
    }


    private void sendMsg(Object body, String topic, String tag) {
        TerminusMessage terminusMessage = new TerminusMessage();
        terminusMessage.setTopic(topic);
        terminusMessage.setBody(body);
        terminusMessage.setTags(tag);
        if (log.isDebugEnabled()) {
            log.debug("合同模块发送消息到mq，topic:{},tag:{},body:{}", topic, tag, JacksonUtils.obj2json(body));
        }
        TerminusSendResult result = producer.send(terminusMessage);
        log.info("合同模块发送消息到mq，result:{}", result);
        if (Objects.isNull(result.getMessageId())) {
            throw new RuntimeException("消息发送失败");
        }
    }

}
