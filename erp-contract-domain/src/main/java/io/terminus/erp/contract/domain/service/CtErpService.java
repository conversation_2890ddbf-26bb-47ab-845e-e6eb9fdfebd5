package io.terminus.erp.contract.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.response.Response;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.erp.contract.domain.mq.producer.ContractProducer;
import io.terminus.erp.contract.domain.util.FileHelper;
import io.terminus.erp.contract.domain.util.PDFUtil;
import io.terminus.erp.contract.infrastructure.repo.tp.*;
import io.terminus.erp.contract.spi.convert.tp.ConCtSignTypeConfConverter;
import io.terminus.erp.contract.spi.convert.tp.GenCtHeadTrExtConverter;
import io.terminus.erp.contract.spi.convert.tp.GenCtOrderLinkTrConverter;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryTrSignatoryStatusDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSigntaskTrSignStatusDict;
import io.terminus.erp.contract.spi.dict.tp.CtHeadStatusDict;
import io.terminus.erp.contract.spi.model.tp.dto.*;
import io.terminus.erp.contract.spi.model.tp.po.*;
import io.terminus.erp.contract.spi.msg.CtSignMsg;
import io.terminus.erp.md.infrastructure.repo.base.GenAttachmentTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.base.GenComTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.ct.CtHeadTrRepo;
import io.terminus.erp.md.infrastructure.repo.ct.CtItemTrRepo;
import io.terminus.erp.md.infrastructure.repo.ct.GenCtAttachmentLinkTrRepo;
import io.terminus.erp.md.infrastructure.repo.ct.GenCtPartnerLinkTrRepo;
import io.terminus.erp.md.infrastructure.repo.mat.GenMatMdRepo;
import io.terminus.erp.md.infrastructure.repo.org.OrgEmployeeMdRepo;
import io.terminus.erp.md.infrastructure.repo.util.MdBillLineOperateUtils;
import io.terminus.erp.md.infrastructure.repo.vend.GenVendPersonLinkMdRepo;
import io.terminus.erp.md.spi.convert.CtHeadTrConverter;
import io.terminus.erp.md.spi.convert.CtItemTrConverter;
import io.terminus.erp.md.spi.convert.base.GenComTypeCfConverter;
import io.terminus.erp.md.spi.convert.ct.GenCtAttachmentLinkTrConverter;
import io.terminus.erp.md.spi.convert.ct.GenCtPartnerLinkTrConverter;
import io.terminus.erp.md.spi.dict.base.GenContractAttachmentFillStageDict;
import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import io.terminus.erp.md.spi.model.dto.ct.CtHeadTrDTO;
import io.terminus.erp.md.spi.model.dto.ct.CtItemTrDTO;
import io.terminus.erp.md.spi.model.dto.ct.GenCtAttachmentLinkTrDTO;
import io.terminus.erp.md.spi.model.po.base.GenAttachmentTypeCfPO;
import io.terminus.erp.md.spi.model.po.base.GenComTypeCfPO;
import io.terminus.erp.md.spi.model.po.ct.CtHeadTrPO;
import io.terminus.erp.md.spi.model.po.ct.CtItemTrPO;
import io.terminus.erp.md.spi.model.po.ct.GenCtAttachmentLinkTrPO;
import io.terminus.erp.md.spi.model.po.ct.GenCtPartnerLinkTrPO;
import io.terminus.erp.md.spi.model.po.mat.GenMatMdPO;
import io.terminus.erp.md.spi.model.po.org.OrgEmployeeMdPO;
import io.terminus.erp.md.spi.model.po.vend.GenVendInfoMdPO;
import io.terminus.erp.md.spi.model.po.vend.GenVendPersonLinkMdPO;
import io.terminus.erp.md.spi.util.MD;
import io.terminus.erp.purchase.spi.model.po.dto.PurPoHeadTrDTO;
import io.terminus.trantor.org.api.facade.EmployeeReadFacade;
import io.terminus.trantor.org.spi.model.dto.EmployeeDTO;
import io.terminus.trantor.org.spi.model.dto.EmployeeQueryDTO;
import io.terminus.trantor.workflow.runtime.v1.app.dto.UserInfo;
import io.terminus.trantor.workflow.runtime.v2.model.dto.WorkflowTaskInstanceDTO;
import io.terminus.trantor.workflow.sdk.core.Workflow;
import io.terminus.trantor.workflow.sdk.model.request.QueryWorkflowInstanceByGroupRequest;
import io.terminus.trantor.workflow.sdk.model.response.QueryWorkflowInstanceByGroupResponse;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.module.service.OSSService;
import io.terminus.wq.md.spi.convert.org.OrgEmployeeMdExtConverter;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static io.terminus.erp.md.spi.message.CtMsg.*;

/**
 * 协议合同app service
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CtErpService {

    private final CtHeadTrRepo ctHeadTrRepo;

    private final CtItemTrRepo ctItemTrRepo;

    private final CtHeadTrConverter ctHeadTrConverter;

    private final CtItemTrConverter ctItemTrConverter;

    private final ContractProducer contractProducer;

    private final ConCtSignTypeConfRepo conCtSignTypeConfRepo;

    private final ConCtSignTypeConfConverter conCtSignTypeConfConverter;

    private final GenComTypeCfRepo genComTypeCfRepo;

    private final EmployeeReadFacade employeeReadFacade;

    private final GenComTypeCfConverter genComTypeCfConverter;

    private final GenCtHeadTrExtConverter genCtHeadTrExtConverter;

    private final GenCtAttachmentLinkTrRepo genCtAttachmentLinkTrRepo;

    private final GenCtAttachmentLinkTrConverter genCtAttachmentLinkTrConverter;

    private final GenCtPartnerLinkTrRepo genCtPartnerLinkTrRepo;

    private final GenCtPartnerLinkTrConverter genCtPartnerLinkTrConverter;

    private final GenCtOrderLinkTrConverter genCtOrderLinkTrConverter;

    private final GenCtOrderLinkTrRepo genCtOrderLinkTrRepo;

    private final ConCtSignfileTrRepo conCtSignfileTrRepo;

    private final GenAttachmentTypeCfRepo genAttachmentTypeCfRepo;

    private final GenAttachmentTypeCfRepo attachmentTypeCfRepo;

    private final CtBaseService ctBaseService;

    private final GenMatMdRepo genMatMdRepo;

    private final String[] CT_ADMIN_LINK_ATTACHMENT_CODE = new String[]{"FTYPE0003","FTYPE0005"};

    private final IdGenerator idGenerator;

    private final GenVendPersonLinkMdRepo genVendPersonLinkMdRepo;

    private final ConCtSigntaskTrRepo conCtSigntaskTrRepo;

    private final ConCtSignatoryTrRepo conCtSignatoryTrRepo;

    private final static String INIT_VERSION = "1";
    // 合同取编码的规则key,在console模块取号中心可以查
    private final String CT_ITEM_RULE_KEY = "TERP_MIGRATE$gen_ct_item_tr_code";

    private final static ObjectMapper objectMapper = new ObjectMapper();
    private final OrgEmployeeMdRepo orgEmployeeMdRepo;
    private final OrgEmployeeMdExtConverter orgEmployeeMdExtConverter;
    private final ConCtSignImgCfRepo conCtSignImgCfRepo;
    private final Workflow workflow;

    private final OSSService ossService;

    public void saveCompletedCheck(CtHeadTrDTO request) {
        // 只检查合同名称 判断文件有没有被修改
        checkAttachmentDuplicateType(request);
        checkCtItemDuplicateMaterial(request.getCtItemList());
    }

    public void updateSignForDateAction(CtHeadTrDTO ctHeadTrDTO) {
        Long id = ctHeadTrDTO.getId();

        CtHeadTrPO ctHeadTrPO = ctHeadTrRepo.selectById(id);
        ctHeadTrPO.setSignForDate(LocalDateTime.now());
        ctHeadTrRepo.updateById(ctHeadTrPO);
    }

    public Long save(GenCtHeadTrExtDTO ctHead) {
        checkCtItemDuplicateMaterial(ctHead.getCtItemList());
        if (ctHead.getId() == null) {
            return create(ctHead, ctHead.getCtCode(), INIT_VERSION);
        }
        update(ctHead);
        return ctHead.getId();
    }

    /**
     * 更新档案编号、归档文件
     * @param ctHead
     * @return
     */
    public Long archive(GenCtHeadTrExtDTO ctHead) {

        log.info("开始归档合同：{}", ctHead);

        List<GenCtAttachmentLinkTrPO> attachmentsToSave = ctHead.getAttachment()
                .stream()
                .filter(attachment -> {
                    Long attachmentType = attachment.getAttachmentType();
                    GenAttachmentTypeCfPO genAttachmentTypeCfPO = genAttachmentTypeCfRepo.selectById(attachmentType);

                    attachment.setDocRef(ctHead.getId());

                    // 仅保存新增的，填写阶段为"归档"阶段
                    return  GenContractAttachmentFillStageDict.ARCHIVING.equals(genAttachmentTypeCfPO.getExtWqFillStage())
                            && StringUtil.isNotBlank(attachment.getAttachmentUrl())
                            && !"[]".equals(attachment.getAttachmentUrl());
                })
                .map(genCtAttachmentLinkTrConverter::dto2Po)
                .collect(Collectors.toList());

        //if(CollectionUtils.isEmpty(attachmentsToSave)){
        //    throw new BusinessException("没有要归档的文件");
        //}

        final CtHeadTrPO theOldCtHead = ctHeadTrRepo.selectById(ctHead.getId());
        Map<String, Object> extra = theOldCtHead.getExtra();
        if(extra != null && Boolean.TRUE.equals(extra.get(GenCtHeadTrExtDTO.Fields.extWqArchived))){
            throw new BusinessException("合同已归档");
        }

        updateArchiveNo(ctHead, theOldCtHead);

        genCtAttachmentLinkTrRepo.updateBatch(attachmentsToSave);

        return ctHead.getId();
    }

    public Long change(GenCtHeadTrExtDTO ctHead) {
        checkCtItemDuplicateMaterial(ctHead.getCtItemList());

        if (!StringUtils.hasText(ctHead.getCtCode())) {
            throw new BusinessException(CT_H_CODE_IS_EMPTY);
        }
        // 将原有记录的最新版本改为false
        CtHeadTrPO oldCurrentVersionRecord = ctHeadTrRepo.selectCurrentVersionByCtCode(ctHead.getCtCode());
        oldCurrentVersionRecord.setCurrentVersion(false);
        ctHeadTrRepo.updateById(oldCurrentVersionRecord);

        // 生成一个新的版本
        String oldVersion = oldCurrentVersionRecord.getCtVersion();
        int oldVersionNo = Integer.parseInt(oldVersion);
        String newVersion = String.valueOf(oldVersionNo + 1);

        return create(ctHead, ctHead.getCtCode(), newVersion);
    }

    public Long create(GenCtHeadTrExtDTO ctHead, String ctCode, String version) {
        CtHeadTrDTO convert = genCtHeadTrExtConverter.convert(ctHead);
        CtHeadTrPO ctHeadPO = ctHeadTrConverter.dto2Po(convert);
        if(ctCode == null){
            ctCode =  ctBaseService.generateModelCode(CtHeadTrPO.class);
        }
        ctHeadPO.setId(null);
        ctHeadPO.setCtCode(ctCode);
        ctHeadPO.setCtVersion(version);
        ctHeadPO.setCurrentVersion(true);
        ctHeadPO.setCreatedAt(null);
        ctHeadPO.setUpdatedAt(null);
        // 含税金额
        BigDecimal ctTaxAmt = ctHeadPO.getCtTaxAmt();
        if (ctTaxAmt != null) {
            String ctTaxAmtChinese = Convert.digitToChinese(ctTaxAmt);
            ctHeadPO.setCtTaxAmtChinese(ctTaxAmtChinese);
        }
        // 添加供应商的联系方式
        LambdaQueryWrapper<GenVendPersonLinkMdPO> vendPersonLinkMdPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vendPersonLinkMdPOLambdaQueryWrapper.eq(GenVendPersonLinkMdPO::getVendId, ctHeadPO.getPrtnB());
        vendPersonLinkMdPOLambdaQueryWrapper.last("limit 0, 1");
        GenVendPersonLinkMdPO genVendPersonLinkMdPO = genVendPersonLinkMdRepo.selectOne(vendPersonLinkMdPOLambdaQueryWrapper);
        if (Objects.nonNull(genVendPersonLinkMdPO)) {
            ctHeadPO.setBPhone(genVendPersonLinkMdPO.getPhone());
        }
        ctHeadTrRepo.insert(ctHeadPO);
        List<CtItemTrDTO> items = ctHead.getCtItemList();
        if (CollectionUtils.isNotEmpty(items)) {
            // 获取物料信息
            Map<Long, GenMatMdPO> genMatMdInfoMap = findGenMatMdInfoMap(items);
            List<CtItemTrPO> poItems = items.stream()
                .peek(dto-> {
                    // 这个其实是物料的ID
                    Long matId = dto.getMatCode();
                    // 获取物料信息
                    GenMatMdPO genMatMdPO = genMatMdInfoMap.get(matId);
                    if (Objects.isNull(genMatMdPO)) {
                        throw new BusinessException("物料信息不存在");
                    }
                    // 技术附件（物料的）
                    dto.setExtWqAttachment(genMatMdPO.getExtWqAttachment());
                })
                .map(ctItemTrConverter::dto2Po)
                .peek(ctItem -> {
                    ctItem.setId(null);
                    String ctItemCode = idGenerator.nextCode(CtItemTrPO.class, CT_ITEM_RULE_KEY);
                    ctItem.setCtItemCode(ctItemCode);
                    ctItem.setGenCtHeadTrId(ctHeadPO.getId());
                    // 处理技术附件
                })
                .collect(Collectors.toList());
            ctItemTrRepo.insertBatch(poItems);
        }

        // 处理合同明细 这个需要将是否需要盖章添加进来 因为在合同详情页面需要使用 但是合同详情页面不能关联 所以需要在这个地方冗余一下
        List<GenCtAttachmentLinkTrDTO> attachmentList = ctHead.getAttachment();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            // 查询合同文件类型信息
            Map<Long, GenAttachmentTypeCfPO> genAttachmentTypeCfPOMap = findGenAttachmentTypeCf(attachmentList);

            List<GenCtAttachmentLinkTrPO> poAttachments = attachmentList.stream()
                .map(genCtAttachmentLinkTrConverter::dto2Po).peek(attachment -> {
                        attachment.setId(null);
                        attachment.setDocRef(ctHeadPO.getId());
                        // 处理是否需要盖章
                        GenAttachmentTypeCfPO genAttachmentTypeCfPO = genAttachmentTypeCfPOMap.get(attachment.getAttachmentType());
                        attachment.setExtWqStamped(genAttachmentTypeCfPO.getExtWqStamped());
                    }).collect(Collectors.toList());
            genCtAttachmentLinkTrRepo.insertBatch(poAttachments);
        }

        // 相关方
        if (CollectionUtils.isNotEmpty(ctHead.getPartner())) {
            List<GenCtPartnerLinkTrPO> partnerLinks = ctHead.getPartner().stream().map(genCtPartnerLinkTrConverter::dto2Po).peek(partner -> {
                partner.setId(null);
                partner.setDocRef(ctHeadPO.getId());
            }).collect(Collectors.toList());
            genCtPartnerLinkTrRepo.insertBatch(partnerLinks);
        }

        // 合同关联订单
        if (CollectionUtils.isNotEmpty(ctHead.getExtWqCtOrderRef())) {
            List<Long> orderIds = ctHead.getExtWqCtOrderRef().stream()
                    .map(GenCtOrderLinkTrDTO::getOrderRef)
                    .map(PurPoHeadTrDTO::getId).distinct().collect(Collectors.toList());

            LambdaQueryWrapper<GenCtOrderLinkTrPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(GenCtOrderLinkTrPO::getOrderRef, orderIds);
            List<GenCtOrderLinkTrPO> genCtOrderLinkTrPOS = genCtOrderLinkTrRepo.selectList(wrapper);

            // 如果不是空的 那么查看合同的数据 状态
            if (CollectionUtils.isNotEmpty(genCtOrderLinkTrPOS)) {
                // 已经创建的合同Ids
                List<Long> ctIds = genCtOrderLinkTrPOS
                        .stream().map(GenCtOrderLinkTrPO::getDocRef).distinct().collect(Collectors.toList());
                List<CtHeadTrPO> ctHeadTrPOS = ctHeadTrRepo.selectBatchIds(ctIds);
                if (CollectionUtils.isNotEmpty(ctHeadTrPOS)) {
                    List<String> status = Lists.newArrayList("CANCELLED", "EXPIRED");
                    for (CtHeadTrPO ctHeadTrPO : ctHeadTrPOS) {
                        if (status.contains(ctHeadTrPO.getCtStatus())) {
                            continue;
                        }
                        if (Objects.equals(ctHeadTrPO.getId(), ctHeadPO.getId())) {
                            continue;
                        }
                        throw new BusinessException(ctHeadTrPO.getCtCode() + "已经关联当前选中订单，切状态不为已作废或已失效，请勿重复提交");
                    }
                }
            }
            List<GenCtOrderLinkTrPO> relOrders = ctHead.getExtWqCtOrderRef().stream().map(genCtOrderLinkTrConverter::dto2Po).peek(ctOrder -> {
                ctOrder.setId(null);
                ctOrder.setDocRef(ctHeadPO.getId());
            }).collect(Collectors.toList());
            genCtOrderLinkTrRepo.insertBatch(relOrders);
        }

        return ctHeadPO.getId();
    }

    public Map<Long, GenMatMdPO> findGenMatMdInfoMap(List<CtItemTrDTO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new HashMap<>();
        }
        List<Long> genMatMdIds = items.stream().map(CtItemTrDTO::getMatCode).distinct().collect(Collectors.toList());
        List<GenMatMdPO> genMatMdPOS = genMatMdRepo.selectBatchIds(genMatMdIds);
        if (CollectionUtils.isEmpty(genMatMdPOS)) {
            return new HashMap<>();
        }
        return genMatMdPOS.stream().collect(Collectors.toMap(GenMatMdPO::getId, Function.identity()));
    }


    public void update(GenCtHeadTrExtDTO ctHead) {

        updateContractItem(ctHead);
        // 更新普通文件(非编制文件)
        updateContractAttachment(ctHead);

        updateContractPartner(ctHead);
        // 下面这段代码 没用；合同有订单转过来的 不会直接操作订单行 这个不能被删除！
        // updateContractRelatedOrder(ctHead);
        // 将编制后的文件插入到  gen_ct_attachment_link_tr
        updateContractDraftFile(ctHead);

        updateContractHead(ctHead);
    }

    private void updateContractHead(GenCtHeadTrExtDTO ctHead) {
        CtHeadTrDTO convert = genCtHeadTrExtConverter.convert(ctHead);
        CtHeadTrPO ctHPO = ctHeadTrConverter.dto2Po(convert);
        // 含税金额
        BigDecimal ctTaxAmt = ctHPO.getCtTaxAmt();
        if (ctTaxAmt != null) {
            String ctTaxAmtChinese = Convert.digitToChinese(ctTaxAmt);
            ctHPO.setCtTaxAmtChinese(ctTaxAmtChinese);
        }
        // 添加供应商的联系方式
        LambdaQueryWrapper<GenVendPersonLinkMdPO> vendPersonLinkMdPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vendPersonLinkMdPOLambdaQueryWrapper.eq(GenVendPersonLinkMdPO::getVendId, ctHPO.getPrtnB());
        vendPersonLinkMdPOLambdaQueryWrapper.last("limit 0, 1");
        GenVendPersonLinkMdPO genVendPersonLinkMdPO = genVendPersonLinkMdRepo.selectOne(vendPersonLinkMdPOLambdaQueryWrapper);
        if (Objects.nonNull(genVendPersonLinkMdPO)) {
            ctHPO.setBPhone(genVendPersonLinkMdPO.getPhone());
        }
        ctHeadTrRepo.updateById(ctHPO);
    }

    /**
     * 保存编制后的文件
     * 这个地方注意 1. 可能存在先点保存 后点提交；这个点 attachments 已经存在 编制合同文件了；这个可以通过文件签署类型 将老版本的文件的逻辑替换一下
     *            2. 由于给予html生成pdf请求返回比较慢；用户可能文件还没有生成之后 就直接提交文件 那么此时 ExtWqDraftFile 这个字段是null；解决思路 null 直接报错
     *            3. 形如先保存，后提交的场景，其实保存的时候编制文件已经生成了 那么如果在准备提交审批的时候 修改了html 那么此时没有生成pdf 此时文件还是老版本的 解决思路 需要在提交审批的时候二次确认
     * @param ctHead
     */
    private void updateContractDraftFile(GenCtHeadTrExtDTO ctHead) {
        // 处理合同编制文件
        if (StringUtil.isNotEmpty(ctHead.getExtWqDraftFile())) {
            Optional<GenAttachmentTypeCfPO> ftype0006 = getFtype0006();
            LambdaQueryWrapper<GenCtAttachmentLinkTrPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GenCtAttachmentLinkTrPO::getDocRef, ctHead.getId());
            GenAttachmentTypeCfPO genAttachmentTypeCfPO = ftype0006.get();
            queryWrapper.eq(GenCtAttachmentLinkTrPO::getAttachmentType, genAttachmentTypeCfPO.getId());
            GenCtAttachmentLinkTrPO attachmentLinkTrPO = genCtAttachmentLinkTrRepo.selectOne(queryWrapper);
            String oriUrl = FileHelper.parseSignedUrl(ctHead.getExtWqDraftFile());
            // 需要讲这个url 放到List种 然后转JSON一下 因为这个文件现在可以上传多个了 那么这个地方保存的格式需要转换成 [] 这种
            List<String> urlList = new ArrayList<>();
            urlList.add(oriUrl);
            try {
                // 将文件url转换一下 这个地方要支持上传多个
                oriUrl = objectMapper.writeValueAsString(urlList);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            // 获取合同明细 这个地方注意 1. 可能存在先点保存 后点提交；这个点 attachments 已经存在 编制合同文件了；这个可以通过文件签署类型 将老版本的文件的逻辑替换一下
            List<GenCtAttachmentLinkTrDTO> attachments = ctHead.getAttachment();
            if (CollectionUtils.isEmpty(attachments)) {
                attachments = new ArrayList<>();
            }
            if (Objects.isNull(attachmentLinkTrPO)) {
                // 附件添加到合同附件表
                attachmentLinkTrPO = new GenCtAttachmentLinkTrPO();
                attachmentLinkTrPO.setDocRef(ctHead.getId());
                attachmentLinkTrPO.setAttachmentType(genAttachmentTypeCfPO.getId());
                attachmentLinkTrPO.setAttachmentUrl(oriUrl); // 一切ok
                attachmentLinkTrPO.setExtWqStamped(genAttachmentTypeCfPO.getExtWqStamped());
                genCtAttachmentLinkTrRepo.insert(attachmentLinkTrPO);
                // 将 编织文件 放到上下文中
                GenCtAttachmentLinkTrDTO genCtAttachmentLinkTrDTO = getGenCtAttachmentLinkTrDTO(attachmentLinkTrPO);
                attachments.add(genCtAttachmentLinkTrDTO);
                ctHead.setAttachment(attachments);
            } else {
                // 附件添加到合同附件表
                for (GenCtAttachmentLinkTrDTO attachment : attachments) {
                    // 处理问题1 先保存后提交的逻辑
                    if (attachment.getAttachmentType().equals(genAttachmentTypeCfPO.getId())) {
                        attachment.setAttachmentUrl(oriUrl);
                    }
                }
                attachmentLinkTrPO.setAttachmentUrl(oriUrl); // 一切ok
                genCtAttachmentLinkTrRepo.updateById(attachmentLinkTrPO);
            }
        }
    }

    /**
     * 编制合同附件类型
     * @return
     */
    private Optional<GenAttachmentTypeCfPO> getFtype0006() {
        List<GenAttachmentTypeCfPO> genAttachmentTypeCfPOS = genAttachmentTypeCfRepo.selectList(new LambdaQueryWrapper<>());

        return genAttachmentTypeCfPOS.stream().filter(v -> v.getAttachmentCode().equals("FTYPE0006")).findFirst();
    }

    /**
     * 查询合同附件中的系统编制生成的附件
     * @param contId
     * @return
     */
    private GenCtAttachmentLinkTrPO getTheDraftedContractAttachment(long contId){
        Optional<GenAttachmentTypeCfPO> ftype0006 = getFtype0006();
        LambdaQueryWrapper<GenCtAttachmentLinkTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GenCtAttachmentLinkTrPO::getDocRef, contId);
        GenAttachmentTypeCfPO genAttachmentTypeCfPO = ftype0006.get();
        queryWrapper.eq(GenCtAttachmentLinkTrPO::getAttachmentType, genAttachmentTypeCfPO.getId());
        GenCtAttachmentLinkTrPO genCtAttachmentLinkTrPO = genCtAttachmentLinkTrRepo.selectOne(queryWrapper);
        String attachmentUrl = genCtAttachmentLinkTrPO.getAttachmentUrl();
        // 合同编制的附件只有一个，这里取第一个
        if(Strings.isNotBlank(attachmentUrl)){
            JSONArray objects = com.alibaba.fastjson.JSON.parseArray(attachmentUrl);
            if(!objects.isEmpty()){
                genCtAttachmentLinkTrPO.setAttachmentUrl(objects.getString(0));
            }
        }
        return genCtAttachmentLinkTrPO;
    }

    private static GenCtAttachmentLinkTrDTO getGenCtAttachmentLinkTrDTO(GenCtAttachmentLinkTrPO attachmentLinkTrPO) {
        GenCtAttachmentLinkTrDTO genCtAttachmentLinkTrDTO = new GenCtAttachmentLinkTrDTO();
        genCtAttachmentLinkTrDTO.setId(attachmentLinkTrPO.getId());
        genCtAttachmentLinkTrDTO.setDocRef(attachmentLinkTrPO.getDocRef());
        genCtAttachmentLinkTrDTO.setSubDocRef(attachmentLinkTrPO.getSubDocRef());
        genCtAttachmentLinkTrDTO.setAttachmentType(attachmentLinkTrPO.getAttachmentType());
        genCtAttachmentLinkTrDTO.setAttachmentUrl(attachmentLinkTrPO.getAttachmentUrl()); // 一切ok
        genCtAttachmentLinkTrDTO.setExtWqStamped(attachmentLinkTrPO.getExtWqStamped());
        return genCtAttachmentLinkTrDTO;
    }

    private void updateContractRelatedOrder(GenCtHeadTrExtDTO ctHead) {
        // 合同关联订单
        LambdaQueryWrapper<GenCtOrderLinkTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GenCtOrderLinkTrPO::getDocRef, ctHead.getId());
        List<GenCtOrderLinkTrPO> originOrderList = genCtOrderLinkTrRepo.selectList(queryWrapper);
        List<GenCtOrderLinkTrPO> relOrders = Optional.ofNullable(ctHead.getExtWqCtOrderRef()).orElse(ImmutableList.of())
                .stream()
                .map(genCtOrderLinkTrConverter::dto2Po)
                .peek(ctOrder -> {
                    ctOrder.setId(null);
                    ctOrder.setDocRef(ctHead.getId());
                }).collect(Collectors.toList());
        // 处理行
        MdBillLineOperateUtils.processLine(genCtOrderLinkTrRepo, relOrders, originOrderList);
    }

    private void updateContractPartner(GenCtHeadTrExtDTO ctHead) {
        // 相关方
        LambdaQueryWrapper<GenCtPartnerLinkTrPO> partnerQueryWrapper = new LambdaQueryWrapper<>();
        partnerQueryWrapper.eq(GenCtPartnerLinkTrPO::getDocRef, ctHead.getId());
        List<GenCtPartnerLinkTrPO> originPartnerList = genCtPartnerLinkTrRepo.selectList(partnerQueryWrapper);

        List<GenCtPartnerLinkTrPO> partnerLinks = Optional.ofNullable(ctHead.getPartner()).orElse(ImmutableList.of())
                .stream()
                .map(genCtPartnerLinkTrConverter::dto2Po)
                .peek(partner -> {
                    partner.setId(null);
                    partner.setDocRef(ctHead.getId());
        }).collect(Collectors.toList());
        // 处理行
        MdBillLineOperateUtils.processLine(genCtPartnerLinkTrRepo, partnerLinks, originPartnerList);
    }

    private void updateContractAttachment(GenCtHeadTrExtDTO ctHead) {
        // 保存在数据里面的
        LambdaQueryWrapper<GenCtAttachmentLinkTrPO> attachQueryWrapper = new LambdaQueryWrapper<>();
        attachQueryWrapper.eq(GenCtAttachmentLinkTrPO::getDocRef, ctHead.getId());
        List<GenCtAttachmentLinkTrPO> originAttachList = genCtAttachmentLinkTrRepo.selectList(attachQueryWrapper);
        List<GenCtAttachmentLinkTrDTO> attachmentList = ctHead.getAttachment();
        // 查询合同类型信息
        Map<Long, GenAttachmentTypeCfPO> genAttachmentTypeCfPOMap = findGenAttachmentTypeCf(attachmentList);
        // 新插入的
        List<GenCtAttachmentLinkTrPO> currentAttachList = Optional.ofNullable(attachmentList).orElse(ImmutableList.of())
                .stream()
                .peek(dto -> {
                    // 处理是否签约
                    GenAttachmentTypeCfPO genAttachmentTypeCfPO = genAttachmentTypeCfPOMap.get(dto.getAttachmentType());
                    dto.setExtWqStamped(genAttachmentTypeCfPO.getExtWqStamped());
                })
                .map(genCtAttachmentLinkTrConverter::dto2Po).peek(attachment -> {
                    attachment.setId(null);
                    attachment.setDocRef(ctHead.getId());
                    // 处理是否签约
                    GenAttachmentTypeCfPO genAttachmentTypeCfPO = genAttachmentTypeCfPOMap.get(attachment.getAttachmentType());
                    attachment.setExtWqStamped(genAttachmentTypeCfPO.getExtWqStamped());
                }).collect(Collectors.toList());
        // 处理行
        MdBillLineOperateUtils.processLine(genCtAttachmentLinkTrRepo, currentAttachList, originAttachList);
    }

    public Map<Long, GenAttachmentTypeCfPO> findGenAttachmentTypeCf(List<GenCtAttachmentLinkTrDTO> attachmentList) {
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            // 获取文件类型ID
            List<Long> attachmentTypeIdList = attachmentList.stream().map(GenCtAttachmentLinkTrDTO::getAttachmentType).distinct().collect(Collectors.toList());
            // 查询文件类型信息
            List<GenAttachmentTypeCfPO> genAttachmentTypeCfPOS = genAttachmentTypeCfRepo.selectBatchIds(attachmentTypeIdList);
            return genAttachmentTypeCfPOS.stream().collect(Collectors.toMap(GenAttachmentTypeCfPO::getId, Function.identity()));
        }
        return new HashMap<>();
    }


    private void updateContractItem(GenCtHeadTrExtDTO ctHead) {
        List<CtItemTrDTO> items = ctHead.getCtItemList();
        List<CtItemTrPO> originItems = ctItemTrRepo.selectByCtHeadTrId(ctHead.getId());
        // 获取这次要插入的物料信息
        Map<Long, GenMatMdPO> genMatMdInfoMap = findGenMatMdInfoMap(items);
        List<CtItemTrPO> currentItems = Optional.ofNullable(items).orElse(ImmutableList.of())
                .stream()
                .peek(dto -> {
                    // 这个地方其实是物料ID
                    Long matId = dto.getMatCode();
                    // 获取物料信息
                    GenMatMdPO genMatMdPO = genMatMdInfoMap.get(matId);
                    if (Objects.isNull(genMatMdPO)) {
                        throw new BusinessException("物料信息不存在");
                    }
                    // 物料的技术附件
                    dto.setExtWqAttachment(genMatMdPO.getExtWqAttachment());
                })
                .map(ctItemTrConverter::dto2Po)
                .peek(ctItem -> {
                    ctItem.setCtItemCode(ctBaseService.generateModelCode(CtItemTrPO.class));
                    ctItem.setGenCtHeadTrId(ctHead.getId());
                })
                .collect(Collectors.toList());
        // 处理行
        MdBillLineOperateUtils.processLine(ctItemTrRepo, currentItems, originItems);
    }

    private void updateArchiveNo(GenCtHeadTrExtDTO ctHead, CtHeadTrPO theOldCtHead) {
        final Long id = ctHead.getId();

        Map<String, Object> extra = theOldCtHead.getExtra();
        if(extra != null && Boolean.TRUE.equals(extra.get(GenCtHeadTrExtDTO.Fields.extWqArchived))){
            throw new BusinessException("合同已归档");
        }

        CtHeadTrPO ctHeadPOToUpdate = new CtHeadTrPO();
        ctHeadPOToUpdate.setId(id);
        extra.put(GenCtHeadTrExtDTO.Fields.extWqArchived, true); //设置已归档
        extra.put(GenCtHeadTrExtDTO.Fields.extWqArchiveNo, ctHead.getExtWqArchiveNo());
        extra.put(GenCtHeadTrExtDTO.Fields.extWqOriginal, ctHead.getExtWqOriginal());
        ctHeadPOToUpdate.setExtra(extra);
        ctHeadPOToUpdate.setSignDate(ctHead.getSignDate());
        ctHeadTrRepo.updateById(ctHeadPOToUpdate);
    }

    public CtHeadTrDTO queryFullCtById(Long ctHeadId) {
        CtHeadTrDTO ctHeadTrDTO = ctHeadTrConverter.po2Dto(ctHeadTrRepo.selectById(ctHeadId));
        List<CtItemTrPO> items = ctItemTrRepo.selectByCtHeadTrId(ctHeadId);
        ctHeadTrDTO.setCtItemList(ctItemTrConverter.po2DtoList(items));
        return ctHeadTrDTO;
    }

    public GenCtHeadTrExtDTO queryCtById(Long ctHeadId) {
        return genCtHeadTrExtConverter.convert(ctHeadTrRepo.selectById(ctHeadId));
    }

    public CtHeadTrDTO pushCtInvalidMsg(CtHeadTrDTO request) {
        CtHeadTrDTO ctHeadTrDTO = queryFullCtById(request.getId());
        contractProducer.sendMsgOnConInvalid(ctHeadTrDTO);
        return request;
    }

    public void submitCompletedCheck(CtHeadTrDTO request) {
        checkSubmitCtHead(request);
        checkSubmitCtItem(request.getCtItemList());
    }

    private void checkSubmitCtHead(CtHeadTrDTO request) {
        if (!StringUtils.hasText(request.getCtName())) {
            throw new BusinessException(CT_H_NAME_IS_EMPTY);
        }
        if (Objects.isNull(request.getCtType())) {
            throw new BusinessException(CT_H_TYPE_IS_EMPTY);
        }
        if (Objects.isNull(request.getComOrgId())) {
            throw new BusinessException(CT_H_COM_ORG_ID_IS_EMPTY);
        }
        if (Objects.isNull(request.getPurOrgCodeManagement())) {
            throw new BusinessException(CT_H_PUR_ORG_CODE_MANAGEMENT_IS_EMPTY);
        }
        if (Objects.isNull(request.getPurOrgCodeApplicable())) {
            throw new BusinessException(CT_H_PUR_ORG_CODE_APPLICABLE_IS_EMPTY);
        }
        if (Objects.isNull(request.getPrtnA())) {
            throw new BusinessException(CT_H_PRTN_A_IS_EMPTY);
        }
        if (Objects.isNull(request.getPrtnB())) {
            throw new BusinessException(CT_H_PRTN_B_IS_EMPTY);
        }
        if (Objects.isNull(request.getCtDateSign())) {
            throw new BusinessException(CT_H_CT_DATE_SIGN_IS_EMPTY);
        }
        if (Objects.isNull(request.getCtDateFrom())) {
            throw new BusinessException(CT_H_CT_DATE_FROM_IS_EMPTY);
        }
        if (Objects.isNull(request.getCtDateTo())) {
            throw new BusinessException(CT_H_CT_DATE_TO_IS_EMPTY);
        }
        if (Objects.isNull(request.getTaxRate())) {
            throw new BusinessException(CT_H_CT_TAX_RATE_IS_EMPTY);
        }
        if (Objects.isNull(request.getCurrency())) {
            throw new BusinessException(CT_H_CT_CURRENCY_IS_EMPTY);
        }
        if (Objects.isNull(request.getSignatory())) {
            throw new BusinessException(CT_H_CT_SIGNATORY_IS_EMPTY);
        }
        if (Objects.isNull(request.getSettCycleType())) {
            throw new BusinessException(CT_H_CT_SETT_CYCLE_TYPE_IS_EMPTY);
        }
        if (Objects.isNull(request.getSettDateFirst())) {
            throw new BusinessException(CT_H_CT_SETT_DATE_FIRST_IS_EMPTY);
        }
    }

    private void checkSubmitCtItem(List<CtItemTrDTO> ctItemList) {
        if (CollectionUtils.isEmpty(ctItemList)) {
            throw new BusinessException(CT_H_ITEM_LIST_IS_EMPTY);
        }
        checkCtItemDuplicateMaterial(ctItemList);
        for (CtItemTrDTO ctItemTrDTO : ctItemList) {
            if (Objects.isNull(ctItemTrDTO.getMatCode())) {
                throw new BusinessException(CT_I_MAT_IS_EMPTY);
            }
            if (Objects.isNull(ctItemTrDTO.getCtMatAmt())) {
                throw new BusinessException(CT_I_MAT_AMT_IS_EMPTY);
            }
            if (Objects.isNull(ctItemTrDTO.getTaxRate())) {
                throw new BusinessException(CT_I_TAX_RATE_IS_EMPTY);
            }
        }
    }

    public ConCtSigntaskTrDTO queryConTaskInfo(ConContractSignwayDTO request) {
        if (Objects.isNull(request) || StringUtil.isEmpty(request.getContract()) || StringUtil.isEmpty(request.getSignWay())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_PARAM_IS_NULL);
        }
        CtHeadTrPO ctHeadTrPO = ctHeadTrRepo.selectById(Long.valueOf(request.getContract()));
        if (Objects.isNull(ctHeadTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_CONTRACT_IS_NULL);
        }
        ConCtSignTypeConfPO conCtSignTypeConfPO = conCtSignTypeConfRepo.selectById(Long.valueOf(request.getSignWay()));
        if (Objects.isNull(conCtSignTypeConfPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNWAY_IS_NULL);
        }
        CtHeadTrDTO ctHeadTrDTO = ctHeadTrConverter.po2Dto(ctHeadTrPO);
        ConCtSignTypeConfDTO conCtSignTypeConfDTO = conCtSignTypeConfConverter.po2Dto(conCtSignTypeConfPO);
        ConCtSigntaskTrDTO signtaskTrDTO = new ConCtSigntaskTrDTO();
        signtaskTrDTO.setSignWay(conCtSignTypeConfDTO.getId());
        List<ConCtSignatoryTrDTO> conCtSignatoryTrDTOS = new ArrayList<>();
        // 甲方签署
        if (conCtSignTypeConfDTO.getPartyASign()) {
            ConCtSignatoryTrDTO conCtSignatoryTrDTO = new ConCtSignatoryTrDTO();
            GenComTypeCfPO genComTypeCfPO = genComTypeCfRepo.selectById(ctHeadTrDTO.getPrtnA());
            GenComTypeCfDTO genComTypeCfDTO = genComTypeCfConverter.po2Dto(genComTypeCfPO);
            conCtSignatoryTrDTO.setSignatory(genComTypeCfDTO);
            conCtSignatoryTrDTO.setSortNum(2);
            // 甲方先签，或者乙方不签的时候，甲方签署人状态为待签署
            if (conCtSignTypeConfDTO.getPartyAFirst() || !conCtSignTypeConfDTO.getPartyBSign()) {
                conCtSignatoryTrDTO.setSortNum(1);
            }
            conCtSignatoryTrDTOS.add(conCtSignatoryTrDTO);
        }
        // 乙方签署
        if (conCtSignTypeConfDTO.getPartyBSign()) {
            ConCtSignatoryTrDTO conCtSignatoryTrDTO = new ConCtSignatoryTrDTO();
            // 供应商主数据，转换成公司信息
            GenVendInfoMdPO genVendInfoMdPO = MD.queryById(ctHeadTrDTO.getPrtnB(), GenVendInfoMdPO.class);
            GenComTypeCfPO genComTypeCfPO = MD.queryById(genVendInfoMdPO.getPurComId(), GenComTypeCfPO.class);
            GenComTypeCfDTO genComTypeCfDTO = genComTypeCfConverter.po2Dto(genComTypeCfPO);
            conCtSignatoryTrDTO.setSignatory(genComTypeCfDTO);
            conCtSignatoryTrDTO.setSortNum(2);
            conCtSignatoryTrDTO.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.NOT_DISPLAY);
            // 乙方先签，或者甲方不签的时候，乙方签署人状态为待签署
            if (!conCtSignTypeConfDTO.getPartyAFirst() || !conCtSignTypeConfDTO.getPartyASign()) {
                conCtSignatoryTrDTO.setSortNum(1);
            }
            conCtSignatoryTrDTOS.add(conCtSignatoryTrDTO);
        }
        signtaskTrDTO.setSignatories(conCtSignatoryTrDTOS);
        return signtaskTrDTO;
    }

    public ConCtSigntaskTrDTO updateSignatoryAndSignTime(ConCtSigntaskTrDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getSignBillId())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_PARAM_IS_NULL);
        }
        User currentUser = TrantorContext.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_USER_IS_NULL);
        }
        EmployeeQueryDTO employeeQueryDTO = new EmployeeQueryDTO();
        employeeQueryDTO.setUserId(currentUser.getId());
        Response<EmployeeDTO> employeeDTOResponse = employeeReadFacade.queryEmployeeByUserId(employeeQueryDTO);
        if (!employeeDTOResponse.isSuccess()) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_EMPLOYEE_IS_NULL);
        }
        CtHeadTrPO ctHeadTrPO = new CtHeadTrPO();
        ctHeadTrPO.setId(request.getSignBillId());
        ctHeadTrPO.setSignatory(Objects.nonNull(employeeDTOResponse.getData()) ? employeeDTOResponse.getData().getId() : null);
        ctHeadTrPO.setCtDateSign(new Date());
        ctHeadTrPO.setCtStatus(CtHeadStatusDict.SIGNING);
        ctHeadTrRepo.updateById(ctHeadTrPO);
        return request;
    }


    /**
     * 更新合同状态
     *
     * @param ctId
     * @param ctStatus
     */
    public void updateContractStatus(long ctId, String ctStatus) {
        LambdaUpdateWrapper<CtHeadTrPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CtHeadTrPO::getId, ctId);
        wrapper.set(CtHeadTrPO::getCtStatus, ctStatus);
        wrapper.set(CtHeadTrPO::getUpdatedAt, LocalDateTime.now());
        ctHeadTrRepo.update(null, wrapper);
    }

    public void addContractFile(long ctId, Long taskId) {
        LambdaQueryWrapper<ConCtSignfileTrPO> fileQuery = new LambdaQueryWrapper<>();
        fileQuery.eq(ConCtSignfileTrPO::getSignTask, taskId);
        List<ConCtSignfileTrPO> conCtSignfileTrPOS = conCtSignfileTrRepo.selectList(fileQuery);
        log.info("conCtSignfileTrPOS={}",JSON.toJSONString(conCtSignfileTrPOS));
        if (CollectionUtils.isEmpty(conCtSignfileTrPOS)) {
            log.warn("conCtSignfileTrPOS is empty");
            return ;
        }
        LambdaQueryWrapper<CtHeadTrPO> ctHeadQuery = new LambdaQueryWrapper<>();
        ctHeadQuery.eq(CtHeadTrPO::getId, ctId);
        CtHeadTrPO ctHeadTrPO = ctHeadTrRepo.selectOne(ctHeadQuery);

        GenAttachmentTypeCfPO genAttachmentTypeCfPO = getTheSignedFileAttachmentType();
        if (genAttachmentTypeCfPO == null) {
            log.warn("genAttachmentTypeCfPO is null");
            return ;
        }
        // insert已签署文件
        List<String> signFileUrlList = new ArrayList<>();
        GenCtAttachmentLinkTrPO genCtAttachmentLinkTrPO = new GenCtAttachmentLinkTrPO();
        genCtAttachmentLinkTrPO.setDocRef(ctHeadTrPO.getId());
        genCtAttachmentLinkTrPO.setAttachmentType(genAttachmentTypeCfPO.getId());
        for (ConCtSignfileTrPO signFilePo : conCtSignfileTrPOS) {
            signFileUrlList.add(signFilePo.getSignedFile());
        }
        genCtAttachmentLinkTrPO.setAttachmentUrl(JSON.toJSONString(signFileUrlList)); // 一切ok

        // 获取原始的文件信息
        LambdaQueryWrapper<GenCtAttachmentLinkTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GenCtAttachmentLinkTrPO::getDocRef, ctHeadTrPO.getId());
        List<GenCtAttachmentLinkTrPO> genCtAttachmentLinkTrPOS = genCtAttachmentLinkTrRepo.selectList(wrapper);
        for (GenCtAttachmentLinkTrPO ctAttachmentLinkTrPO : genCtAttachmentLinkTrPOS) {
            Long attachmentType = ctAttachmentLinkTrPO.getAttachmentType();
            if (Objects.equals(attachmentType, genCtAttachmentLinkTrPO.getAttachmentType())) {
                genCtAttachmentLinkTrPO.setId(ctAttachmentLinkTrPO.getId());
                genCtAttachmentLinkTrPO.setCreatedBy(ctAttachmentLinkTrPO.getCreatedBy());
                genCtAttachmentLinkTrPO.setCreatedAt(ctAttachmentLinkTrPO.getCreatedAt());
            }
        }

        if (Objects.isNull(genCtAttachmentLinkTrPO.getId())) {
            genCtAttachmentLinkTrRepo.insert(genCtAttachmentLinkTrPO);
        } else {
            genCtAttachmentLinkTrRepo.updateById(genCtAttachmentLinkTrPO);
        }
    }


    /**
     * 删除已签署文件
     *
     * 在已签署完成后，重新发起签署或者编制，需要把已签署文件给逻辑删除掉
     * @param ctId
     */
    public void deleteTheSignedFile(long ctId) {

        GenAttachmentTypeCfPO genAttachmentTypeCfPO = getTheSignedFileAttachmentType();
        if (genAttachmentTypeCfPO == null) return;

        LambdaQueryWrapper<GenCtAttachmentLinkTrPO> attachQuery = new LambdaQueryWrapper<>();
        attachQuery.eq(GenCtAttachmentLinkTrPO::getDocRef, ctId);
        attachQuery.eq(GenCtAttachmentLinkTrPO::getAttachmentType, genAttachmentTypeCfPO.getId());
        List<GenCtAttachmentLinkTrPO> genCtAttachmentLinkTrPOS = genCtAttachmentLinkTrRepo.selectList(attachQuery);
        for (GenCtAttachmentLinkTrPO genCtAttachmentLinkTrPO : genCtAttachmentLinkTrPOS) {
            genCtAttachmentLinkTrPO.setAttachmentUrl("[]");
            genCtAttachmentLinkTrRepo.updateById(genCtAttachmentLinkTrPO);
        }

    }

    /**
     * 删除编制生成的文件
     *
     * 在已签署完成后，重新发起签署或者编制，需要把已签署文件给逻辑删除掉
     * @param ctId
     */
    public void deleteTheDraftedFile(long ctId) {

        GenAttachmentTypeCfPO genAttachmentTypeCfPO = getFtype0006().orElse(null);
        if (genAttachmentTypeCfPO == null) return;

        LambdaQueryWrapper<GenCtAttachmentLinkTrPO> attachQuery = new LambdaQueryWrapper<>();
        attachQuery.eq(GenCtAttachmentLinkTrPO::getDocRef, ctId);
        attachQuery.eq(GenCtAttachmentLinkTrPO::getAttachmentType, genAttachmentTypeCfPO.getId());
        List<GenCtAttachmentLinkTrPO> genCtAttachmentLinkTrPOS = genCtAttachmentLinkTrRepo.selectList(attachQuery);
        for (GenCtAttachmentLinkTrPO genCtAttachmentLinkTrPO : genCtAttachmentLinkTrPOS) {
            genCtAttachmentLinkTrPO.setAttachmentUrl("[]");
            genCtAttachmentLinkTrRepo.updateById(genCtAttachmentLinkTrPO);
        }
    }



    /**
     * 获取"已签署文件类型"
     * @return
     */
    private GenAttachmentTypeCfPO getTheSignedFileAttachmentType() {
        List<GenAttachmentTypeCfPO> genAttachmentTypeCfPOS = genAttachmentTypeCfRepo.selectList(new LambdaQueryWrapper<>());
        Optional<GenAttachmentTypeCfPO> signedType = genAttachmentTypeCfPOS.stream().filter(v -> v.getAttachmentName().equals("已签署文件")).findFirst();
        return signedType.orElse(null);
    }

    private void checkCtItemDuplicateMaterial(List<CtItemTrDTO> ctItemList) {
        if (CollectionUtils.isEmpty(ctItemList)) {
            return;
        }
        for (CtItemTrDTO ctItemTrDTO : ctItemList) {
            Long matId = ctItemTrDTO.getMatCode();
            if (Objects.isNull(matId)) {
                throw new BusinessException("物料编码ID为空");
            }
        }
    }

    public void updateContractStatus(Long ctId, String ctStatus) {
        CtHeadTrPO tsrmConBdCtHPO = new CtHeadTrPO();
        tsrmConBdCtHPO.setId(ctId);
        tsrmConBdCtHPO.setCtStatus(ctStatus);
        LambdaUpdateWrapper<CtHeadTrPO> tsrmConBdCtHPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        tsrmConBdCtHPOLambdaUpdateWrapper.eq(CtHeadTrPO::getId, ctId);
        tsrmConBdCtHPOLambdaUpdateWrapper.set(CtHeadTrPO::getCtStatus, ctStatus);
        ctHeadTrRepo.update(tsrmConBdCtHPO, tsrmConBdCtHPOLambdaUpdateWrapper);
    }

    public void fillCtAttachmentLink(Map<String,Object> request) {
        LambdaQueryWrapper<GenAttachmentTypeCfPO> queryType = new LambdaQueryWrapper<>();
        queryType.in(GenAttachmentTypeCfPO::getAttachmentCode, Arrays.asList(CT_ADMIN_LINK_ATTACHMENT_CODE));
        List<GenAttachmentTypeCfPO> typeCfPOS = attachmentTypeCfRepo.selectList(queryType);
        if (CollUtil.isNotEmpty(typeCfPOS)){
            JSONObject ctObj = JSONUtil.parseObj(request);
            List<Map> attachment = JSONUtil.toList(ctObj.getJSONArray("attachment"), Map.class);
            if (CollectionUtils.isNotEmpty(attachment)){
                return;
            } else {
                // 这个地方其实可能是null 所以我们要给他设置一下
                attachment = Lists.newArrayList();
            }
            for (GenAttachmentTypeCfPO typeCfPO : typeCfPOS) {
                Map<String,Object> linkDTO = new HashMap<>();
                linkDTO.put("attachmentType",typeCfPO);
                attachment.add(linkDTO);
            }
            request.put("attachment",attachment);

        }
    }

    /**
     * 检查文件类型是否重复
     * 检查文件是否被他人改动过
     * @param request 文件列表
     */
    public void checkAttachmentDuplicateType(CtHeadTrDTO request){
        List<GenCtAttachmentLinkTrDTO> attachmentLinkList = request.getAttachment();
        if (Objects.isNull(attachmentLinkList)){
            attachmentLinkList = new ArrayList<>();
        }
        Map<Long, Long> typeCountMap = attachmentLinkList.stream().collect(Collectors.groupingBy(GenCtAttachmentLinkTrDTO::getAttachmentType, Collectors.counting()));
        boolean anyMatch = typeCountMap.entrySet().stream().anyMatch(ent -> ent.getValue() > 1);
        if (anyMatch){
            throw new BusinessException("出现重复的合同文件类型，请修改后重试！");
        }
    }

    /**
     * 生成临时访问的文件链接
     * @param request 请求参数
     * @return 返回参数
     */
    public GenCtHeadTrExtDTO generateTemporaryLinks(GenCtHeadTrExtDTO request) {
        // 获取原始的链接
        if (Objects.nonNull(request) && Objects.nonNull(request.getExtWqDraftFile())) {
            String oriUrl = FileHelper.parseSignedUrl(request.getExtWqDraftFile());
            String fileUrl = ossService.getFileUrl(oriUrl, true);
            request.setExtWqDraftFile(fileUrl);
            return request;
        }
        return request;
    }

    /**
     * 1. 通过Trantor审批组件SDK，根据审批流组编码和合同ID，查询审批流信息；
     * 2. 通过审批流中的审批任务，过滤包含特定关键字的任务，识别其审批节点名称和审批人。注意处理审批拒绝后再次发起审批的情况，过滤审批通过的节点。
     * 3. 获取审批人的签名，注意审批人是员工，查询签名图片要确认是用用户关联还是用员工关联。
     * 4. 调用PDF编辑接口，追加签名图片。
     * @param request
     */
    public void appendSignImage(ConAppendSingImgContextDTO request) {

        long ctId = request.getContractId();
        final GenCtHeadTrExtDTO contract = queryCtById(ctId);

        LinkedList<PDFUtil.SignatureImage> signImages = buildSignImages(request, contract);
        GenCtAttachmentLinkTrPO theDraftedContractAttachment = getTheDraftedContractAttachment(ctId);

        // 暂定：只给编制生成的合同追加签名
        List<GenCtAttachmentLinkTrPO> contractFiles = Lists.newArrayList(theDraftedContractAttachment);

        List<GenCtAttachmentLinkTrPO> genAttachmentList = ctBaseService.appendSignImage(contractFiles, signImages);
        for (GenCtAttachmentLinkTrPO genCtAttachmentLinkTrPO : genAttachmentList) {
            genCtAttachmentLinkTrRepo.updateById(genCtAttachmentLinkTrPO);
        }

        CtOssUtil.deleteFiles(signImages.stream().map(PDFUtil.SignatureImage::getImageFile).collect(Collectors.toList()));
    }

    /**
     * todo 未完成
     *
     * 通过审批SDK获取需要追加的审批人的签名图片：
     * 按照编制人、审核人、审批人的顺序查询签名图片
     * @param appendSingImgContext
     * @return
     */
    private LinkedList<PDFUtil.SignatureImage> buildSignImages(ConAppendSingImgContextDTO appendSingImgContext, GenCtHeadTrExtDTO contract ) {

        final LinkedList<PDFUtil.SignatureImage> list = Lists.newLinkedList();

        final long orgPurOrgCfId = appendSingImgContext.getOrgPurOrgCfId();
        // 最新需求调整改为合同发起人id
        OrgEmployeeMdPO empByUser = orgEmployeeMdRepo.getEmpByUser(contract.getCreatedBy());
        Assert.notNull(empByUser,()->new BusinessException("合同创建人对应员工不存在"));
        final long drafterEmployeeId = empByUser.getId();

        list.add(buildSignImg("经办", drafterEmployeeId, orgPurOrgCfId));

        // 审核人ID
        final List<Long> auditorEmployeeIds = getAuditors(appendSingImgContext);

        log.info("response auditorEmployeeIds: {}", auditorEmployeeIds);

        // 审批人； 因为是在审批回掉中调用此逻辑，这时候调用审批的审批任务接口是获取不到最后一个审批任务的审批人的，对应的审批任务状态是待审批，审批人为空。
        // 需要从当前的上线文中获取最后一个节点的审批人
        long approvedEmployeeId = getEmployeeId(TrantorContext.getCurrentUserId());

        for (Long auditorEmployeeId : auditorEmployeeIds) {
            list.add(buildSignImg("审核", auditorEmployeeId, orgPurOrgCfId));
        }
        /**
         * 最新需求 合同审批通过后 去掉审批人签名
         */
       // list.add(buildSignImg("审批", approvedEmployeeId, orgPurOrgCfId));
        return list;
    }

    /**
     * 查询合同编制审批流中的审批人
     * @param appendSingImgContext
     * @return
     */
    private List<Long> getAuditors(ConAppendSingImgContextDTO appendSingImgContext) {
        List<Long> auditors = Lists.newArrayList();
        QueryWorkflowInstanceByGroupRequest request = new QueryWorkflowInstanceByGroupRequest();
        request.setUserId("1");
        request.setBizId(String.valueOf(appendSingImgContext.getContractId()));
        request.setWorkflowGroupKey("CONTRACT_2B$CT_DRAFT_FLOW");
        QueryWorkflowInstanceByGroupResponse response = workflow.execute(request);
        for (WorkflowTaskInstanceDTO taskInstance : response.getWorkflowInstance().getTaskInstances()) {
            if("ApprovalNode".equals(taskInstance.getTaskNodeType()) && Boolean.TRUE.equals(taskInstance.getAuditResult()) && taskInstance.getDescription().contains("签名")){
                UserInfo taskCompleteUser = taskInstance.getTaskCompleteUser();
                if(taskCompleteUser != null){
                    long employeeId = getEmployeeId(Long.parseLong(taskCompleteUser.getId()));
                    auditors.add(employeeId);
                }
            }
        }
        return auditors;
    }

    private long getEmployeeId(Long extWqCtDrafter) {
        final OrgEmployeeMdPO empByUser = orgEmployeeMdRepo.getEmpByUser(extWqCtDrafter);
        return empByUser.getId();
    }

    /**
     * 实时从员工表中查询签名图片
     * @param name
     * @param drafterEmployeeId
     * @param purOrgCodeManagement
     * @return
     */
    private PDFUtil.SignatureImage buildSignImg(String name, long drafterEmployeeId, long purOrgCodeManagement) {
        PDFUtil.SignatureImage image = new PDFUtil.SignatureImage();
        image.setName(name);
        ConCtSignImgCfPO signImg = conCtSignImgCfRepo.getSignImageUrl(drafterEmployeeId, purOrgCodeManagement);
        if (signImg == null || signImg.getSignEmployee() == null) {
            return image;
        }
        OrgEmployeeMdPO employee = MD.queryById(signImg.getSignEmployee(), OrgEmployeeMdPO.class);
        if (employee == null) {
            return image;
        }
        //员工签名图片路径
        Object personalSignaturePath = employee.getExtra().get("extWqSignature");
        if (personalSignaturePath instanceof String) {
            image.setImageUrl((String) personalSignaturePath);
        }
        return image;
    }

    /**
     * 合同确认签署文件
     * @param request 请求参数
     */
    public void conSignAffirmAction(GenCtHeadTrExtDTO request) {
        if (Objects.isNull(request)) {
            throw new BusinessException("请求参数为空");
        }
        Long conId = request.getId();
        if (Objects.isNull(conId)) {
            throw new BusinessException("请求参数ID为空");
        }
        // 合同信息
        CtHeadTrPO ctHeadTrPO = ctHeadTrRepo.selectById(conId);
        if (!Objects.equals(ctHeadTrPO.getCtStatus(), CtHeadStatusDict.SIGN_CONFORM)) {
            throw new BusinessException("该合同状态不为签署确认，无法进行确认");
        }
        ctHeadTrPO.setCtStatus(CtHeadStatusDict.SIGNED);
        ctHeadTrRepo.updateById(ctHeadTrPO);
    }

    /**
     * 合同重新签署 状态变更 & 校验签署任务状态
     * @param request 请求参数
     */
    public void conAgainSubmitStatusChangeAction(GenCtHeadTrExtDTO request) {
        // 查询合同信息 判断当前状态是不是 待签署、签署中、签署确认
        Long ctId = request.getId();
        CtHeadTrPO ctHeadTrPO = ctHeadTrRepo.selectById(ctId);
        if (Objects.isNull(ctHeadTrPO)) {
            throw new BusinessException("合同不存在，请检查该数据是否被删除");
        }
        List<String> statusList = Lists.newArrayList(CtHeadStatusDict.SIGNING, CtHeadStatusDict.AWAITING, CtHeadStatusDict.SIGN_CONFORM);
        if (!statusList.contains(ctHeadTrPO.getCtStatus())) {
            throw new BusinessException("当前状态无法进行重新编制，请刷新页面尝试获取最新数据");
        }
        // 查询合同关联的签署任务
        List<ConCtSigntaskTrPO> conCtSigntaskTrPOList = conCtSigntaskTrRepo.findConCtSigntaskTrByCtConId(request.getId());
        for (ConCtSigntaskTrPO conCtSigntaskTrPO : conCtSigntaskTrPOList) {
            conCtSigntaskTrPO.setSignStatus(ConCtSigntaskTrSignStatusDict.CLOSED);
            conCtSigntaskTrRepo.updateById(conCtSigntaskTrPO);
            // 更新签署人记录表状态
            LambdaQueryWrapper<ConCtSignatoryTrPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ConCtSignatoryTrPO::getSignTask, conCtSigntaskTrPO.getId());
            List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrRepo.selectList(queryWrapper);
            for (ConCtSignatoryTrPO conCtSignatoryTrPO : conCtSignatoryTrPOS) {
                conCtSignatoryTrPO.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.CLOSED);
                conCtSignatoryTrRepo.updateById(conCtSignatoryTrPO);
            }
        }
        ctHeadTrPO.setCtStatus(CtHeadStatusDict.DRAFTING);
        ctHeadTrRepo.updateById(ctHeadTrPO);
    }

}
