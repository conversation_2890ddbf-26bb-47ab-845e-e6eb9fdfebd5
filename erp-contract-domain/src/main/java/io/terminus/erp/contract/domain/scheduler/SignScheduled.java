package io.terminus.erp.contract.domain.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSignRemindRecordTrRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSignatoryTrRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSigntaskTrRepo;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryTrSignatoryStatusDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryTrSignatoryTypeDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSigntaskTrSignBillTypeDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSigntaskTrSignStatusDict;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignRemindRecordTrPO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignatoryTrPO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSigntaskTrPO;
import io.terminus.notice.sdk.req.task.NoticeTaskCreateReq1;
import io.terminus.notice.sdk.service.NoticeService;
import io.terminus.notice.spi.model.res.NoticeTarget;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 签署提醒定时任务
 * <p>
 * 功能：
 * 1. 每日定时扫描状态=签署中且乙方=待签署的签署任务
 * 2. 根据甲方签署时间判断提醒频率：
 * - 若当前日期-甲方签署时间<=7天，则每2天发送一次短信
 * - 若当前日期-甲方签署时间>7天，则每天发送短信催促签署
 * 3. 发送短信内容："您的瑞信数采合同$合同编号$需签署，请及时签署。"
 * 4. 记录签署提醒记录到数据库
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SignScheduled {

    private final ConCtSigntaskTrRepo conCtSigntaskTrRepo;
    private final ConCtSignatoryTrRepo conCtSignatoryTrRepo;
    private final ConCtSignRemindRecordTrRepo conCtSignRemindRecordTrRepo;
    private final NoticeService noticeService;
    private final UserService userService;
    private final StringRedisTemplate stringRedisTemplate;

    // 提醒类型常量
    private static final int REMIND_TYPE_EVERY_2_DAYS = 1; // 每2天提醒
    private static final int REMIND_TYPE_EVERY_DAY = 2; // 每天提醒
    private static final int DAYS_THRESHOLD = 7; // 7天阈值

    /**
     * 每小时执行一次签署提醒定时任务
     * cron表达式：0 0 * * * ? 表示每小时的第0分钟执行
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void signRemind() {
        log.info("开始执行签署提醒定时任务");
        Boolean result = stringRedisTemplate.opsForValue().setIfAbsent("SIGN_REMIND_LOCK", "1", 59, TimeUnit.SECONDS);
        if (Boolean.FALSE.equals(result)) {
            log.info("[签署提醒] 获取分布式锁失败，任务已在执行中，跳过本次调度");
            return;
        }
        try {
            // 1. 扫描状态=签署中且乙方=待签署的签署任务
            List<ConCtSigntaskTrPO> signingTasks = findSigningTasksWithWaitingPartyB();

            if (CollectionUtils.isEmpty(signingTasks)) {
                log.info("没有找到需要提醒的签署任务");
                return;
            }

            log.info("找到{}个需要提醒的签署任务", signingTasks.size());

            for (ConCtSigntaskTrPO signTask : signingTasks) {
                try {
                    processSignTaskRemind(signTask);
                } catch (Exception e) {
                    log.error("处理签署任务提醒失败，任务ID: {}, 错误: {}", signTask.getId(), e.getMessage(), e);
                }
            }

            log.info("签署提醒定时任务执行完成");
        } catch (Exception e) {
            log.error("签署提醒定时任务执行失败", e);
        }
    }

    /**
     * 查找状态=签署中且乙方=待签署的签署任务
     */
    private List<ConCtSigntaskTrPO> findSigningTasksWithWaitingPartyB() {
        try {
            // 查询状态=签署中的签署任务
            LambdaQueryWrapper<ConCtSigntaskTrPO> taskWrapper = new LambdaQueryWrapper<>();
            taskWrapper.eq(ConCtSigntaskTrPO::getSignStatus, ConCtSigntaskTrSignStatusDict.SIGNING);
            taskWrapper.eq(ConCtSigntaskTrPO::getSignBillType, ConCtSigntaskTrSignBillTypeDict.CONTRACT);

            List<ConCtSigntaskTrPO> signingTasks = conCtSigntaskTrRepo.selectList(taskWrapper);

            if (CollectionUtils.isEmpty(signingTasks)) {
                return new ArrayList<>();
            }

            // 过滤出有乙方待签署的任务
            List<ConCtSigntaskTrPO> result = new ArrayList<>();
            for (ConCtSigntaskTrPO task : signingTasks) {
                if (hasWaitingPartyB(task.getId())) {
                    result.add(task);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("查询签署任务失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查是否有乙方待签署
     */
    private boolean hasWaitingPartyB(Long signTaskId) {
        try {
            LambdaQueryWrapper<ConCtSignatoryTrPO> signatoryWrapper = new LambdaQueryWrapper<>();
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignTask, signTaskId);
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignatoryType, ConCtSignatoryTrSignatoryTypeDict.PARTYB);
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignatoryStatus, ConCtSignatoryTrSignatoryStatusDict.WAITING);

            return conCtSignatoryTrRepo.selectCount(signatoryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查乙方待签署状态失败，签署任务ID: {}", signTaskId, e);
            return false;
        }
    }

    /**
     * 处理单个签署任务的提醒
     */
    private void processSignTaskRemind(ConCtSigntaskTrPO signTask) {
        // 获取甲方签署时间
        LocalDateTime partyASignTime = getPartyASignTime(signTask.getId());
        if (partyASignTime == null) {
            log.warn("签署任务{}没有找到甲方签署时间，跳过提醒", signTask.getId());
            return;
        }

        // 计算距离甲方签署的天数
        long daysSincePartyASign = ChronoUnit.DAYS.between(partyASignTime.toLocalDate(), LocalDate.now());

        // 获取乙方签署方信息
        List<ConCtSignatoryTrPO> partyBSignatories = getPartyBSignatories(signTask.getId());
        if (CollectionUtils.isEmpty(partyBSignatories)) {
            log.warn("签署任务{}没有找到乙方签署方，跳过提醒", signTask.getId());
            return;
        }

        // 确定提醒类型
        int remindType = determineRemindType(daysSincePartyASign);

        // 检查是否需要发送提醒
        if (shouldSendRemind(signTask.getId(), partyBSignatories, remindType)) {
            // 发送短信提醒
            sendSmsRemind(signTask, partyBSignatories, remindType, daysSincePartyASign);
        }
    }

    /**
     * 获取甲方签署时间
     */
    private LocalDateTime getPartyASignTime(Long signTaskId) {
        try {
            LambdaQueryWrapper<ConCtSignatoryTrPO> signatoryWrapper = new LambdaQueryWrapper<>();
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignTask, signTaskId);
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignatoryType, ConCtSignatoryTrSignatoryTypeDict.PARTYA);
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignatoryStatus, ConCtSignatoryTrSignatoryStatusDict.SIGNED);
            signatoryWrapper.orderByDesc(ConCtSignatoryTrPO::getSignTime);
            signatoryWrapper.last("limit 1");

            ConCtSignatoryTrPO partyASignatory = conCtSignatoryTrRepo.selectOne(signatoryWrapper);
            return partyASignatory != null ? partyASignatory.getSignTime() : null;
        } catch (Exception e) {
            log.error("获取甲方签署时间失败，签署任务ID: {}", signTaskId, e);
            return null;
        }
    }

    /**
     * 获取乙方签署方信息
     */
    private List<ConCtSignatoryTrPO> getPartyBSignatories(Long signTaskId) {
        try {
            LambdaQueryWrapper<ConCtSignatoryTrPO> signatoryWrapper = new LambdaQueryWrapper<>();
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignTask, signTaskId);
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignatoryType, ConCtSignatoryTrSignatoryTypeDict.PARTYB);
            signatoryWrapper.eq(ConCtSignatoryTrPO::getSignatoryStatus, ConCtSignatoryTrSignatoryStatusDict.WAITING);

            return conCtSignatoryTrRepo.selectList(signatoryWrapper);
        } catch (Exception e) {
            log.error("获取乙方签署方信息失败，签署任务ID: {}", signTaskId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 确定提醒类型
     */
    private int determineRemindType(long daysSincePartyASign) {
        if (daysSincePartyASign <= DAYS_THRESHOLD) {
            return REMIND_TYPE_EVERY_2_DAYS; // 每2天提醒
        } else {
            return REMIND_TYPE_EVERY_DAY; // 每天提醒
        }
    }

    /**
     * 检查是否需要发送提醒
     */
    private boolean shouldSendRemind(Long signTaskId, List<ConCtSignatoryTrPO> partyBSignatories, int remindType) {
        for (ConCtSignatoryTrPO partyBSignatory : partyBSignatories) {
            try {
                // 查询最近的提醒记录
                ConCtSignRemindRecordTrPO latestRecord = conCtSignRemindRecordTrRepo.findLatestRemindRecord(signTaskId, partyBSignatory.getId());

                if (latestRecord == null) {
                    // 没有发送过提醒，需要发送
                    return true;
                }

                // 计算距离上次发送的天数
                long daysSinceLastRemind = ChronoUnit.DAYS.between(latestRecord.getSmsSendTime().toLocalDate(), LocalDate.now());

                if (remindType == REMIND_TYPE_EVERY_2_DAYS && daysSinceLastRemind >= 2) {
                    return true;
                } else if (remindType == REMIND_TYPE_EVERY_DAY && daysSinceLastRemind >= 1) {
                    return true;
                }
            } catch (Exception e) {
                log.error("检查是否需要发送提醒失败，签署任务ID: {}, 签署方ID: {}", signTaskId, partyBSignatory.getId(), e);
                // 出错时默认发送提醒
                return true;
            }
        }

        return false;
    }

    /**
     * 发送短信提醒
     */
    private void sendSmsRemind(ConCtSigntaskTrPO signTask, List<ConCtSignatoryTrPO> partyBSignatories,
                               int remindType, long daysSincePartyASign) {

        for (ConCtSignatoryTrPO partyBSignatory : partyBSignatories) {
            try {
                // 获取乙方联系人手机号
                String mobile = getPartyBMobile(partyBSignatory);
                if (mobile == null || mobile.trim().isEmpty()) {
                    log.warn("乙方没有有效的手机号，签署方ID: {}", partyBSignatory.getId());
                    continue;
                }
                // 发送短信
                sendSms(mobile, signTask, partyBSignatory, remindType, daysSincePartyASign);

                log.info("签署提醒短信发送成功，任务ID: {}, 签署方ID: {}, 手机号: {}, 提醒类型: {}",
                        signTask.getId(), partyBSignatory.getId(), mobile, remindType);

            } catch (Exception e) {
                log.error("发送签署提醒短信失败，任务ID: {}, 签署方ID: {}, 错误: {}",
                        signTask.getId(), partyBSignatory.getId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 获取乙方联系人手机号
     */
    private String getPartyBMobile(ConCtSignatoryTrPO partyBSignatory) {
        // 优先使用代办人手机号
        if (partyBSignatory.getAgent() != null) {
            try {
                log.info("获取代办人信息，签署方ID: {}, 代办人ID: {}", partyBSignatory.getId(), partyBSignatory.getAgent());
                User user = userService.findById(partyBSignatory.getAgent());
                if (user != null) {
                    return user.getMobile();
                }
                return null;
            } catch (Exception e) {
                log.warn("获取代办人信息失败，签署方ID: {}", partyBSignatory.getId(), e);
            }
        }
        return null;
    }

    /**
     * 发送短信
     */
    private void sendSms(String mobile, ConCtSigntaskTrPO signTask,
                         ConCtSignatoryTrPO partyBSignatory, int remindType, long daysSincePartyASign) {

        try {
            // 使用通知服务发送短信
            NoticeTaskCreateReq1 noticeReq = new NoticeTaskCreateReq1();
            // 设置通知目标
            List<NoticeTarget> noticeTargets = new ArrayList<>();
            NoticeTarget noticeTarget = new NoticeTarget();
            noticeTarget.setMobile(mobile);
            noticeTargets.add(noticeTarget);
            noticeReq.setNoticeTargets(noticeTargets);
            // 设置业务代码
            noticeReq.setNoticeBusinessCode("CONTRACT_SIGN_REMIND");
            // 设置参数
            Map<String, Object> params = new HashMap<>();
            params.put("contractCode", signTask.getSignBillCode());
            noticeReq.setParams(params);

            // 发送通知
            noticeService.createTaskBySceneWithNoticeTarget(noticeReq);

            // 保存签署提醒记录到数据库
            saveSignRemindRecord(signTask, partyBSignatory, mobile, remindType, daysSincePartyASign);

        } catch (Exception e) {
            log.error("发送短信失败，手机号: {}", mobile, e);
            throw e;
        }
    }

    /**
     * 保存签署提醒记录
     */
    private void saveSignRemindRecord(ConCtSigntaskTrPO signTask, ConCtSignatoryTrPO partyBSignatory,
                                      String mobile, int remindType, long daysSincePartyASign) {
        try {
            ConCtSignRemindRecordTrPO record = new ConCtSignRemindRecordTrPO();
            record.setContractId(signTask.getSignBillId());
            record.setSignTaskId(signTask.getId());
            record.setSignatoryId(partyBSignatory.getId());
            record.setSmsSendTime(LocalDateTime.now());
            record.setMobile(mobile);
            record.setRemindType(String.valueOf(remindType));
            record.setPartyASignTime(getPartyASignTime(signTask.getId()));
            record.setDaysSincePartyASign(new BigDecimal(daysSincePartyASign));
            conCtSignRemindRecordTrRepo.insert(record);
            log.info("签署提醒记录保存成功，记录ID: {}", record.getId());
        } catch (Exception e) {
            log.error("保存签署提醒记录失败，任务ID: {}, 签署方ID: {}", signTask.getId(), partyBSignatory.getId(), e);
        }
    }

} 