package io.terminus.erp.contract.domain.util;

import com.github.wxpay.sdk.WXPayConstants;
import com.github.wxpay.sdk.WXPayConstants.SignType;
import io.terminus.trantor2.common.utils.JsonUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import lombok.extern.slf4j.Slf4j;

/**
 * 微信支付工具类
 *
 * @className: WXPayUtil
 * @author: charl
 * @date: 2023/11/6 15:09
 */
@Slf4j
public class WXPayUtil {

    public static Map<String, String> createWxPay(String appId, String mchId, String key, BigDecimal totalFee, String bizOrderInfo, String bizOrderCode, String notifyUrl) {
        try {
            SortedMap<String, String> parameters = new TreeMap<String, String>();
            parameters.put("appid", appId); //* 公众账号ID
            parameters.put("mch_id", mchId); //* 商户号
            parameters.put("device_info", "WEB"); //设备号
            parameters.put("nonce_str", com.github.wxpay.sdk.WXPayUtil.generateNonceStr()); //随机字符串
            String generateSignature = com.github.wxpay.sdk.WXPayUtil.generateSignature(parameters, key, SignType.MD5);
            parameters.put("sign", generateSignature);//签名
            parameters.put("body", bizOrderInfo);//商品描述
            parameters.put("out_trade_no", bizOrderCode);
            // 转换为分
            BigDecimal totalPrice = totalFee.multiply(BigDecimal.valueOf(100));
            parameters.put("total_fee", totalPrice.intValue()+"");//标价金额
            parameters.put("spbill_create_ip", InetAddress.getLocalHost().getHostAddress()); //终端IP
            parameters.put("notify_url", notifyUrl);//支付成功后回调的action，与JSAPI相同
            parameters.put("trade_type", "NATIVE");
            log.info("微信支付预下单请求json格式：{}", JsonUtil.toJson(parameters));

            String result = requestWithoutCert(WXPayConstants.UNIFIEDORDER_URL, parameters, key, 5000, 5000);
            return com.github.wxpay.sdk.WXPayUtil.xmlToMap(result);
        } catch (Exception ex) {
            log.error("invoke wx pay error:{}", ex.getCause());
            throw new RuntimeException();
        }

    }


    /**
     * 不需要证书的请求
     *
     * @param strUrl           String
     * @param reqData          向wxpay post的请求数据
     * @param connectTimeoutMs 超时时间，单位是毫秒
     * @param readTimeoutMs    超时时间，单位是毫秒
     * @return API返回数据
     * @throws Exception
     */
    public static String requestWithoutCert(String strUrl, Map<String, String> reqData, String key, int connectTimeoutMs, int readTimeoutMs) throws Exception {
        String UTF8 = "UTF-8";
        String reqBody = com.github.wxpay.sdk.WXPayUtil.generateSignedXml(reqData, key);
        log.info("微信支付预下单请求xml格式：{}", reqBody);
        URL httpUrl = new URL(strUrl);
        HttpURLConnection httpURLConnection = (HttpURLConnection) httpUrl.openConnection();
        httpURLConnection.setDoOutput(true);
        httpURLConnection.setRequestMethod("POST");
        httpURLConnection.setConnectTimeout(connectTimeoutMs);
        httpURLConnection.setReadTimeout(readTimeoutMs);
        httpURLConnection.connect();
        OutputStream outputStream = httpURLConnection.getOutputStream();
        outputStream.write(reqBody.getBytes(UTF8));

        // if (httpURLConnection.getResponseCode()!= 200) {
        //     throw new Exception(String.format("HTTP response code is %d, not 200", httpURLConnection.getResponseCode()));
        // }

        //获取内容
        InputStream inputStream = httpURLConnection.getInputStream();
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, UTF8));
        final StringBuffer stringBuffer = new StringBuffer();
        String line = null;
        while ((line = bufferedReader.readLine()) != null) {
            stringBuffer.append(line);
        }
        String resp = stringBuffer.toString();
        if (stringBuffer != null) {
            try {
                bufferedReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (outputStream != null) {
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        // if (httpURLConnection!=null) {
        //     httpURLConnection.disconnect();
        // }

        return resp;
    }

}
