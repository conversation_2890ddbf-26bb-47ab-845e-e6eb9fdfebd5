package io.terminus.erp.contract.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.contract.infrastructure.repo.emp.ContractEmployeeRepo;
import io.terminus.erp.contract.infrastructure.repo.role.ConOrgRoleTrRepo;
import io.terminus.erp.contract.infrastructure.repo.role.ConRoleItemTrRepo;
import io.terminus.erp.contract.infrastructure.repo.role.DbOrgAdmOrgCfRepo;
import io.terminus.erp.contract.infrastructure.repo.tactics.ContractDistributionConfigurationHeaderRepo;
import io.terminus.erp.contract.infrastructure.repo.tactics.ContractDistributionRuleRowRepo;
import io.terminus.erp.contract.spi.convert.tp.ContractDistributionConfigurationHeaderConverter;
import io.terminus.erp.contract.spi.dict.tp.CtHeadStatusDict;
import io.terminus.erp.contract.spi.model.role.dto.ConOrgRoleTrDTO;
import io.terminus.erp.contract.spi.model.role.dto.ConRoleItemTrDTO;
import io.terminus.erp.contract.spi.model.role.po.ConOrgRoleTrPO;
import io.terminus.erp.contract.spi.model.role.po.ConRoleItemTrPO;
import io.terminus.erp.contract.spi.model.tactics.dto.ContractDistributionConfigurationHeaderDTO;
import io.terminus.erp.contract.spi.model.tactics.dto.ExtCtHeadTrDTO;
import io.terminus.erp.contract.spi.model.tactics.po.ContractDistributionConfigurationHeaderPO;
import io.terminus.erp.contract.spi.model.tactics.po.ContractDistributionRuleRowPO;
import io.terminus.erp.md.infrastructure.repo.base.GenComTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.ct.CtHeadTrRepo;
import io.terminus.erp.md.infrastructure.repo.ct.CtHeadTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.org.OrgPurOrgCfRepo;
import io.terminus.erp.md.infrastructure.repo.vend.GenVendInfoMdRepo;
import io.terminus.erp.md.spi.model.po.base.GenComTypeCfPO;
import io.terminus.erp.md.spi.model.po.ct.CtHeadTrPO;
import io.terminus.erp.md.spi.model.po.ct.CtHeadTypeCfPO;
import io.terminus.erp.md.spi.model.po.org.OrgPurOrgCfPO;
import io.terminus.erp.md.spi.model.po.vend.GenVendInfoMdPO;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.trantor.org.spi.model.po.EmployeePO;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ContractDistributionConfigurationHeaderService {

    @Resource
    private CtHeadTrRepo ctHeadTrRepo;

    @Resource
    private ContractEmployeeRepo contractEmployeeRepo;

    @Resource
    private ContractDistributionRuleRowRepo contractDistributionRuleRowRepo;

    @Resource
    private ContractDistributionConfigurationHeaderRepo contractDistributionConfigurationHeaderRepo;

    @Resource
    private ContractDistributionConfigurationHeaderConverter contractDistributionConfigurationHeaderConverter;

    @Resource
    private CtHeadTypeCfRepo ctHeadTypeCfRepo;

    @Resource
    private GenComTypeCfRepo genComTypeCfRepo;

    @Resource
    private GenVendInfoMdRepo genVendInfoMdRepo;

    @Resource
    private OrgPurOrgCfRepo orgPurOrgCfRepo;

    @Resource
    private IAMClient iamClient;

    @Resource
    private ConOrgRoleTrRepo conOrgRoleTrRepo;

    @Resource
    private ConRoleItemTrRepo conRoleItemTrRepo;

    @Resource
    private DbOrgAdmOrgCfRepo dbOrgAdmOrgCfRepo;


    private static final ThreadLocal<List<ContractDistributionConfigurationHeaderPO>> CONTRACT_DISTRIBUTION_CONFIGURATION_LOCAL = new ThreadLocal<>();

    /**
     * 合同派单展示规则保存/更新
     * @param contractDistributionConfigurationHeaderDTO 合同派单规则
     */
    @Transactional
    public void saveOrUpdate(ContractDistributionConfigurationHeaderDTO contractDistributionConfigurationHeaderDTO) {
        try {
            // 这个里面数据不多 不会超过10条
            List<ContractDistributionConfigurationHeaderPO> contractDistributionConfigurationHeaderPOS = contractDistributionConfigurationHeaderRepo.selectList(null);
            CONTRACT_DISTRIBUTION_CONFIGURATION_LOCAL.set(contractDistributionConfigurationHeaderPOS);
            // 检查数据
            contractDistributionConfigurationHeaderDTO.checkData();
            // 保存或者更新数据
            if (Objects.isNull(contractDistributionConfigurationHeaderDTO.getId())) {
                save(contractDistributionConfigurationHeaderDTO);
            } else {
                update(contractDistributionConfigurationHeaderDTO);
            }
        } finally {
            CONTRACT_DISTRIBUTION_CONFIGURATION_LOCAL.remove();
        }
    }

    /**
     *  保存
     * @param contractDistributionConfigurationHeaderDTO 合同派单规则
     */
    private void save(ContractDistributionConfigurationHeaderDTO contractDistributionConfigurationHeaderDTO) {
        // 保存前置检查是否存在有相同的行政组织维护了数据
        List<ContractDistributionConfigurationHeaderPO> contractDistributionConfigurationHeaderPOS = CONTRACT_DISTRIBUTION_CONFIGURATION_LOCAL.get();
        for (ContractDistributionConfigurationHeaderPO contractDistributionConfigurationHeaderPO : contractDistributionConfigurationHeaderPOS) {
            if (Objects.equals(contractDistributionConfigurationHeaderPO.getAdministrativeOrganization(), contractDistributionConfigurationHeaderDTO.getAdministrativeOrganization())) {
                throw new BusinessException("该行政组织已经存在配置规则");
            }
        }
        // 头数据
        ContractDistributionConfigurationHeaderPO header = contractDistributionConfigurationHeaderConverter.convert(contractDistributionConfigurationHeaderDTO);
        contractDistributionConfigurationHeaderRepo.insert(header);
        // 行数据保存
        saveLine(contractDistributionConfigurationHeaderDTO, header.getId());
    }

    /**
     * 更新
     * @param contractDistributionConfigurationHeaderDTO 合同派单规则
     */
    private void update(ContractDistributionConfigurationHeaderDTO contractDistributionConfigurationHeaderDTO) {
        // 保存前置检查是否存在有相同的行政组织维护了数据
        List<ContractDistributionConfigurationHeaderPO> contractDistributionConfigurationHeaderPOS = CONTRACT_DISTRIBUTION_CONFIGURATION_LOCAL.get();
        for (ContractDistributionConfigurationHeaderPO contractDistributionConfigurationHeaderPO : contractDistributionConfigurationHeaderPOS) {
            if (Objects.equals(contractDistributionConfigurationHeaderPO.getId(), contractDistributionConfigurationHeaderDTO.getId())) {
                continue;
            }
            if (Objects.equals(contractDistributionConfigurationHeaderPO.getAdministrativeOrganization(), contractDistributionConfigurationHeaderDTO.getAdministrativeOrganization())) {
                throw new BusinessException("该行政组织已经存在配置规则");
            }
        }
        // 处理头信息
        ContractDistributionConfigurationHeaderPO header = contractDistributionConfigurationHeaderConverter.convert(contractDistributionConfigurationHeaderDTO);
        if (Objects.isNull(header.getDesc())) {
            header.setDesc("");
        }
        contractDistributionConfigurationHeaderRepo.updateById(header);
        // 处理行信息 先删除
        contractDistributionRuleRowRepo.deleteLineByHeaderId(header.getId());
        // 行数据保存
        saveLine(contractDistributionConfigurationHeaderDTO, header.getId());
    }

    /**
     * 保存行数据
     * @param contractDistributionConfigurationHeaderDTO 合同派单规则
     * @param headerId 头ID
     */
    private void saveLine(ContractDistributionConfigurationHeaderDTO contractDistributionConfigurationHeaderDTO, Long headerId) {
        // 行数据
        List<ContractDistributionRuleRowPO> line = contractDistributionConfigurationHeaderDTO
                .getDistributionRuleRow()
                .stream()
                .map(contractDistributionConfigurationHeaderConverter::convert)
                .peek(contractDistributionRuleRowPO -> {
                    contractDistributionRuleRowPO.setDistributionConfigurationHeader(headerId);
                    contractDistributionRuleRowPO.setId(null);
                })
                .collect(Collectors.toList());
        contractDistributionRuleRowRepo.insertBatch(line);
    }

    /**
     * 删除数据
     * @param contractDistributionConfigurationHeaderDTO 待删除数据
     */
    @Transactional
    public void delete(ContractDistributionConfigurationHeaderDTO contractDistributionConfigurationHeaderDTO) {
        // 查询头信息
        Long headerId = contractDistributionConfigurationHeaderDTO.getId();
        ContractDistributionConfigurationHeaderPO contractDistributionConfigurationHeaderPO = contractDistributionConfigurationHeaderRepo.selectById(headerId);
        // 删除头行信息
        contractDistributionConfigurationHeaderRepo.deleteById(contractDistributionConfigurationHeaderPO.getId());
        // 删除行信息
        contractDistributionRuleRowRepo.deleteLineByHeaderId(headerId);
    }


    /**
     * 合同共享池
     * @param pageable 分页参数
     * @return 合同共享池
     */
    public Paging<ExtCtHeadTrDTO> contractPaging(Pageable pageable) {
        // 1. 获取当前用户ID
        Long userId = TrantorContext.getCurrentUser().getId();
        List<Role> roleList;
        try {
            roleList = iamClient.rolerelationClient().findRoleByUserId(userId).execute();
        } catch (Exception e) {
            throw new BusinessException("查询角色失败");
        }
        // 交接的RoleId
        List<String> intersectRoleKeys = this.findIntersectRoleIds(roleList.stream().map(Role::getKey).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(intersectRoleKeys)) {
            return selectPage(pageable, null);
        }
        // 根绝交集角色查询 角色明细行
        List<ConRoleItemTrPO> conRoleItemTrPOS = conRoleItemTrRepo.findByRoleIds(intersectRoleKeys);
        if (CollectionUtils.isEmpty(conRoleItemTrPOS)) {
            return selectPage(pageable, null);
        }
        List<Long> roleHeaderIds = conRoleItemTrPOS.stream().map(ConRoleItemTrPO::getConOrgRoleTrId).distinct().collect(Collectors.toList());
        List<ConOrgRoleTrPO> conOrgRoleTrPOS = conOrgRoleTrRepo.selectBatchIds(roleHeaderIds);
        List<Long> orgAuthIdIds = conOrgRoleTrPOS.stream().map(ConOrgRoleTrPO::getId).collect(Collectors.toList());
        return selectPage(pageable, orgAuthIdIds);
    }

    public ConOrgRoleTrDTO findRuleOrgIds(Pageable pageable) {
        ConOrgRoleTrDTO response = new ConOrgRoleTrDTO();

        List<ConRoleItemTrDTO> responseList = new ArrayList<>();

        response.setRoleLine(responseList);
        // 1. 获取当前用户ID
        Long userId = TrantorContext.getCurrentUser().getId();
        List<Role> roleList;
        try {
            roleList = iamClient.rolerelationClient().findRoleByUserId(userId).execute();
        } catch (Exception e) {
            throw new BusinessException("查询角色失败");
        }
        // 交接的RoleId
        List<String> intersectRoleKeys = this.findIntersectRoleIds(roleList.stream().map(Role::getKey).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(intersectRoleKeys)) {
            return response;
        }
        // 根绝交集角色查询 角色明细行
        List<ConRoleItemTrPO> conRoleItemTrPOS = conRoleItemTrRepo.findByRoleIds(intersectRoleKeys);
        if (CollectionUtils.isEmpty(conRoleItemTrPOS)) {
            return response;
        }
        List<Long> roleHeaderIds = conRoleItemTrPOS.stream().map(ConRoleItemTrPO::getConOrgRoleTrId).distinct().collect(Collectors.toList());
        List<ConOrgRoleTrPO> conOrgRoleTrPOS = conOrgRoleTrRepo.selectBatchIds(roleHeaderIds);

        for (ConOrgRoleTrPO conOrgRoleTrPO : conOrgRoleTrPOS) {
            ConRoleItemTrDTO conRoleItemTrDTO = new ConRoleItemTrDTO();
            conRoleItemTrDTO.setId(conOrgRoleTrPO.getId());
            responseList.add(conRoleItemTrDTO);
        }

        return response;
    }

    /**
     * 查询所有组织角色头及其下所有角色行，并与roleList取交集，返回交集roleId列表
     * @param roleList 角色ID列表
     * @return 交集后的roleId列表
     */
    public List<String> findIntersectRoleIds(List<String> roleList) {
        List<ConRoleItemTrPO> allLines = conRoleItemTrRepo.selectList(null);
        // 提取所有行的roleId
        List<String> allLineRoleIds = allLines.stream().map(ConRoleItemTrPO::getRoleKey).collect(Collectors.toList());
        // 取交集
        return allLineRoleIds.stream().distinct().filter(roleList::contains).collect(Collectors.toList());
    }


    public Map<Long, EmployeePO> findEmployeeByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<EmployeePO> employeePOS = contractEmployeeRepo.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(employeePOS)) {
            return new HashMap<>();
        }
        return employeePOS.stream().collect(Collectors.toMap(EmployeePO::getId, Function.identity()));
    }

    public Paging<ExtCtHeadTrDTO> selectPage(Pageable pageable, List<Long> roleOrgIds) {
        // 分页对象
        Page<CtHeadTrPO> page = new Page<>(pageable.getPageNo(), pageable.getPageSize());
        String ctCode = pageable.getConditionValue("ctCode"); // 合同编码
        LambdaQueryWrapper<CtHeadTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CtHeadTrPO::getCtStatus, CtHeadStatusDict.SUB_DRAFT) // 已提交等待编制
                .eq(Objects.nonNull(ctCode), CtHeadTrPO::getCtCode, ctCode)
                .in(!CollectionUtils.isEmpty(roleOrgIds), CtHeadTrPO::getRuleAuthId, roleOrgIds)
                .orderByDesc(CtHeadTrPO::getId);
        page = ctHeadTrRepo.selectPage(page, wrapper);
        // 合同类型
        List<CtHeadTrPO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new Paging<>();
        }
        List<Long> ctTypeIds = records.stream().map(CtHeadTrPO::getCtType).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, CtHeadTypeCfPO> ctHeadTypeCfMap = findCtHeadTypeCfMap(ctTypeIds);
        // 签约人
        List<Long> signatoryIds = records.stream().map(CtHeadTrPO::getSignatory).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, EmployeePO> employeePOMap = findEmployeeByIds(signatoryIds);
        // 甲方
        List<Long> pAIds = records.stream().map(CtHeadTrPO::getPrtnA).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, GenComTypeCfPO> genComTypeCfPOMap = findGenComTypeCfPOMap(pAIds);
        // 乙方
        List<Long> pBIds = records.stream().map(CtHeadTrPO::getPrtnB).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, GenVendInfoMdPO> genVendInfoMap = findGenVendInfoMap(pBIds);
        // 采购组织
        List<Long> orgIds = records.stream().map(CtHeadTrPO::getPurOrgCodeApplicable).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, OrgPurOrgCfPO> orgPurOrgCfPOMap = findOrgPurOrgCfPOMap(orgIds);
        // 签约组织
        List<Long> signOrgIds = records.stream().map(CtHeadTrPO::getRuleAuthId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, ConOrgRoleTrPO> orgAdmOrgCfExtPOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(signOrgIds)) {
            List<ConOrgRoleTrPO> conOrgRoleTrPOS = conOrgRoleTrRepo.selectBatchIds(signOrgIds);

            orgAdmOrgCfExtPOMap = conOrgRoleTrPOS.stream().collect(Collectors.toMap(ConOrgRoleTrPO::getId, Function.identity()));
        }
        List<ExtCtHeadTrDTO> response = new ArrayList<>();
        for (CtHeadTrPO record : records) {
            ExtCtHeadTrDTO extCtHeadTrDTO = new ExtCtHeadTrDTO();
            extCtHeadTrDTO.setId(record.getId());
            extCtHeadTrDTO.setCtCode(record.getCtCode());
            if (ctHeadTypeCfMap.containsKey(record.getCtType())) {
                CtHeadTypeCfPO ctHeadTypeCfPO = ctHeadTypeCfMap.get(record.getCtType());
                extCtHeadTrDTO.setCtTypeName(ctHeadTypeCfPO.getCtTypeName());
            }
            extCtHeadTrDTO.setCtStatus(record.getCtStatus());
            // 签约人
            if (employeePOMap.containsKey(record.getSignatory())) {
                EmployeePO employeePO = employeePOMap.get(record.getSignatory());
                extCtHeadTrDTO.setSignatory(employeePO.getName());
            }
            // 甲方
            if (genComTypeCfPOMap.containsKey(record.getPrtnA())) {
                GenComTypeCfPO genComTypeCfPO = genComTypeCfPOMap.get(record.getPrtnA());
                extCtHeadTrDTO.setPrtnA(genComTypeCfPO.getName());
            }
            // 乙方
            if (genVendInfoMap.containsKey(record.getPrtnB())) {
                GenVendInfoMdPO genVendInfoMdPO = genVendInfoMap.get(record.getPrtnB());
                extCtHeadTrDTO.setPrtnB(genVendInfoMdPO.getName());
            }
            // 签约组织
            if (orgAdmOrgCfExtPOMap.containsKey(record.getRuleAuthId())) {
                ConOrgRoleTrPO conOrgRoleTrPO = orgAdmOrgCfExtPOMap.get(record.getRuleAuthId());
                extCtHeadTrDTO.setOrgName(conOrgRoleTrPO.getOrgName());
            }
            extCtHeadTrDTO.setCtTaxAmt(record.getCtTaxAmt());
            extCtHeadTrDTO.setCtAmt(record.getCtAmt());
            extCtHeadTrDTO.setSupplementContract(Objects.isNull(record.getSupplementContract()) ? Boolean.FALSE : record.getSupplementContract());
            // 采购组织
            if (orgPurOrgCfPOMap.containsKey(record.getPurOrgCodeApplicable())) {
                OrgPurOrgCfPO orgPurOrgCfPO = orgPurOrgCfPOMap.get(record.getPurOrgCodeApplicable());
                extCtHeadTrDTO.setPurOrgCodeApplicable(orgPurOrgCfPO.getName());
            }
            extCtHeadTrDTO.setCreatedAt(record.getCreatedAt());
            extCtHeadTrDTO.setUpdatedAt(record.getUpdatedAt());
            response.add(extCtHeadTrDTO);
        }
        return new Paging<>(page.getTotal(), response);
    }

    /**
     * 获取合同类型
     * @param ctTypeIds 合同类型ID
     * @return 合同类型
     */
    public Map<Long, CtHeadTypeCfPO> findCtHeadTypeCfMap(List<Long> ctTypeIds) {
        if (CollectionUtils.isEmpty(ctTypeIds)) {
            return new HashMap<>();
        }
        List<CtHeadTypeCfPO> ctHeadTypeCfPOS = ctHeadTypeCfRepo.selectBatchIds(ctTypeIds);
        if (CollectionUtils.isEmpty(ctHeadTypeCfPOS)) {
            return new HashMap<>();
        }
        return ctHeadTypeCfPOS.stream().collect(Collectors.toMap(CtHeadTypeCfPO::getId, Function.identity()));
    }

    /**
     * 获取公司甲方
     * @param ids 甲方信息
     * @return 获取甲方信息
     */
    public Map<Long, GenComTypeCfPO> findGenComTypeCfPOMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<GenComTypeCfPO> genComTypeCfPOS = genComTypeCfRepo.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(genComTypeCfPOS)) {
            return new HashMap<>();
        }
        return genComTypeCfPOS.stream().collect(Collectors.toMap(GenComTypeCfPO::getId, Function.identity()));
    }

    /**
     * 获取供应商主数据
     * @param ids 供应商ID
     * @return 供应商主数据
     */
    public Map<Long, GenVendInfoMdPO> findGenVendInfoMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<GenVendInfoMdPO> genVendInfoMdPOS = genVendInfoMdRepo.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(genVendInfoMdPOS)) {
            return new HashMap<>();
        }
        return genVendInfoMdPOS.stream().collect(Collectors.toMap(GenVendInfoMdPO::getId, Function.identity()));
    }

    public Map<Long, OrgPurOrgCfPO> findOrgPurOrgCfPOMap(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<OrgPurOrgCfPO> orgPurOrgCfPOS = orgPurOrgCfRepo.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(orgPurOrgCfPOS)) {
            return new HashMap<>();
        }
        return orgPurOrgCfPOS.stream().collect(Collectors.toMap(OrgPurOrgCfPO::getId, Function.identity()));
    }

}
