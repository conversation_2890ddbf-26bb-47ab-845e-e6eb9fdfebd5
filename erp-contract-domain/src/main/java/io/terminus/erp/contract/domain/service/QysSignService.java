package io.terminus.erp.contract.domain.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.base.Joiner;
import com.google.common.base.MoreObjects;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import io.terminus.cloud.storage.core.client.CloudClient;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.contract.domain.util.FileHelper;
import io.terminus.erp.contract.domain.util.PDFKeywordSearchUtil;
import io.terminus.erp.contract.infrastructure.repo.tp.*;
import io.terminus.erp.contract.spi.convert.tp.ConCtEsignatureTrConverter;
import io.terminus.erp.contract.spi.convert.tp.ConCtSignfileTrConverter;
import io.terminus.erp.contract.spi.convert.tp.ConCtSigntaskTrConverter;
import io.terminus.erp.contract.spi.dict.tp.ConCtEsignatureTrEsignatureStatusDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryTrSignatoryStatusDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryTrSignatoryTypeDict;
import io.terminus.erp.contract.spi.dict.tp.ConSignatoryAccountUsedSignTypeDict;
import io.terminus.erp.contract.spi.model.tp.dto.*;
import io.terminus.erp.contract.spi.model.tp.po.*;
import io.terminus.erp.md.infrastructure.repo.base.GenComGraphicSealCfRepo;
import io.terminus.erp.md.infrastructure.repo.base.GenComTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.ct.CtHeadTrRepo;
import io.terminus.erp.md.spi.convert.base.GenComGraphicSealCfConverter;
import io.terminus.erp.md.spi.dict.base.GenComGraphicSealTypeDict;
import io.terminus.erp.md.spi.dict.base.GenContractStampPositioningMehodDict;
import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import io.terminus.erp.md.spi.model.po.base.GenComGraphicSealCfPO;
import io.terminus.erp.md.spi.model.po.base.GenComTypeCfPO;
import io.terminus.erp.md.spi.model.po.ct.CtHeadTrPO;
import io.terminus.erp.md.spi.model.po.vend.GenVendInfoMdPO;
import io.terminus.erp.md.spi.util.MD;
import io.terminus.notice.sdk.req.task.NoticeTaskCreateReq0;
import io.terminus.notice.sdk.service.NoticeService;
import io.terminus.thirdparty.common.constant.ThirdPartyChannel;
import io.terminus.thirdparty.common.result.ThirdPartyListResponse;
import io.terminus.thirdparty.common.result.ThirdPartyResponse;
import io.terminus.thirdparty.common.util.DateUtil;
import io.terminus.thirdparty.sign.api.client.SignClient;
import io.terminus.thirdparty.sign.api.dto.CompanyDetailResponse;
import io.terminus.thirdparty.sign.api.dto.CreateESignResponse;
import io.terminus.thirdparty.sign.api.dto.GetSignUrlResponse;
import io.terminus.thirdparty.sign.api.dto.QuerySealListResponse;
import io.terminus.thirdparty.sign.api.dto.request.*;
import io.terminus.thirdparty.sign.api.dto.request.CreateESignRequest.Action;
import io.terminus.thirdparty.sign.api.dto.request.CreateESignRequest.Rect;
import io.terminus.thirdparty.sign.api.dto.request.CreateESignRequest.Signatory;
import io.terminus.thirdparty.sign.qys.properties.QysProperties;
import io.terminus.trantor2.common.TrantorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.sdk.api.CompanyService;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 契约锁签署服务
 *
 * @className: QysSignService
 * @author: charl
 * @date: 2023/10/13 15:31
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class QysSignService {

    private final SignClient signClient;
    private final QysProperties qysProperties;
    public final CtSignTaskService signTaskService;
    private final ConCtEsignatureTrRepo conCtEsignatureTrRepo;
    private final ConCtEsignatureTrConverter conCtEsignatureTrConverter;
    private final ConCtSignfileTrConverter conCtSignfileTrConverter;
    private final ConCtSignfileTrRepo conCtSignfileTrRepo;
    private final ConCtSigntaskTrConverter conCtSigntaskTrConverter;
    private final ConCtSigntaskTrRepo conCtSigntaskTrRepo;
    private final ConCtSignatoryTrRepo conCtSignatoryTrRepo;
    private final CtHeadTrRepo ctHeadTrRepo;
    private final CloudClient cloudClient;
    private final ConCtSignatoryAccountTrRepo conCtSignatoryAccountTrRepo;
    private final NoticeService noticeService;
    public final CtBaseService ctBaseService;
    private final CtOssUtil ctOssUtil;
    private final CompanyService companyService;
    private final GenComTypeCfRepo genComTypeCfRepo;
    private final GenComGraphicSealCfConverter genComGraphicSealCfConverter;
    private final GenComGraphicSealCfRepo genComGraphicSealCfRepo;

    @Value("${terminus.contract.autoSignManager.userName:张军}")
    private String autoSignManagerName;

    @Value("${terminus.contract.autoSignManager.mobile:***********}")
    private String autoSignManagerMobile;

    @Value("${terminus.contract.notice.template.event:*****************}")
    private String eventNoticeTemplate;

    // btClass存储值与展示值映射
    private static final Map<String, String> BT_CLASS_DISPLAY_MAP = new HashMap<>();

    static {
        BT_CLASS_DISPLAY_MAP.put("PARTS", "配件合同");
        BT_CLASS_DISPLAY_MAP.put("INFRASTRUCTURE", "基建材料合同");
        BT_CLASS_DISPLAY_MAP.put("ACCESSORY", "辅材合同");
        BT_CLASS_DISPLAY_MAP.put("SOCIAL_INVENTORY", "社会库存协议");
        BT_CLASS_DISPLAY_MAP.put("SERVICE", "维保合同");
        BT_CLASS_DISPLAY_MAP.put("LONG_TERM", "长期合同");
        BT_CLASS_DISPLAY_MAP.put("EQUIPMENT", "设备合同");
        BT_CLASS_DISPLAY_MAP.put("SUPPLIER", "供货保证协议");
        BT_CLASS_DISPLAY_MAP.put("TRANSPORTATION", "运输合同");
        BT_CLASS_DISPLAY_MAP.put("COAL", "煤炭合同");
        BT_CLASS_DISPLAY_MAP.put("QUALITY", "质量处理协议");
        BT_CLASS_DISPLAY_MAP.put("MECHATRONICS", "机电材料合同");
    }

    public static String getBtClassDisplay(String btClass) {
        return BT_CLASS_DISPLAY_MAP.getOrDefault(btClass, btClass);
    }

    /**
     * 将文件上传到契约锁
     *
     * @param signFiles           文件内容
     * @param companySealComplete
     * @return 契约锁的文件ID，以及对应的签章定位方式
     */
    public Map<String, String> uploadQysFileGetDocumentId(List<ConCtSignfileTrDTO> signFiles, boolean companySealComplete) {
        if (CollectionUtils.isEmpty(signFiles)) {
            throw new BusinessException("签署文件为空");
        }
        Map<String, String> documents = Maps.newTreeMap();
        for (ConCtSignfileTrDTO signFile : signFiles) {
            try {
                CreateDocByFileRequest createDocByFileRequest = new CreateDocByFileRequest();
                createDocByFileRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
                String bucket = ctOssUtil.getBucket(true);
                String objectName = FileHelper.getOSSUrlObjectName(companySealComplete ? signFile.getSignedFile() : signFile.getSignFile(), bucket);
                InputStream inputStream = cloudClient.downloadFile(bucket, objectName);
                createDocByFileRequest.setInputStream(inputStream);
                createDocByFileRequest.setTitle(signFile.getFileName());
                ThirdPartyResponse<String> document = signClient.createDocumentByFile(createDocByFileRequest);
                if (!document.isSuccess()) {
                    throw new BusinessException(document.getMessage());
                }
                String documentId = document.getData();
                signFile.setThirdFileId(documentId);
                documents.put(documentId, signFile.getStampPositioningMethod());
                log.info("uploadQysFileGetDocumentId {} {}", documentId, signFile.getStampPositioningMethod());
                // 更新三方签署文件ID
                ConCtSignfileTrPO conCtSignfileTrPO = conCtSignfileTrConverter.dto2Po(signFile);
                conCtSignfileTrRepo.updateById(conCtSignfileTrPO);
            } catch (Exception e) {
                log.error("签约文件同步契约锁执行异常 {}", Throwables.getStackTraceAsString(e));
                throw new BusinessException(e.getMessage());
            }
        }
        return documents;
    }

    /**
     * 根据公司的名称查询契约锁的公司的印章
     *
     * @param partyAList 甲方所有公司
     * @return 公司名称：公司电子章
     */
    public Map<String, Long> getCompanySign(List<ConCtSignatoryTrDTO> partyAList) {
        if (CollectionUtils.isEmpty(partyAList)) {
            throw new BusinessException("公司信息为空");
        }
        Map<String, Long> sealMap = new HashMap<>();
        for (ConCtSignatoryTrDTO conCtSignatoryTrDTO : partyAList) {
            String companyName = conCtSignatoryTrDTO.getSignatory().getName();
            QuerySealListRequest querySealListRequest = new QuerySealListRequest();
            querySealListRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
            querySealListRequest.setCompanyName(companyName);
            ThirdPartyListResponse<QuerySealListResponse> sealList = signClient.sealList(querySealListRequest);
            List<QuerySealListResponse> responses = sealList.getData() == null ? null : sealList.getData().stream().filter(v -> v.getName().endsWith("合同专用章")).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(responses)) {
                throw new BusinessException("甲方电子签章不存在");
            }
            QuerySealListResponse seal = responses.get(0);
            sealMap.put(companyName, seal.getId());
        }
        return sealMap;
    }

    /**
     * 发起线上签署:
     * 过滤所有的线上签署的签署方，然后调用契约锁接口，发起电子签署。
     * fixme: 设置签署任务和签署方的签署是线上还是线下签署的逻辑，最好是放到创建签署任务的流程里。而非甲方自动签署逻辑里
     *
     * @param request
     * @return
     */
    public ConCtSigntaskTrDTO launchOnlineSign(ConCtSigntaskTrDTO request) {
        final CtHeadTrPO ctHeadTrPO = ctHeadTrRepo.selectById(request.getSignBillId());
        final ConCtSigntaskTrDTO signtaskTrDTO = signTaskService.getSignTaskDetail(request.getId());
        // 甲方签署信息
        final List<ConCtSignatoryTrDTO> signatories = signtaskTrDTO.getSignatories();
        final List<ConCtSignatoryTrDTO> hasEsignPartyAList = signatories.stream()
                .filter(signatory -> signatory.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYA) && Objects.nonNull(signatory.geteSign()))
                .collect(Collectors.toList());
        // 乙方签署信息
        final List<ConCtSignatoryTrDTO> hasEsignPartyBList = signatories.stream()
                .filter(signatory -> signatory.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYB) && Objects.nonNull(signatory.geteSign()))
                .collect(Collectors.toList());

        // true 线上；false 线下
        final boolean aIsOnline = !CollectionUtils.isEmpty(hasEsignPartyAList);
        final boolean bIsOnline = !CollectionUtils.isEmpty(hasEsignPartyBList);

        // 甲方没有电子签章 那么直接返回 不需要自动签署
        boolean companySealComplete = false;
        if (CollectionUtils.isEmpty(hasEsignPartyAList)) {

            // 判断是否有自己上传的企业章
            LambdaQueryWrapper<GenComGraphicSealCfPO> genComGraphicSealCfPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            genComGraphicSealCfPOLambdaQueryWrapper.eq(GenComGraphicSealCfPO::getGraphicSealType, GenComGraphicSealTypeDict.CONTRACT)
                    .eq(GenComGraphicSealCfPO::getGenComTypeCfId, ctHeadTrPO.getPrtnA())
                    .orderByDesc(GenComGraphicSealCfPO::getId).last("limit 1");
            GenComGraphicSealCfPO genComGraphicSealCfPO = genComGraphicSealCfRepo.selectOne(genComGraphicSealCfPOLambdaQueryWrapper);
            if (Objects.nonNull(genComGraphicSealCfPO)) {
                // 盖上传的章
                List<ConCtSignatoryTrDTO> partASignList = signatories.stream()
                        .filter(signatory -> signatory.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYA))
                        .collect(Collectors.toList());
                autosignByUploadSeal(signtaskTrDTO, genComGraphicSealCfPO, partASignList);

                for (ConCtSignatoryTrDTO conCtSignatoryTrDTO : partASignList) {
                    signTaskService.updateSignatoryToSigned(conCtSignatoryTrDTO);
                }
                companySealComplete = true;
            }
        }

        // 签约供应商
        List<Signatory> signatoryList = new ArrayList<>();
        CreateESignRequest createESignRequest = new CreateESignRequest();
        // 1.上传合同文档（所有文件）将文件ID拿回来
        Map<String, String> documents = uploadQysFileGetDocumentId(signtaskTrDTO.getSignFiles(),companySealComplete);

        createESignRequest.setSubject(DateUtil.dateTimeNow());
        createESignRequest.setThirdPartyId(qysProperties.getCategory());
        createESignRequest.setFileIds(Lists.newArrayList(documents.keySet()));
        createESignRequest.setCustomBizNum(signtaskTrDTO.getSignBillId() + "");
        createESignRequest.setCreatorName(autoSignManagerName);
        createESignRequest.setCreateContract(autoSignManagerMobile);
        int index = 1;
        if (aIsOnline) {
            // 3.1.查询甲方公司印章
            Map<String, Long> sealMap = getCompanySign(hasEsignPartyAList);

            int total = hasEsignPartyAList.size();
            // 3.2.甲方列表签署信息
            for (ConCtSignatoryTrDTO conCtSignatoryTrDTO : hasEsignPartyAList) {
                Signatory signatory = buildPartyASignatory(conCtSignatoryTrDTO, documents, index, total, sealMap);
                signatoryList.add(signatory);
                index++;
            }
        }

        // 乙方签约信息
        if (bIsOnline) {
            Signatory signatory = buildPartyBSignatory(hasEsignPartyBList, index, documents);
            signatoryList.add(signatory);
        }


        // 将签署方发送至契约锁
        createESignRequest.setSignatories(signatoryList);
        createESignRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
        ThirdPartyResponse<CreateESignResponse> signTask = signClient.createSignTask(createESignRequest);
        if (!signTask.isSuccess()) {
            throw new BusinessException(signTask.getMessage());
        }
        // 保存契约锁签约流程信息
        String flowId = signTask.getData().getFlowId();
        signtaskTrDTO.setSignFlowId(flowId);
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrConverter.dto2Po(signtaskTrDTO);
        conCtSigntaskTrRepo.updateById(conCtSigntaskTrPO);

        // 6.甲方公司自动签署合同
        for (ConCtSignatoryTrDTO conCtSignatoryTrDTO : hasEsignPartyAList) {
            String partACompanyName = conCtSignatoryTrDTO.getSignatory().getName();
            SignByCompanyRequest signByCompanyRequest = new SignByCompanyRequest();
            signByCompanyRequest.setContractId(flowId);
            signByCompanyRequest.setTenantName(partACompanyName);
            signByCompanyRequest.setStampers(Lists.newArrayList());
            signByCompanyRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
            ThirdPartyResponse signResponse = signClient.signByCompany(signByCompanyRequest);
            log.info("自动签署结果：result:{}", JSONUtil.toJsonStr(signResponse));
            if (signResponse.isSuccess()) {
                // 更新签署方状态
                // fixme: 不在这里修改签署状态，而在契约锁回掉的地方修改签署状态
                signTaskService.updateSignatoryToSigned(conCtSignatoryTrDTO);
            }

        }
        // 甲方公司去掉自动签署的,通知签署
        List<ConCtSignatoryTrDTO> signatorieNoticeList = signatories;
        if (companySealComplete){
            signatorieNoticeList = signatorieNoticeList.stream().filter(v -> !Objects.equals(v.getSignatoryType(), ConCtSignatoryTrSignatoryTypeDict.PARTYA)).collect(Collectors.toList());
        }
        signTaskService.noticeSign(signatorieNoticeList, MoreObjects.firstNonNull(ctHeadTrPO.getCtName(), ctHeadTrPO.getCtCode()), true);
        return signtaskTrDTO;
    }

    private void autosignByUploadSeal(ConCtSigntaskTrDTO signtaskTrDTO, GenComGraphicSealCfPO genComGraphicSealCfPO, List<ConCtSignatoryTrDTO> partASignList) {
        //List<String> companyNameList = partASignList.stream().map(it -> it.getSignatory().getName()).collect(Collectors.toList());

        // 一次性下载印章图片，避免重复下载
        byte[] sealImageBytes = downloadSealImage(genComGraphicSealCfPO);

        for (ConCtSignfileTrDTO signFile : signtaskTrDTO.getSignFiles()) {
            String bucket = ctOssUtil.getBucket(true);
            String objectName = FileHelper.getOSSUrlObjectName(signFile.getSignFile(), bucket);

            try (InputStream inputStream = cloudClient.downloadFile(bucket, objectName)) {
                try {
                    // 为每个甲方公司在PDF上盖章，好像只会有一个
                    for (ConCtSignatoryTrDTO conCtSignatoryTrDTO : partASignList) {
                        addSealToPdf(inputStream, sealImageBytes, conCtSignatoryTrDTO.getSignatory().getName(), signFile);
                    }
                } catch (Exception e) {
                    log.error("为文件 {} 盖章时发生错误", signFile.getFileName(), e);
                    throw new BusinessException("盖章失败: " + e.getMessage());
                }
            } catch (IOException e) {
                log.warn("关闭输入流时发生错误", e);
            }
        }

        List<ConCtSignfileTrDTO> updateFileList = signtaskTrDTO.getSignFiles().stream().map(it -> {
            ConCtSignfileTrDTO conCtSignfileTrDTO = new ConCtSignfileTrDTO();
            conCtSignfileTrDTO.setId(it.getId());
            conCtSignfileTrDTO.setSignedFile(it.getSignedFile());
            return conCtSignfileTrDTO;
        }).collect(Collectors.toList());
        conCtSignfileTrRepo.updateBatch(conCtSignfileTrConverter.dto2PoList(updateFileList));
    }

    /**
     * 构建契约锁甲方签署信息:
     * 签署方、电子章的位置信息
     *
     * @param conCtSignatoryTrDTO
     * @param documents
     * @param index
     * @param total
     * @param sealMap
     * @return
     */
    private static Signatory buildPartyASignatory(ConCtSignatoryTrDTO conCtSignatoryTrDTO, Map<String, String> documents, int index, int total, Map<String, Long> sealMap) {
        Signatory signatory = new Signatory();
        signatory.setTenantType("COMPANY");
        String companyName = conCtSignatoryTrDTO.getSignatory().getName();
        signatory.setTenantName(companyName);
        signatory.setSignOrder(conCtSignatoryTrDTO.getSortNum());
        List<Rect> locations = new ArrayList<>();

        for (Map.Entry<String, String> pair : documents.entrySet()) {
            Rect rect = new Rect();
            String docFileId = pair.getKey();
            String stampPositioningMethod = pair.getValue();
            rect.setFileId(docFileId);
            rect.setRectType("SEAL_CORPORATE");
            if (GenContractStampPositioningMehodDict.KEYWORD.equals(stampPositioningMethod)) {
                // 最后一个文件是编制生成的合同
                rect.setKeyword("方（章）：" + companyName);
                rect.setOffsetX(-0.2);
                rect.setOffsetY(-0.05);
            } else if (GenContractStampPositioningMehodDict.COORDINATE.equals(stampPositioningMethod)) {
                rect.setPage(1);
                rect.setOffsetX(index > 18 ? 0 : index > 12 ? 0.25 : index > 6 ? 0.5 : 0.75);
                rect.setOffsetY(1.0 - 0.15 * (index % 6 + 1));
            } else {
                log.error("buildPartyASignatory invalid method: {}", pair);
            }
            locations.add(rect);
            // 骑缝章
            locations.add(buildAcrossRectForPartyA(index, total, docFileId));
        }
        Action action = new Action();
        action.setActionType("CORPORATE");
        action.setActionName("企业签章" + index);
        action.setSignOrder(1);
        action.setSealId(sealMap.get(companyName) + "");
        action.setLocations(locations);
        signatory.setActions(Collections.singletonList(action));
        return signatory;
    }

    /**
     * 甲方骑缝章
     *
     * @param index
     * @param total
     * @param docFileId
     * @return
     */
    private static Rect buildAcrossRectForPartyA(int index, int total, String docFileId) {
        Rect crossRect = new Rect();
        crossRect.setFileId(docFileId);
        crossRect.setRectType("ACROSS_PAGE");
        double avg = total == 1 ? 0.4 : total == 2 ? 0.3 : total == 3 ? 0.2 : 0.8 / total;
        crossRect.setOffsetY(0.05 + avg * index);
        return crossRect;
    }

    /**
     * 构建契约锁乙方签署信息
     *
     * @param hasEsignPartyBList
     * @param index
     * @param documents
     * @return
     */
    private static Signatory buildPartyBSignatory(List<ConCtSignatoryTrDTO> hasEsignPartyBList, int index, Map<String, String> documents) {
        ConCtSignatoryTrDTO partBSignatory = hasEsignPartyBList.get(0);
        String companyName = partBSignatory.getSignatory().getName();
        Signatory signatory = new Signatory();
        signatory.setTenantType("COMPANY");
        signatory.setTenantName(companyName);
        signatory.setSignOrder(index);
        List<Rect> locations = new ArrayList<>();
        for (Map.Entry<String, String> pair : documents.entrySet()) {

            Rect rect = new Rect();
            String fileId = pair.getKey();
            String stampPositioningMethod = pair.getValue();
            rect.setFileId(fileId);
            rect.setRectType("SEAL_CORPORATE");

            if ("KEYWORD".equals(stampPositioningMethod)) {
                // 最后一个文件是编制生成的合同
                rect.setKeyword("方（章）：" + companyName);
                rect.setOffsetX(-0.2D);
                rect.setOffsetY(-0.05D);
            } else if ("COORDINATE".equals(stampPositioningMethod)) {
                rect.setPage(1);
                rect.setOffsetX(0.1D);
                rect.setOffsetY(0.85D);
            } else {
                log.error("buildPartyBSignatory invalid method: {}", pair);
                continue;
            }
            locations.add(rect);

            locations.add(buildAcrossRectForPartyB(fileId));
        }
        Action action = new Action();
        action.setActionType("CORPORATE");
        action.setActionName("企业签章" + index);
        action.setSignOrder(1);
        action.setLocations(locations);
        signatory.setActions(Collections.singletonList(action));
        return signatory;
    }

    /**
     * 乙方骑缝章
     *
     * @param fileId
     * @return
     */
    private static Rect buildAcrossRectForPartyB(String fileId) {
        // 骑缝章
        Rect crossRect = new Rect();
        crossRect.setFileId(fileId);
        crossRect.setRectType("ACROSS_PAGE");
        crossRect.setOffsetY(0.1D);
        return crossRect;
    }

    /**
     * 创建供应商公司
     * 签署方、电子章的位置信息
     *
     * @param request
     * @return
     */
    public ConCtEsignatureTrDTO createSupplierCompany(ConCtEsignatureTrDTO request) {
        CreateOrgCompanyRequest createOrgCompanyRequest = new CreateOrgCompanyRequest();
        createOrgCompanyRequest.setName(request.getCompanyName());
        createOrgCompanyRequest.setRegisterNo(request.getRegisterSocialCreditCode());
        createOrgCompanyRequest.setLegalPerson(request.getLegalPersonMame());
        createOrgCompanyRequest.setCharger(request.getAgentName());
        createOrgCompanyRequest.setTenantType("COMPANY");
        createOrgCompanyRequest.setMobile(request.getAgentContactDetails());
        createOrgCompanyRequest.setArea("CN");
        createOrgCompanyRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
        String businessLicence = request.getBusinessLicenseScanAttachment();
        File file = null;
        try {
            file = ctOssUtil.downloadFileFromUrl(businessLicence);
            createOrgCompanyRequest.setLicenseFile(file);
            ThirdPartyResponse response = signClient.createCompany(createOrgCompanyRequest);
            if (Objects.nonNull(response) && response.getCode().equals(Integer.valueOf(300))) {
                ThirdPartyResponse<CompanyDetailResponse> companyDetail = signClient.getCompanyDetail(request.getCompanyName(), ThirdPartyChannel.QYS_PRIVATE);
                if (Objects.nonNull(companyDetail) && Objects.nonNull(companyDetail.getData())) {
                    response.setData(companyDetail.getData().getId());
                }
            }
            if (Objects.isNull(response) || Objects.isNull(response.getData())) {
                log.error("创建企业失败: {}", response);
                throw new RuntimeException(response.getMessage());
            }
            // 更新三方公司id
            Long companyId = (Long) response.getData();
            request.setOrgId(companyId + "");
            ConCtEsignatureTrPO conCtEsignatureTrPO = conCtEsignatureTrConverter.dto2Po(request);
            conCtEsignatureTrRepo.updateById(conCtEsignatureTrPO);
        } catch (Exception e) {
            log.error("创建企业失败", e);
            throw new BusinessException(e.getMessage());
        } finally {
            // 删除临时文件
            if (file.exists()) {
                file.delete();
            }
        }
        return request;
    }

    /**
     * 提交认证
     *
     * @param request
     * @return
     */
    public ConCtEsignatureTrDTO submitCompanyAuthInfo(ConCtEsignatureTrDTO request) {
        SubmitCompanyAuthRequest submitAuthRequest = new SubmitCompanyAuthRequest();
        submitAuthRequest.setArea("CN");
        submitAuthRequest.setCompanyType(1L);
        submitAuthRequest.setCompanyName(request.getCompanyName());
        submitAuthRequest.setRegisterNo(request.getRegisterSocialCreditCode());
        submitAuthRequest.setLegalPerson(request.getLegalPersonMame());
        submitAuthRequest.setCharger(request.getAgentName());
        submitAuthRequest.setMobile(request.getAgentContactDetails());
        submitAuthRequest.setCardType(1L);
        submitAuthRequest.setBankCardNo(request.getBankAccount());
        submitAuthRequest.setBankName(request.getBankName());
        submitAuthRequest.setLineNo(request.getBankLinkCode());
        submitAuthRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
        File licenceFile = null;
        File operAuthFile = null;
        try {
            String businessLicenseScanAttachment = request.getBusinessLicenseScanAttachment();
            licenceFile = ctOssUtil.downloadFileFromUrl(businessLicenseScanAttachment);
            submitAuthRequest.setLicenseFile(licenceFile);
            String authPromiseFile = request.getAuthPromiseFile();
            operAuthFile = ctOssUtil.downloadFileFromUrl(authPromiseFile);
            submitAuthRequest.setOperAuthorizationFile(operAuthFile);
            ThirdPartyResponse<String> companyAuthResponse = signClient.submitCompanyAuth(submitAuthRequest);
            if (Objects.isNull(companyAuthResponse)) {
                throw new BusinessException("提交认证失败");
            }
            if (!companyAuthResponse.isSuccess()) {
                throw new BusinessException(companyAuthResponse.getMessage());
            }
            request.setAuthFlowId(companyAuthResponse.getData());
            // 更新认证id到数据库
            ConCtEsignatureTrPO conCtEsignatureTrPO = conCtEsignatureTrConverter.dto2Po(request);
            conCtEsignatureTrRepo.updateById(conCtEsignatureTrPO);
        } catch (Exception e) {
            log.error("企业认证失败", e);
            throw new BusinessException(e.getMessage());
        } finally {
            // 删除临时文件
            if (licenceFile.exists()) {
                licenceFile.delete();
            }
            // 删除临时文件
            if (operAuthFile.exists()) {
                operAuthFile.delete();
            }
        }
        return request;
    }


    public ConCtEsignatureTrDTO checkAuthPayAmount(ConCtEsignatureTrDTO request) {

        Pair<Boolean, String> isAuthFailResult = checkIsCompanyAuthFail(request);
        if (isAuthFailResult.getKey()) {
            updateCtESignatureAuditStatus(request, ConCtEsignatureTrEsignatureStatusDict.FAILED);
            throw new BusinessException(isAuthFailResult.getValue());
        }

        ComfirmBankAccountRequest comfirmBankAccountRequest = new ComfirmBankAccountRequest();
        comfirmBankAccountRequest.setRequestId(request.getAuthFlowId());
        comfirmBankAccountRequest.setAmount(request.getAuthPayAmt().doubleValue());
        comfirmBankAccountRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
        ThirdPartyResponse<Integer> response = signClient.confirmBankAccount(comfirmBankAccountRequest);
        //认证失败，抛出错误原因
        if (response.getCode() != 0) {
            // 更新电子签名状态为认证失败
            throw new BusinessException(response.getMessage());
        }
        //认证成功，更新电子签名状态为认证成功
        updateCtESignatureAuditStatus(request, ConCtEsignatureTrEsignatureStatusDict.SUCCESS);
        return request;
    }

    private void updateCtESignatureAuditStatus(ConCtEsignatureTrDTO request, String failed) {
        LambdaUpdateWrapper<ConCtEsignatureTrPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ConCtEsignatureTrPO::getId, request.getId());
        wrapper.set(ConCtEsignatureTrPO::getEsignatureStatus, failed);
        conCtEsignatureTrRepo.update(null, wrapper);
    }

    /**
     * 检查审核状态，返回检查结果，以及失败原因。如果审核失败了，返回true
     * <p>
     * 以下状态为审核失败，需要重新提交
     * BASE_INFO_REJECT(企业基本信息审核失败)
     * SEAL_AUTH_REJECT(组织机构认证授权书已拒绝
     * ACCOUNT_INFO_REJECT(反向打款失败)
     * <p>
     * https://aliyuque.antfin.com/uhftro/kg7h1z/aoegzhm7ev1znqaa#c7873b64
     *
     * @param request
     */
    private Pair<Boolean, String> checkIsCompanyAuthFail(ConCtEsignatureTrDTO request) {
        try {
            Map<String, String> response = companyService.getComnpanyAuthStatus(request.getCompanyName());
            log.info("response: {}", Joiner.on(",").withKeyValueSeparator(":").join(response));
            String status = response.get("status");
            String reason = response.get("reason");
            log.info("status: {}", status);
            log.info("reason: {}", reason);
            boolean isAuditFail = "BASE_INFO_REJECT".equals(status) || "SEAL_AUTH_REJECT".equals(status) || "ACCOUNT_INFO_REJECT".equals(status);
            return Pair.create(isAuditFail, reason);
        } catch (Exception e) {
            log.error("checkCompanyAuthStatus: {}", request, e);
            throw new BusinessException("系统异常：checkCompanyAuthStatus fail");
        }
    }

    /**
     * 更新签署方式
     *
     * @param request
     * @return
     */
    public ConCtEsignatureTrDTO updateCompanySignWay(ConCtEsignatureTrDTO request) {
        String signatoryWay = request.getSignWay();
        // 原来是ukey签署方式
        int signWayInt = 0;
        if (signatoryWay.equals(ConSignatoryAccountUsedSignTypeDict.UKEY)) {
            signWayInt = 1;
        } else if (signatoryWay.equals(ConSignatoryAccountUsedSignTypeDict.EVENT)) {
            signWayInt = 2;
        } else {
            throw new BusinessException("未知的签署方式");
        }
        // 公司的电子签章
        ConCtEsignatureTrPO conCtEsignatureTrPO = conCtEsignatureTrRepo.selectById(request.getId());
        Long thirdCompanyId;
        if (Strings.isNullOrEmpty(conCtEsignatureTrPO.getOrgId())) {
            // 大部分的orgID都为空，特增加这段兼容逻辑 这里根据公司名称调用契约锁接口获取公司
            ThirdPartyResponse<CompanyDetailResponse> companyDetail = signClient.getCompanyDetail(conCtEsignatureTrPO.getCompanyName(), ThirdPartyChannel.QYS_PRIVATE);
            if (!companyDetail.isSuccess()) {
                throw new BusinessException(companyDetail.getMessage());
            }
            // 获取到qys上的公司ID
            thirdCompanyId = companyDetail.getData().getId();
            conCtEsignatureTrRepo.updateThirdCompanyId(request.getId(), thirdCompanyId);
        } else {
            thirdCompanyId = Long.valueOf(conCtEsignatureTrPO.getOrgId());
        }

        ThirdPartyResponse<String> companySignWay = signClient.getCompanySignWay(thirdCompanyId, null, null, ThirdPartyChannel.QYS_PRIVATE);
        if (companySignWay.isSuccess() && signatoryWay.equals(companySignWay.getData())) {
            // 无需更新
            return request;
        }
        // 根绝qys的公司ID 更新数据三方平台的状态
        ThirdPartyResponse<Boolean> response = signClient.updateCompanySignWay(thirdCompanyId, request.getCompanyName(), signWayInt, ThirdPartyChannel.QYS_PRIVATE);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getMessage());
        }
        return request;
    }

    /**
     * 供应商签署
     *
     * @param request
     * @return
     */
    public ConCtSignatoryTrDTO getSupplierESignUrl(ConCtSigntaskTrDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw new BusinessException("参数为空");
        }
        ConCtSigntaskTrDTO signTaskDetail = signTaskService.getSignTaskDetail(request.getId());
        String signFlowId = signTaskDetail.getSignFlowId();
        List<ConCtSignatoryTrDTO> signatories = signTaskDetail.getSignatories();
        Optional<ConCtSignatoryTrDTO> first = signatories.stream()
                .filter(v -> v.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYB) && v.getSignatoryStatus().equals(ConCtSignatoryTrSignatoryStatusDict.WAITING)).findFirst();
        if (!first.isPresent()) {
            throw new BusinessException("未找到签署方");
        }
        ConCtSignatoryTrDTO conCtSignatoryTrDTO = first.get();
        // 计费逻辑
        GenComTypeCfDTO signatory = conCtSignatoryTrDTO.getSignatory();
        ThirdPartyResponse<CompanyDetailResponse> companyDetail = signClient.getCompanyDetail(signatory.getName(), ThirdPartyChannel.QYS_PRIVATE);
        if (!companyDetail.isSuccess()) {
            throw new BusinessException(companyDetail.getMessage());
        }
        Long companyId = companyDetail.getData().getId();
        ThirdPartyResponse<String> signWayResponse = signClient.getCompanySignWay(companyId, null, null, ThirdPartyChannel.QYS_PRIVATE);
        if (!signWayResponse.isSuccess()) {
            throw new BusinessException(signWayResponse.getMessage());
        }
        String signWay = signWayResponse.getData();
        log.info("getSupplierESignUrl getCompanySignWay: {}", signWayResponse);
        log.info("getSupplierESignUrl getCompanySignWay signWay: {}", signWay);

        LambdaQueryWrapper<ConCtSignatoryAccountTrPO> accountQuery = new LambdaQueryWrapper<>();
        accountQuery.eq(ConCtSignatoryAccountTrPO::getRelEsign, conCtSignatoryTrDTO.geteSign());
        ConCtSignatoryAccountTrPO signatoryAccount = conCtSignatoryAccountTrRepo.selectOne(accountQuery);
        if (Objects.isNull(signatoryAccount)) {
            throw new BusinessException("供应商契约锁账户不存在，请先申请签章，再进行签署！");
        }
        this.checkESignAccount(signWayResponse, signatoryAccount);
        GetSignUrlRequest getSignUrlRequest = new GetSignUrlRequest();
        getSignUrlRequest.setSignFlowId(signFlowId);
        GenComTypeCfPO genComTypeCfPO = MD.queryById(conCtSignatoryTrDTO.getSignatory().getId(), GenComTypeCfPO.class);
        getSignUrlRequest.setOrgName(genComTypeCfPO.getName());
        String mobile = TrantorContext.getCurrentUser().getMobile();
        getSignUrlRequest.setAgentMobile(mobile);
        getSignUrlRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
        try {
            ThirdPartyResponse<GetSignUrlResponse> signUrlResponse = signClient.getSignUrl(getSignUrlRequest);
            if (signUrlResponse.isSuccess()) {
                LambdaUpdateWrapper<ConCtSignatoryTrPO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ConCtSignatoryTrPO::getId, conCtSignatoryTrDTO.getId());
                String signUrl = signUrlResponse.getData().getSignUrl();

                signUrl = signUrl.replace("192.168.32.170", "222.134.48.102");

                updateWrapper.set(ConCtSignatoryTrPO::getSignUrl, signUrl);
                conCtSignatoryTrRepo.update(null, updateWrapper);
                // 更新签署链接
                conCtSignatoryTrDTO.setSignUrl(signUrl);
            }
        } catch (Exception e) {
            log.error("获取签署地址失败", e);
        }
        // 1.创建签署任务
        return conCtSignatoryTrDTO;
    }

    /**
     * 校验付款金额
     *
     * @param signatoryAccount
     * @return
     */
    public void checkEventPayMoney(ConCtSignatoryAccountTrPO signatoryAccount) {
        // 账户余额
        BigDecimal signatoryAccountAmount = signatoryAccount.getSignatoryAccountAmount();
        // 当账户余额＜10/5/0 元时，提醒用户续费充值
        if (signatoryAccountAmount.compareTo(BigDecimal.TEN) < 0 || signatoryAccountAmount.compareTo(BigDecimal.valueOf(5)) < 0 || signatoryAccountAmount.compareTo(BigDecimal.ZERO) < 0) {
            NoticeTaskCreateReq0 req = new NoticeTaskCreateReq0();
            req.setTaskName("通知任务" + System.currentTimeMillis());
            Long userId = ctBaseService.getUserBySupplierCompany(signatoryAccount.getRelCompany());
            req.setNoticeTargetIds(Collections.singletonList(userId));
            req.setNoticeBusinessCode(eventNoticeTemplate);
            noticeService.createTaskByTemplate(req);
        }
        // 签约一次一块钱 我们需要先看一下还有没有一块钱
        if (signatoryAccountAmount.compareTo(new BigDecimal(1)) < 0) {
            throw new BusinessException("余额不足。您当前的签署方式为事件型签署，请在付费管理画面选择【事件型签署预付款】完成充值");
        }
    }

    public GenCtHeadTrExtDTO generatePdfByCt(GenCtHeadTrExtDTO request) {
        // 合同签约乙方
        Long prtnB = request.getPrtnB();
        GenVendInfoMdPO genVendInfoMdPO = MD.queryById(prtnB, GenVendInfoMdPO.class);
        String vendName = genVendInfoMdPO.getName();
        // 查询合同类型
        String btClass = request.getBtClass();
        // 设置展示值到extra的title字段
        String btClassDisplay = getBtClassDisplay(btClass);
        // 签署公司名称
        GenComTypeCfPO genComTypeCfPO = genComTypeCfRepo.selectById(request.getPrtnA());
        // * 合同审批(业务类型-供方名称-需方名称-合同号)
        request.setIdentifier(btClassDisplay + "-" + vendName + "-" + genComTypeCfPO.getName() + "-" + request.getCtCode());
        return request;
    }


    /**
     * 检查账户是否在有效期内，余额是否足够
     *
     * @param signWayResponse
     * @param signatoryAccount
     */
    public void checkESignAccount(ThirdPartyResponse<String> signWayResponse, ConCtSignatoryAccountTrPO signatoryAccount) {
        // UKEY签署方式，需要校验是否在有效期内
        if (ConSignatoryAccountUsedSignTypeDict.UKEY.equals(signWayResponse.getData())) {
            if (Objects.isNull(signatoryAccount.getSignatoryAccountLoseTime()) || signatoryAccount.getSignatoryAccountLoseTime().isBefore(LocalDateTime.now())) {
                throw new BusinessException("供应商契约锁账户UKEY已失效，请充值后进行签署！");
            }
        } else if (ConSignatoryAccountUsedSignTypeDict.EVENT.equals(signWayResponse.getData())) {
            // 事件签署方式，需要校验金额和添加扣费记录
            // 校验金额是否足够支付
            checkEventPayMoney(signatoryAccount);
        } else {
            throw new BusinessException("未知的签署方式");
        }
    }

    /**
     * 在PDF上添加印章
     *
     * @param inputStream    PDF输入流
     * @param sealImageBytes 印章图片字节数组
     * @param companyName    公司名称
     * @param signFile       签署文件信息
     */
    private void addSealToPdf(InputStream inputStream, byte[] sealImageBytes, String companyName, ConCtSignfileTrDTO signFile) {
        try {
            // 定义搜索关键字，按优先级排序
            List<String> keywords = Arrays.asList(
                    "需方（章）",
                    "需方(章)",
                    "甲方（章）",
                    "甲方(章)"
            );

            // 重新创建输入流用于搜索（因为流只能读取一次）
            byte[] pdfBytes = readInputStreamToBytes(inputStream);
            InputStream searchStream = new ByteArrayInputStream(pdfBytes);

            // 搜索关键字位置
            PDFKeywordSearchUtil.KeywordPosition position = PDFKeywordSearchUtil.searchKeywordsFromLastPage(
                    searchStream, keywords, companyName);

            if (position == null) {
                // 如果没有找到关键字，使用最后一页的默认位置
                InputStream defaultStream = new ByteArrayInputStream(pdfBytes);
                position = PDFKeywordSearchUtil.getLastPageDefaultPosition(defaultStream);
                log.info("未找到关键字，将在最后一页默认位置盖章: {}", position);
            } else {
                log.info("找到关键字，将在指定位置盖章: {}", position);
            }

            if (position != null) {
                // 在指定位置添加印章
                InputStream sealStream = new ByteArrayInputStream(pdfBytes);
                addSealAtPosition(sealStream, sealImageBytes, position, signFile);
            } else {
                log.error("无法确定盖章位置");
                throw new BusinessException("无法确定盖章位置");
            }

        } catch (Exception e) {
            log.error("处理PDF盖章时发生错误", e);
            throw new BusinessException("PDF盖章处理失败: " + e.getMessage());
        }
    }

    /**
     * 在指定位置添加印章
     */
    private void addSealAtPosition(InputStream inputStream, byte[] sealImageBytes,
                                   PDFKeywordSearchUtil.KeywordPosition position, ConCtSignfileTrDTO signFile) {
        try {

            // 创建输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 处理PDF并添加印章
            PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputStream), new PdfWriter(outputStream));
            Document document = new Document(pdfDoc);

            // 创建印章图片
            Image sealImage = new Image(ImageDataFactory.create(sealImageBytes));

            // 设置印章大小（可以根据需要调整）
            float sealWidth = 80f;
            float sealHeight = 80f;
            sealImage.setWidth(sealWidth);
            sealImage.setHeight(sealHeight);

            // 计算印章位置
            float x = position.getX() + 50; // 向右偏移50个点
            float y = position.getY() - 40; // 向下偏移40个点

            // 设置印章位置
            sealImage.setFixedPosition(position.getPageNumber(), x, y);

            // 添加印章到文档
            document.add(sealImage);

            // 关闭文档
            document.close();
            pdfDoc.close();

            // 上传处理后的PDF
            byte[] processedPdfBytes = outputStream.toByteArray();
            String newFileName = signFile.getFileName() + "_sealed.pdf";
            String newFileUrl = ctOssUtil.uploadWithFileName(
                    new ByteArrayInputStream(processedPdfBytes), newFileName, "application/pdf");

            // 更新签署文件信息
            signFile.setSignedFile(newFileUrl.replaceAll("\\?.*", ""));
            log.info("成功为文件 {} 添加印章，新文件: {}", signFile.getFileName(), newFileUrl);

        } catch (Exception e) {
            log.error("在指定位置添加印章时发生错误", e);
            throw new BusinessException("添加印章失败: " + e.getMessage());
        }
    }

    /**
     * 下载印章图片（只下载一次）
     */
    private byte[] downloadSealImage(GenComGraphicSealCfPO sealConfig) {
        try {
            // 获取印章图片URL
            String sealImageUrl = sealConfig.getGraphicSeal();
            if (Strings.isNullOrEmpty(sealImageUrl)) {
                throw new BusinessException("印章图片不存在");
            }

            // 下载印章图片
            String bucket = ctOssUtil.getBucket(true);
            String sealObjectName = FileHelper.getOSSUrlObjectName(sealImageUrl, bucket);
            InputStream sealImageStream = cloudClient.downloadFile(bucket, sealObjectName);
            byte[] sealImageBytes = readInputStreamToBytes(sealImageStream);

            log.info("成功下载印章图片，大小: {} bytes", sealImageBytes.length);
            return sealImageBytes;

        } catch (Exception e) {
            log.error("下载印章图片失败", e);
            throw new BusinessException("下载印章图片失败: " + e.getMessage());
        }
    }

    /**
     * 将输入流转换为字节数组
     */
    private byte[] readInputStreamToBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        return buffer.toByteArray();
    }
}
