package io.terminus.erp.contract.domain.util;

import com.google.common.base.Throwables;
import io.terminus.common.api.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;

/**
 * @author: xu<PERSON>i<PERSON>ie
 * @create: 2019/11/27 09:52:57
 **/
@Slf4j
public class FileHelper {

    public static File byteToFile(byte[] bytes, String type) {
        File file;
        OutputStream output = null;
        try {
            file = File.createTempFile("tmp_signed_file" + System.currentTimeMillis(), "." + type);
            output = Files.newOutputStream(file.toPath());
            BufferedOutputStream bufferedOutput = new BufferedOutputStream(output);
            bufferedOutput.write(bytes);

            bufferedOutput.close();
        } catch (IOException e) {
            log.error("字节转换文件失败， error: {}", Throwables.getStackTraceAsString(e));
            throw new RuntimeException("Bytes convert to file fail");
        } finally {
            if (output != null) {
                try {
                    output.close();
                } catch (IOException e) {
                    log.error("文件流关闭时异常,error : {}", Throwables.getStackTraceAsString(e));
                }
            }
        }
        return file;
    }


    /**
     * summary:将流转化为字节数组
     *
     * fixme：需要关闭inputStream
     */
    public static byte[] toByteArray(InputStream inputStream) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024 * 4];
        byte[] result;
        try {
            int n = 0;
            while ((n = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, n);
            }
            result = out.toByteArray();
        } catch (IOException e) {
            log.error("文件流转换字节数组失败,error : {}", Throwables.getStackTraceAsString(e));
            throw new RuntimeException("Stream converter to byte array fail");
        } finally {
            try {
                out.close();
            } catch (IOException e) {
                log.error("文件流关闭时异常,error : {}", Throwables.getStackTraceAsString(e));
            }
        }
        return result;
    }

    /**
     * inputStream 转 File
     * <p>
     * 需要 删除临时创建的文件
     */
    public static File inputStreamtoFile(InputStream inputStream, String fileType) throws IOException {
        File file = File.createTempFile(System.currentTimeMillis() + "" + Math.random(), "." + fileType);
        OutputStream os = Files.newOutputStream(file.toPath());
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
            os.write(buffer, 0, bytesRead);
        }
        os.close();
        inputStream.close();
        return file;

    }

    /**
     * 将网络文件转 File
     */
    public static File transformUrlToAttachment(String urlStr, String type) {
        InputStream inputStream = getInputStreamByUrl(urlStr);
        if(inputStream == null){
            log.error("读取文件失败: {}", urlStr);
            return null;
        }
        byte[] bytes;
        File file;
        bytes = toByteArray(inputStream);
        file = byteToFile(bytes, type);
        try {
            inputStream.close();
        } catch (IOException e) {
            log.error("close inputStream fail: {}", urlStr, e);
        }
        return file;
    }

    /**
     * 将网络文件转 File
     */
    public static File transformUrlToAttachment(String urlStr) {
        String chineseUrl = getChineseUrl(urlStr);
        String type = getUrlFileExtension(chineseUrl);
        InputStream inputStream = getInputStreamByUrl(chineseUrl);
        if(inputStream == null){
            log.error("读取文件失败: {}", urlStr);
            return null;
        }
        byte[] bytes;
        File file;
        bytes = toByteArray(inputStream);
        file = byteToFile(bytes, type);
        try {
            inputStream.close();
        } catch (IOException e) {
            log.error("close inputStream fail: {}", urlStr, e);
        }
        return file;
    }


    /**
     * 将网络文件转 File
     */
    public static String getUrlFileExtension(String urlStr) {
        URL url;
        try {
            url = new URL(urlStr);
            String fileName = url.getFile();
            return fileName.substring(fileName.lastIndexOf("."));
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取网络文件的输入流
     * 注意：调用方需要正确关闭inputStream
     */
    public static InputStream getInputStreamByUrl(String urlStr) {
        DataInputStream in = null;
        try {
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestProperty("Content-Type", "utf-8");
            in = new DataInputStream(conn.getInputStream());
        } catch (IOException e) {
            log.error("url转换输入流失败,错误信息{}", e.getMessage());
        }
        return in;
    }

    public static String getChineseUrl(String fileUrl){
        String dowloadUrl = fileUrl;
        int indexOf = fileUrl.lastIndexOf("/")+1;
        fileUrl = fileUrl.substring(indexOf);
        try {
            for (char c : fileUrl.toCharArray()) {
                if(isChinese(c)) {
                    fileUrl = fileUrl.replace(c + "", URLEncoder.encode(c + "", "UTF-8"));
                }
                if(c == ' ') {
                    fileUrl = fileUrl.replace(c + "", "%20");
                }
            }
        } catch (UnsupportedEncodingException e) {
            return fileUrl;
        }
        dowloadUrl = dowloadUrl.substring(0,indexOf) + fileUrl;
        return dowloadUrl;
    }

    public static boolean isChinese(char c) {
        return (c >= '\u4e00' && c <= '\u9fa5');
    }

    public static String getOSSUrlObjectName(String urlStr,String bucketName){
        try {
            URL uri = new URL(urlStr);
            String path = uri.getPath();
            if (path.startsWith("/")) {
                path = path.substring(1); // 去除路径的前斜杠（/）
            }
            path = path.replaceFirst(bucketName, "");
            if (path.startsWith("/")) {
                path = path.substring(1); // 去除路径的前斜杠（/）
            }
            return path;
        } catch (MalformedURLException e) {
            log.error("url转换文件名失败,错误信息{}", e.getMessage());
            return null;  // 处理异常情况
        }
    }


    public static String parseSignedUrl(String url) {
        int index = url.indexOf("?"); // 查找问号的位置
        if (index != -1) {
            return url.substring(0, index); // 截取问号之前的部分
        } else {
            return url; // 如果没有问号，直接返回原字符串
        }
    }

    public static void main(String[] args) {
        String str = "https://minio-tenant.inc.ruixinzb.com/terminus-new-trantor/trantor2/portal/TERP/6c66dcaf-2954-496a-88cd-9e90a30a9721/dd (1).docx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241017T093742Z&X-Amz-SignedHeaders=host&X-Amz-Expires=3600&X-Amz-Credential=minio%2F20241017%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=4adeed46da05f9d07f87f95386c64510b2b16f32cffe936b1b02d2b28d7310fe";
        String ossUrlObjectName = getOSSUrlObjectName(str, "terminus-new-trantor");
        System.out.println(ossUrlObjectName);



    }


}
