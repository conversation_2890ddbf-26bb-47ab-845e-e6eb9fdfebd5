package io.terminus.erp.contract.domain.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.erp.contract.domain.properties.WXPayProperties;
import io.terminus.erp.contract.domain.util.WXPayUtil;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtEsignatureTrRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSignatoryAccountTrRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConSignatoryAccountRechargeRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ESignRechargePackRepo;
import io.terminus.erp.contract.spi.convert.tp.ConCtSignatoryAccountTrConverter;
import io.terminus.erp.contract.spi.convert.tp.ConSignatoryAccountRechargeConverter;
import io.terminus.erp.contract.spi.dict.tp.*;
import io.terminus.erp.contract.spi.model.tp.dto.*;
import io.terminus.erp.contract.spi.model.tp.po.ConCtEsignatureTrPO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignatoryAccountTrPO;
import io.terminus.erp.contract.spi.model.tp.po.ConSignatoryAccountRechargePO;
import io.terminus.erp.contract.spi.model.tp.po.ESignRechargePackPO;
import io.terminus.erp.md.infrastructure.repo.mat.GenMatMdRepo;
import io.terminus.erp.md.spi.model.dto.ct.CtItemTrDTO;
import io.terminus.erp.md.spi.model.po.mat.GenMatMdPO;
import io.terminus.trantor2.module.service.OSSService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 签署账户服务
 *
 * @className: CtSignAccountService
 * @author: charl
 * @date: 2023/11/6 09:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CtSignAccountService {

    private final IdGenerator idGenerator;

    private final WXPayProperties wxPayProperties;

    private final ConCtSignatoryAccountTrRepo conCtSignatoryAccountTrRepo;

    private final ConCtSignatoryAccountTrConverter conCtSignatoryAccountTrConverter;

    private final ConCtEsignatureTrRepo conCtEsignatureTrRepo;

    private final ConSignatoryAccountRechargeRepo conSignatoryAccountRechargeRepo;

    private final ConSignatoryAccountRechargeConverter conSignatoryAccountRechargeConverter;

    private final ESignRechargePackRepo eSignRechargePackRepo;

    private final OSSService ossService;

    private final GenMatMdRepo genMatMdRepo;

    private final static String BUCKET_NAME = "pay_qrcode_img";

    /**
     * 创建支付账户
     *
     * @param request
     * @return
     */
    public ConCtSignatoryAccountTrDTO createPaymentAccount(ConCtEsignatureTrDTO request) {
        final Long esignId = request.getId();
        if (Objects.isNull(esignId)) {
            return null;
        }

        // 创建支付账户
        ConCtEsignatureTrPO esign = conCtEsignatureTrRepo.selectById(esignId);

        ConCtSignatoryAccountTrPO theNewSignatoryAccount = buildSignatoryAccount(esign);

        ConCtSignatoryAccountTrPO theExistedSignatoryAccount = selectSignatoryAccount(esignId);
        // 更新账户信息
        if (Objects.nonNull(theExistedSignatoryAccount)) {
            theNewSignatoryAccount.setId(theExistedSignatoryAccount.getId());
            conCtSignatoryAccountTrRepo.updateById(theNewSignatoryAccount);
            return conCtSignatoryAccountTrConverter.po2Dto(theNewSignatoryAccount);
        }

        conCtSignatoryAccountTrRepo.insert(theNewSignatoryAccount);
        esign.setRelSignAccount(theNewSignatoryAccount.getId());
        conCtEsignatureTrRepo.updateById(esign);
        return conCtSignatoryAccountTrConverter.po2Dto(theNewSignatoryAccount);
    }

    private ConCtSignatoryAccountTrPO buildSignatoryAccount(ConCtEsignatureTrPO conCtEsignatureTrPO) {
        ConCtSignatoryAccountTrPO conCtSignatoryAccountTrPO = new ConCtSignatoryAccountTrPO();
        String code = idGenerator.nextCode(ConCtSignatoryAccountTrPO.class, conCtSignatoryAccountTrPO);
        conCtSignatoryAccountTrPO.setCode(code);
        conCtSignatoryAccountTrPO.setRelCompany(conCtEsignatureTrPO.getEntity());
        conCtSignatoryAccountTrPO.setRelEsign(conCtEsignatureTrPO.getId());
        conCtSignatoryAccountTrPO.setSignatoryAccountStatus(ConCtSignatoryAccountTrSignatoryAccountStatusDict.ACTIVE);
        conCtSignatoryAccountTrPO.setType(ConCtSignatoryAccountTrTypeDict.NONE);
        conCtSignatoryAccountTrPO.setSignatoryAccountAmount(BigDecimal.ZERO);
        conCtSignatoryAccountTrPO.setSignatoryAccountAmountUsed(BigDecimal.ZERO);
        if(conCtEsignatureTrPO.getSignWay().equals(ConCtEsignatureTrSignWayDict.UKEY)) {
            conCtSignatoryAccountTrPO.setType(ConCtSignatoryAccountTrTypeDict.UKEY);
        }
        return conCtSignatoryAccountTrPO;
    }

    /**
     * 根据电子签章查询签章账户
     * @param esignId
     * @return
     */
    private ConCtSignatoryAccountTrPO selectSignatoryAccount(long esignId) {
        LambdaQueryWrapper<ConCtSignatoryAccountTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSignatoryAccountTrPO::getRelEsign, esignId);
        return conCtSignatoryAccountTrRepo.selectOne(queryWrapper);
    }

    /**
     * 创建微信支付单
     *
     * @param request
     * @return
     */
    public ConSignatoryAccountRechargeDTO createPaymentOrder(ConSignatoryAccountRechargeDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getRechargePackRef())) {
            throw new BusinessException("参数异常，请联系技术人员处理。");
        }
        // 查询支付套餐金额
        Long rechargePackRef = request.getRechargePackRef();
        ESignRechargePackPO eSignRechargePackPO = eSignRechargePackRepo.selectById(rechargePackRef);
        if (Objects.isNull(eSignRechargePackPO)) {
            throw new BusinessException("没有选择充值金额。");
        }
        ConSignatoryAccountRechargePO accountRechargePO = conSignatoryAccountRechargeConverter.dto2Po(request);
        // 支付金额（元）
        BigDecimal payAmt = eSignRechargePackPO.getAmt();
        accountRechargePO.setOrderType(ConSignatoryAccountRechargeOrderTypeDict.PREPAY);
        // UKey的签署方式，需要判断是否首年
        if (eSignRechargePackPO.getType().equals(ESignRechargePackTypeDict.UKEY)) {
            LambdaQueryWrapper<ConSignatoryAccountRechargePO> accountRechargeQuery = new LambdaQueryWrapper<>();
            accountRechargeQuery.eq(ConSignatoryAccountRechargePO::getRelSignAccount, request.getRelSignAccount());
            accountRechargeQuery.eq(ConSignatoryAccountRechargePO::getOrderType, ConSignatoryAccountRechargeOrderTypeDict.INITIAL_COSTS);
            accountRechargeQuery.eq(ConSignatoryAccountRechargePO::getOrderStatus, ConSignatoryAccountRechargeOrderStatusDict.PAID);
            List<ConSignatoryAccountRechargePO> conSignatoryAccountRechargePOS = conSignatoryAccountRechargeRepo.selectList(accountRechargeQuery);
            accountRechargePO.setOrderType(ConSignatoryAccountRechargeOrderTypeDict.ANNUAL_FEE_RENEWAL);
            payAmt = BigDecimal.valueOf(150);
            if (CollectionUtils.isEmpty(conSignatoryAccountRechargePOS)) {
                accountRechargePO.setOrderType(ConSignatoryAccountRechargeOrderTypeDict.INITIAL_COSTS);
                payAmt = BigDecimal.valueOf(300);
            }
        }
        // 保存签署记录
        String code = idGenerator.nextCode(ConSignatoryAccountRechargePO.class, accountRechargePO);
        accountRechargePO.setOrderCode(code);
        accountRechargePO.setOrderStatus(ConSignatoryAccountRechargeOrderStatusDict.INCOMING);
        accountRechargePO.setOrderSum(payAmt);
        accountRechargePO.setOrderTime(LocalDateTime.now());
        conSignatoryAccountRechargeRepo.insert(accountRechargePO);
        String name = eSignRechargePackPO.getName();
        // 发起微信预支付
        Map<String, String> payResultMap = WXPayUtil.createWxPay(wxPayProperties.getAppId(), wxPayProperties.getMchId(), wxPayProperties.getKey(), payAmt, name, code, wxPayProperties.getNotifyUrl());
        log.info("invoke wx pay result:{}", JSONUtil.toJsonStr(payResultMap));
        String returnCode = payResultMap.get("return_code");
        String resultCode = payResultMap.get("result_code");
        if (!returnCode.equalsIgnoreCase("SUCCESS") || !resultCode.equalsIgnoreCase("SUCCESS")) {
            throw new BusinessException("微信支付失败，错误原因：" + payResultMap.get("return_msg"));
        }
        // 更新预支付链接
        String codeUrl = payResultMap.get("code_url");
        accountRechargePO.setPrepayUrl(codeUrl);
        conSignatoryAccountRechargeRepo.updateById(accountRechargePO);
        return conSignatoryAccountRechargeConverter.po2Dto(accountRechargePO);
    }


    /**
     * 创建二维码支付
     * @param request
     * @return
     */
    public ConSignatoryAccountRechargeDTO createPaymentQRCode(ConSignatoryAccountRechargeDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getPrepayUrl())) {
            throw new BusinessException("预支付url为空");
        }
        String prepayUrl = request.getPrepayUrl();
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);
        hints.put(EncodeHintType.MARGIN, 0);
        BitMatrix bitMatrix;
        try {
            bitMatrix = new MultiFormatWriter().encode(prepayUrl, BarcodeFormat.QR_CODE, 300, 300, hints);
        } catch (WriterException e) {
            throw new RuntimeException(e);
        }
        String fullFileName = request.getId() + ".jpg";
        String fileUrl = "";
        try {
            Path filePath = Paths.get(fullFileName);
            OutputStream outputStream = Files.newOutputStream(filePath);
            MatrixToImageWriter.writeToStream(bitMatrix, "jpg", outputStream);
            InputStream inputStream = Files.newInputStream(filePath);
            fileUrl = ossService.uploadFileAndGetUrl(BUCKET_NAME, fullFileName, inputStream, "application/octet-stream", false);
        } catch (Exception e) {
            log.error("CtSignAccountService 上传文件错误 {}", Throwables.getStackTraceAsString(e));
        } finally {
            File file = new File(fullFileName);
            if (file.exists()) {
                if (file.delete()) {
                    log.info("CtSignAccountService 文件删除成功");
                } else {
                    log.info("CtSignAccountService 文件删除失败");
                }
            }
        }
        // 更新二维码链接
        ConSignatoryAccountRechargePO conSignatoryAccountRechargePO = conSignatoryAccountRechargeConverter.dto2Po(request);
        conSignatoryAccountRechargePO.setPayLink(fileUrl);
        conSignatoryAccountRechargeRepo.updateById(conSignatoryAccountRechargePO);
        request.setPayLink(fileUrl);
        return request;
    }

    public ConSignatoryAccountRechargeDTO queryPaymentDetail(ConSignatoryAccountRechargeDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw new BusinessException("参数为空");
        }

        ConSignatoryAccountRechargePO conSignatoryAccountRechargePO = conSignatoryAccountRechargeRepo.selectById(request.getId());
        return conSignatoryAccountRechargeConverter.po2Dto(conSignatoryAccountRechargePO);
    }

    /**
     * 下载契约锁认证资料
     * @return response
     */
    public ContractDownloadDTO downloadCertificationAction() {
        ContractDownloadDTO contractDownloadDTO = new ContractDownloadDTO();
        String url = "http://minio-tenant.inc.ruixinzb.com/terminus-new-trantor/trantor2/portal/组织机构认证承诺书.docx";
        contractDownloadDTO.setUrl(ossService.getFileUrl(url, true));
        return contractDownloadDTO;
    }

    /**
     * 合同行导入
     * @param contractDownloadDTO 文件链接
     * @return 导入后的文件
     */
    public GenCtHeadTrExtDTO importContractLine(ContractDownloadDTO contractDownloadDTO) {
        if (Objects.isNull(contractDownloadDTO.getFile())) {
            throw new BusinessException("请上传文件");
        }
        // 获取文件名字
        String fileName = contractDownloadDTO
                .getFile()
                .substring(contractDownloadDTO.getFile().lastIndexOf("trantor2"));
        // 获取文件输入流
        InputStream inputStream = ossService.migrateFileOut(fileName, true);
        // 读取文件
        ImportContractLineListener listener = new ImportContractLineListener();
        EasyExcel
            .read(inputStream, ImportContractExcelParams.class, listener)
            .sheet(0)
            .doRead();
        // 获取文件数据
        List<ImportContractExcelParams> importContractExcelParams = listener.getCtItemTrDTOS();
        // 获取物料信息
        Map<String, GenMatMdPO> genMatMdPOMap = findGenMatMdPO(importContractExcelParams);
        List<CtItemTrDTO> ctItemTrDTOS = new ArrayList<>();
        for (ImportContractExcelParams importContractExcelParam : importContractExcelParams) {
            if (!genMatMdPOMap.containsKey(importContractExcelParam.getMatCode())) {
                throw new BusinessException("物料编码 " + importContractExcelParam.getMatCode() + " 不存在");
            }
            GenMatMdPO genMatMdPO = genMatMdPOMap.get(importContractExcelParam.getMatCode());
            CtItemTrDTO ctItemTrDTO = getCtItemTrDTO(importContractExcelParam, genMatMdPO);
            ctItemTrDTOS.add(ctItemTrDTO);
        }
        GenCtHeadTrExtDTO genCtHeadTrExtDTO = new GenCtHeadTrExtDTO();
        genCtHeadTrExtDTO.setCtItemList(ctItemTrDTOS);
        return genCtHeadTrExtDTO;
    }

    /**
     * 生成合同清单明细
     * @param importContractExcelParam 导入数据
     * @param genMatMdPO 物料信息
     * @return 合同明细行
     */
    private static CtItemTrDTO getCtItemTrDTO(ImportContractExcelParams importContractExcelParam, GenMatMdPO genMatMdPO) {
        CtItemTrDTO ctItemTrDTO = new CtItemTrDTO();
        ctItemTrDTO.setMatCode(genMatMdPO.getId());
        ctItemTrDTO.setTaxRate(new BigDecimal(importContractExcelParam.getTax()));
        ctItemTrDTO.setCtMatAmt(importContractExcelParam.getAmt());
        HashMap<String, Object> ext = new HashMap<>();
        ext.put(GenCtItemTrExtDTO.Fields.extWqDeliveryData, importContractExcelParam.getDateDelivery().toString());
        ext.put(GenCtItemTrExtDTO.Fields.extWqCtTotalQty, importContractExcelParam.getQty());
        ext.put(GenCtItemTrExtDTO.Fields.extWqWarranty, importContractExcelParam.getGuaranteePeriod().longValue());
        ext.put(GenCtItemTrExtDTO.Fields.extWqCtRemark, importContractExcelParam.getRemark());
        ctItemTrDTO.setExtra(ext);
        return ctItemTrDTO;
    }

    /**
     * 根据物料编码获取
     * @param importContractExcelParams 导入数据
     * @return 物料编码
     */
    public Map<String, GenMatMdPO> findGenMatMdPO(List<ImportContractExcelParams> importContractExcelParams) {
        // 判断数据是否为空
        if (CollectionUtils.isEmpty(importContractExcelParams)) {
            return new HashMap<>();
        }
        List<String> matCodeList = importContractExcelParams.stream().map(ImportContractExcelParams::getMatCode).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<GenMatMdPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GenMatMdPO::getMatCode, matCodeList);
        List<GenMatMdPO> genMatMdPOS = genMatMdRepo.selectList(wrapper);
        if (CollectionUtils.isEmpty(genMatMdPOS)) {
            return new HashMap<>();
        }
        return genMatMdPOS.stream().collect(Collectors.toMap(GenMatMdPO::getMatCode, Function.identity()));
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    static class ImportContractLineListener extends AnalysisEventListener<ImportContractExcelParams> {

        private List<ImportContractExcelParams> ctItemTrDTOS = new ArrayList<>();

        @Override
        public void invoke(ImportContractExcelParams importContractExcelParams, AnalysisContext analysisContext) {
            if (Objects.isNull(importContractExcelParams.getMatCode())) {
                throw new BusinessException("物料编码为空");
            }
            // 税率不填默认给13
            if (Objects.isNull(importContractExcelParams.getTax())) {
                importContractExcelParams.setTax("13");
            }
            if (Objects.isNull(importContractExcelParams.getAmt())) {
                throw new BusinessException("物料单价不能为空");
            }
            if (Objects.isNull(importContractExcelParams.getQty())) {
                throw new BusinessException("数量必填");
            }
            ctItemTrDTOS.add(importContractExcelParams);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {

        }
    }

}
