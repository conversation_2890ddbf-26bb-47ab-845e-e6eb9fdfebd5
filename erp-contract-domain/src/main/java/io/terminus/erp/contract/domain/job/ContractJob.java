package io.terminus.erp.contract.domain.job;

import io.terminus.common.scheduler.annotation.Job;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: ContractJob
 * @author: charl
 * @date: 2024/1/18 14:58
 */

@Slf4j
@RestController
@RequiredArgsConstructor
public class ContractJob {

    @Deprecated
    @Job(key = "CT_STATE_TASK_JOB", name = "定时扫描合同列表，切换生效/失效的合同，并发送mq消息")
    public void loopContractEnable() {
        log.info("合同生效失效定时任务begin");
    }

}
