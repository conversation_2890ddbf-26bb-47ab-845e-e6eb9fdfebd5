package io.terminus.erp.contract.domain.util;

import cn.hutool.core.date.StopWatch;
import io.terminus.trantor2.module.service.OSSService;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import com.itextpdf.html2pdf.HtmlConverter;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;

/**
 * @className: HtmlToPDFUtil
 * @author: charl
 * @date: 2023/9/26 15:23
 */
@Slf4j
public class HtmlToPDFUtil {

    /**
     * 根据html生成pdf
     *
     * @param html
     * @return
     */
    public static String html2PDF(String html, String bucketName, String fileName, OSSService ossService) {
        final String ossFullFileName = fileName + ".pdf";
        final String tmpFileName = "html2PDF_" + java.util.UUID.randomUUID() + ".pdf";
        String fileUrl = "";
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("转换富文本为文件流");
            OutputStream os = Files.newOutputStream(Paths.get(tmpFileName));
            PDFUtil.convertToPdf(html, os);
            stopWatch.stop();
            log.info("richTextToPdf html2PDF :{}", stopWatch.prettyPrint());
            // 临时文件上传oss
            stopWatch.start("文件上传oss");
            fileUrl = ossService.uploadFileAndGetUrl(bucketName, ossFullFileName, new FileInputStream(tmpFileName), "application/pdf", null);
            stopWatch.stop();
            log.info("richTextToPdf to oss :{}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("html to pdf error", e);
        } finally {
            File file = new File(tmpFileName);
            if (file.exists()) {
                file.delete();
            }
        }
        return fileUrl;
    }

    /**
     * 使用itextpdf将本地html文件转换为pdf，支持分页，效率高
     * @param htmlFilePath 本地html文件路径
     * @param pdfOutputPath 输出pdf文件路径
     */
    public static void html2PdfTest(String htmlFilePath, String pdfOutputPath) {
        long start = System.currentTimeMillis();
        try {
            // 读取html文件内容
            String html = new String(Files.readAllBytes(Paths.get(htmlFilePath)), StandardCharsets.UTF_8);
            // 转换为pdf
            PDFUtil.convertToPdf(html, new FileOutputStream(pdfOutputPath));
            long end = System.currentTimeMillis();
            System.out.println("HTML转PDF完成，耗时: " + (end - start) + " ms");
            System.out.println("PDF输出路径: " + pdfOutputPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 可选：main方法，方便直接运行测试
    public static void main(String[] args) {
        String htmlPath = "/Users/<USER>/Downloads/ueditortest.html"; // 本地html文件路径
        String pdfPath = "/Users/<USER>/Downloads/test_output_ueditor.pdf"; // 输出pdf路径
        html2PdfTest(htmlPath, pdfPath);
    }
}
