package io.terminus.erp.contract.domain.util;

import java.io.IOException;
import java.net.URL;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

/**
 * @className: ResourceUtil
 * @author: charl
 * @date: 2023/11/6 16:38
 */
@Component
public class ResourceUtil {

    private final ResourceLoader resourceLoader;

    public ResourceUtil(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    public String getResourceFilePath(String resourceName) {
        Resource resource = resourceLoader.getResource("classpath:" + resourceName);
        URL url = null;
        try {
            url = resource.getURL();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return url.getPath();

    }
}