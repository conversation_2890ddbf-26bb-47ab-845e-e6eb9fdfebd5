package io.terminus.erp.contract.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @className: WXPayConfig
 * @author: charl
 * @date: 2023/11/6 15:21
 */

@Data
@ConfigurationProperties(prefix = WXPayProperties.PREFIX)
public class WXPayProperties {

    public static final String PREFIX = "terminus.pay.wx";//这里注意前缀一律小写，这是规范

    /**
     * 公众号ID
     */
    private String appId = "wx3630da2b96410b61";

    /**
     * 商户号
     */
    private String mchId = "1601850574";

    /**
     * API密钥
     */
    private String key = "qh4dguoe70qNTvfEADq2zDJoAGmn7taj";

    /**
     * 回调地址
     */
    private String notifyUrl;

}
