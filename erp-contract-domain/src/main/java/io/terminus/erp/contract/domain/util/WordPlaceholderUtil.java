package io.terminus.erp.contract.domain.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.cloud.storage.core.client.CloudClient;
import io.terminus.erp.contract.domain.service.CtOssUtil;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.imageio.ImageIO;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ooxml.POIXMLDocument;
import org.apache.poi.ooxml.util.PackageHelper;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WordPlaceholderUtil {

    public static final String preFix = "{{";

    public static final String suffex = "}}";

    private static final String PATTERN = "(\\{\\{.*?\\}\\})";

    private static final String TABLE_PATTERN = "(\\{\\{\\<.+?\\>\\[.*?\\])(.+?)(-.*?\\}\\})";
    private static final String TABLE_PATTERN_2 = "(\\{\\{\\<.+?\\>\\[.*?\\])(.+?)(-.*?\\}\\})";

    private static final Pattern pattern = Pattern.compile(PATTERN);

    @Value("${cloud.storage.publicBucket:terminus-new-trantor}")
    private String bucketName;

    @Autowired
    private CloudClient cloudClient;

    @Autowired
    private CtOssUtil ctOssUtil;

    /**
     * 合同类型
     */
    private String docxType;

    /**
     * key: 未解析字段 value: 字段类型
     */
    private Map<String, String> fieldTypeMap;

    /**
     * 是否覆盖全部标记
     */
    public Boolean isReplaceAll = true;


    /**
     * 按照指定前缀、后缀从word文档中解析符合规范的占位符集合
     *
     * @param inputStream       待解析的文件流
     * @param placeholderPrefix 占位符前缀
     * @param placeholderSuffix 占位符后缀
     * @return Map<解析后的字段, 未解析的字段> 方便之后替换文本
     * @throws InvalidFormatException
     * @throws IOException
     */
    public Map<String, String> parsePlaceholders(@NotNull InputStream inputStream, @NotNull String placeholderPrefix, @NotNull String placeholderSuffix) throws IOException {
        try (OPCPackage pack = PackageHelper.open(inputStream)) {
            XWPFDocument doc = new XWPFDocument(pack);
            return parseDoc(placeholderPrefix, placeholderSuffix, doc);
        }
    }

    /**
     * 按照指定前缀、后缀从word文档中解析符合规范的占位符集合
     *
     * @param fileSrc           待解析的文件
     * @param placeholderPrefix 占位符前缀
     * @param placeholderSuffix 占位符后缀
     * @return
     * @throws InvalidFormatException
     * @throws IOException
     */
    public Map<String, String> parsePlaceholders(@NotNull String fileSrc, @NotNull String placeholderPrefix, @NotNull String placeholderSuffix) throws IOException {
        try (OPCPackage pack = POIXMLDocument.openPackage(fileSrc)) {
            XWPFDocument doc = new XWPFDocument(pack);
            return parseDoc(placeholderPrefix, placeholderSuffix, doc);
        }
    }

    @NotNull
    private Map<String, String> parseDoc(@NotNull String placeholderPrefix, @NotNull String placeholderSuffix, @NotNull XWPFDocument doc) {
        String encodePlaceholderPrefix = RegexUtil.encode(placeholderPrefix);
        String encodePlaceholderSuffix = RegexUtil.encode(placeholderSuffix);
        Pattern pattern = Pattern.compile(String.format("%s.+?%s", encodePlaceholderPrefix, encodePlaceholderSuffix));

        // 处理段落
        List<XWPFParagraph> paragraphList = doc.getParagraphs();
        Map<String, String> paragraphPlaceholders = parseParagraphs(paragraphList, pattern);

        // 处理表格
        List<XWPFTable> tables = doc.getTables();
        Map<String, String> tablePlaceholders = parseTables(tables, pattern);

        //去掉提取到的占位符中的前缀和后缀
        Map<String, String> allPlaceholders = new HashMap<>(16);
        allPlaceholders.putAll(trim(paragraphPlaceholders, placeholderPrefix, placeholderSuffix));
        allPlaceholders.putAll(trim(tablePlaceholders, placeholderPrefix, placeholderSuffix));
        return allPlaceholders;
    }


    /**
     * 按照指定前缀、后缀从段落列表中解析符合规范的占位符集合
     *
     * @param paragraphList 段落列表
     */
    private Map<String, String> parseParagraphs(List<XWPFParagraph> paragraphList, Pattern pattern) {
        if (CollectionUtils.isEmpty(paragraphList)) {
            return Collections.emptyMap();
        }
        Map<String, String> placeholderMap = new HashMap<>(16);
        // 遍历所有段落
        for (XWPFParagraph paragraph : paragraphList) {
            String text = paragraph.getText();
            if (StringUtils.isNotEmpty(text)) {
                placeholderMap.putAll(processText(text, pattern));
            }

        }

        return placeholderMap;
    }

    /**
     * 按照指定前缀、后缀从表格列表中解析符合规范的占位符集合
     *
     * @param tableList 段落列表
     */
    private Map<String, String> parseTables(List<XWPFTable> tableList, Pattern pattern) {
        Map<String, String> placeholderMap = new HashMap<>(16);
        for (XWPFTable table : tableList) {
            //获取表格对应的行
            parseTable(pattern, placeholderMap, table);
        }
        return placeholderMap;
    }

    private void parseTable(Pattern pattern, Map<String, String> placeholderMap, XWPFTable table) {
        List<XWPFTableRow> rows = table.getRows();
        for (XWPFTableRow row : rows) {
            //获取行对应的单元格
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                placeholderMap.putAll(processText(cell.getText(), pattern));
            }
        }
    }

    /**
     * 按照指定正则规则解析文本，提取符合条件的占位符集合
     *
     * @param text    文本
     * @param pattern 正则
     * @return
     */
    private Map<String, String> processText(String text, Pattern pattern) {
        Map<String, String> placeholderMap = new HashMap<>(16);

        Matcher matcher = pattern.matcher(text);
        String matcherText;
        while (matcher.find()) {
            String matchedField = matcher.group();
            //如果为表格的形式
            if (matchedField.contains(ContractFieldType.TABLE)) {
                //将 {{商品项目列表:|类别|品牌|名称|-TABLE}} 形式转化为 {{商品项目列表:类别-TABLE}}、{{商品项目列表:品牌-TABLE}}、{{商品项目列表:名称-TABLE}}
                String tableHead = matchedField.replaceAll(TABLE_PATTERN, "$2").trim();
                String[] tableHeadArray = tableHead.split("\\|");
                for (String head : tableHeadArray) {
                    if (!head.isEmpty()) {
                        matcherText = matchedField.replaceAll(TABLE_PATTERN, "$1" + head.trim() + "$3");
                        placeholderMap.put(matcherText, matchedField);
                    }
                }
            } else {
                placeholderMap.put(matchedField, matchedField);
            }
        }

        return placeholderMap;
    }

    /**
     * trim掉list中每个元素的前缀和后缀
     *
     * @param textMap 待trim的列表
     * @param prefix  需要去掉的前缀
     * @param suffix  需要去掉的后缀
     * @return
     */
    private Map<String, String> trim(Map<String, String> textMap, String prefix, String suffix) {
        Map<String, String> newMap = new HashMap<>(16);

        textMap.forEach((key, value) -> {
            if (key.length() > prefix.length() + suffix.length()) {
                newMap.put(key.substring(prefix.length(), key.length() - suffix.length()).trim(), value);
            }
        });
        return newMap;
    }

    /**
     * 替换合同模板占位符,返回正式合同
     *
     * @param inputStream
     * @param toReplaceTextMapping 替换内容，key:待替换的内容-value:替换内容
     */
    public InputStream handlePlaceholders(@NotNull InputStream inputStream, @NotNull String placeholderPrefix, @NotNull String placeholderSuffix, @NotNull Map<String, String> toReplaceTextMapping,
        Map<String, String> fieldTypeMap) throws IOException {
        //设置合同未解析字段和字段类型关系
        this.fieldTypeMap = fieldTypeMap;
        return (InputStream) replacePlaceholders(inputStream, placeholderPrefix, placeholderSuffix, toReplaceTextMapping, xwpfDocument -> {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            try {
                xwpfDocument.write(bos);
            } catch (IOException e) {
            }
            return new ByteArrayInputStream(bos.toByteArray());
        });


    }

    /**
     * 替换合同模板占位符,返回正式合同
     *
     * @param inputStream
     * @param toReplaceTextMapping 占位符内容和实际值的映射
     * @param placeholderPrefix    占位符前缀
     * @param placeholderSuffix    占位符后缀
     */
    public Object replacePlaceholders(@NotNull InputStream inputStream, @NotNull String placeholderPrefix, @NotNull String placeholderSuffix, @NotNull Map<String, String> toReplaceTextMapping,
        Function<XWPFDocument, Object> apply) throws IOException {
        //聚合前缀和后缀
        Map<String, String> placeholderValueMap = Maps.newHashMapWithExpectedSize(toReplaceTextMapping.size());
        toReplaceTextMapping.entrySet().forEach(entry -> {
            if (Objects.nonNull(entry.getKey())) {
                placeholderValueMap.put(entry.getKey(), entry.getValue());
            }
        });

        try (OPCPackage pack = PackageHelper.open(inputStream)) {
            XWPFDocument doc = new XWPFDocument(pack);

            //处理表格
            List<XWPFTable> tables = doc.getTables();
            for (XWPFTable table : tables) {
                replaceInTable(table, placeholderValueMap, placeholderPrefix, placeholderSuffix);
            }

            //处理段落
            List<XWPFParagraph> paragraphList = doc.getParagraphs();
            for (XWPFParagraph paragraph : paragraphList) {
                replaceInParagraph(doc, paragraph, placeholderValueMap, placeholderPrefix, placeholderSuffix);
            }

            return apply.apply(doc);
        }
    }

    /**
     * 替换段落中的占位符
     *
     * @param paragraph           当前段落
     * @param placeholderValueMap 占位符和实际值的映射
     * @param placeholderPrefix   占位符前缀
     * @param placeholderSuffix   占位符后缀
     */
    private void replaceInParagraph(XWPFDocument doc, XWPFParagraph paragraph, Map<String, String> placeholderValueMap, @NotNull String placeholderPrefix, @NotNull String placeholderSuffix) {
        PlaceholderRefer refer = new PlaceholderRefer(doc, paragraph, placeholderPrefix, placeholderSuffix);
//        boolean idTitleLvl = isTitleLvl(doc, paragraph);
        for (int idx = 0; idx < paragraph.getRuns().size(); ) {
            XWPFRun run = paragraph.getRuns().get(idx);
            //设置每一行的字体
//            if (!idTitleLvl) {
//                setRunStyle(run);
//            }
            refer.checkRuns(run, idx, paragraph.getRuns());
            if (refer.canProcess()) {
                int firstRunIndex = refer.getFirstRun().runIndex;
                boolean processed = refer.process(placeholderValueMap);
                if (!processed) {
                    idx++;
                } else {
                    idx = firstRunIndex;
                }
            } else {
                idx++;
            }
        }
    }

    /**
     * 替换表格中的占位符
     *
     * @param table               表格
     * @param placeholderValueMap 占位符和实际值的映射
     * @param placeholderPrefix   占位符前缀
     * @param placeholderSuffix   占位符后缀
     */
    private void replaceInTable(XWPFTable table, Map<String, String> placeholderValueMap, @NotNull String placeholderPrefix, @NotNull String placeholderSuffix) {
        List<XWPFTableRow> rows = table.getRows();
        if (rows.get(0).getCell(0).getText().contains(ContractFieldType.TABLE)) {
            //获取第一行表头数据
            String firstRowCellText = rows.get(0).getCell(0).getText().trim();
            insertTableInfo(firstRowCellText, table, placeholderValueMap.getOrDefault(firstRowCellText, null));
            return;
        }
        for (XWPFTableRow row : rows) {
            //获取行对应的单元格
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                replaceInTableCell(placeholderValueMap, placeholderPrefix, placeholderSuffix, cell);
            }
        }
    }

    /**
     * 表格中替换标记文本
     *
     * @param placeholderValueMap
     * @param placeholderPrefix
     * @param placeholderSuffix
     * @param cell
     */
    private void replaceInTableCell(Map<String, String> placeholderValueMap, @NotNull String placeholderPrefix, @NotNull String placeholderSuffix, XWPFTableCell cell) {
        String text = cell.getText();

        cell.removeParagraph(0);
        //设置表格字体样式
        XWPFParagraph cellParagraph = cell.addParagraph();
        XWPFRun cellParagraphRun = cellParagraph.createRun();
        setRunStyle(cellParagraphRun);

        if (!text.contains(placeholderPrefix) || !text.contains(placeholderSuffix)) {
            cellParagraphRun.setText(text);
            return;
        }
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            String tagText = matcher.group(1);
            if (placeholderValueMap.containsKey(tagText)) {
                String fieldType = fieldTypeMap.get(tagText);
                //转换类型为Date和Check的值
                text = text.replace(tagText, transformData(tagText, fieldType, placeholderValueMap));
            }
        }
        cellParagraphRun.setText(text);

    }


    @Data
    @AllArgsConstructor
    public class PlaceholderRefer {

        private static final String singlePrefix = "{";
        private static final String singleSuffix = "}";

        private XWPFParagraph paragraph;
        private String placeholderPrefix;
        private String placeholderSuffix;
        private XWPFDocument doc;

        private RunRecord firstRun;
        private List<RunRecord> middleRuns;
        private RunRecord lastRun;
        /**
         * 不存入 middleRuns 的 index 下标
         */
        private Integer ignoreIndex;

        public PlaceholderRefer(XWPFDocument doc, XWPFParagraph paragraph, String placeholderPrefix, String placeholderSuffix) {
            this.paragraph = paragraph;
            this.placeholderPrefix = placeholderPrefix;
            this.placeholderSuffix = placeholderSuffix;
            this.doc = doc;
            this.middleRuns = new ArrayList<>();
        }

        private void addMiddleRun(XWPFRun run, Integer index) {
            if (middleRuns == null) {
                middleRuns = new ArrayList<>();
            }
            this.middleRuns.add(new RunRecord(run, index));
        }

        public void addRun(XWPFRun run, Integer idx) {
            String runText = Objects.nonNull(run.getText(0)) ? run.getText(0) : "";
            if (runText.contains(placeholderPrefix)) {
                clear();
                this.firstRun = new RunRecord(run, idx);
            }

            if (runText.contains(placeholderSuffix)) {
                this.lastRun = new RunRecord(run, idx);
            }

            if (firstRun != null && lastRun == null && !idx.equals(firstRun.runIndex)) {
                addMiddleRun(run, idx);
            }
        }

        /**
         * 防止{{}} 被拆分
         *
         * @param runList
         */
        public XWPFRun checkRuns(XWPFRun xwpfRun, int index, List<XWPFRun> runList) {

            int runListSize = runList.size();

            String runText = Objects.nonNull(xwpfRun.getText(0)) ? xwpfRun.getText(0) : "";

            if (singlePrefix.equals(runText.trim()) || getCharNum(runText.trim(), singlePrefix) == 1) {
                //判断 {{ 是否被解析为单个 { 或者 { 只出现一次
                if (index + 1 < runListSize) {
                    //与上一节点的 run 拼接
                    String preText = "";
                    if (index != 0) {
                        preText = Optional.ofNullable(runList.get(index - 1).getText(0)).orElse("");
                    }

                    // 若当前 { 与上一个 text 合并包含 {{ ,则不将其放入middleRuns
                    if (!runText.concat(preText).contains(placeholderPrefix)) {
                        String nextText = runList.get(index + 1).getText(0);
                        runText = runText.concat(nextText);
                        setRunInfo(runText, xwpfRun, index);
                    }
                }
            } else if (singleSuffix.equals(runText.trim()) || getCharNum(runText.trim(), singleSuffix) == 1) {
                //判断 }} 是否被解析为单个 } 或者 } 只出现一次
                if (index - 1 > 0) {

                    //与下一节点的 run 拼接
                    String sufText = "";
                    if (index + 1 < runListSize) {
                        sufText = Optional.ofNullable(runList.get(index + 1).getText(0)).orElse("");
                    }

                    // 若当前 } 与下一个 text 合并包含 }} ,则不将其放入middleRuns
                    if (!runText.concat(sufText).contains(placeholderSuffix)) {
                        //与上一节点的 run 拼接
                        runText = runList.get(index - 1).getText(0).concat(runText);
                        setRunInfo(runText, xwpfRun, index);
                    }
                }
            } else {
                setRunInfo(runText, xwpfRun, index);
            }

            return xwpfRun;
        }

        /**
         * 校验对应字符串在文本中出现几次
         *
         * @param text 文本
         * @param cha  查询的字符串
         * @return 出现次数
         */
        private int getCharNum(String text, String cha) {
            if (text.contains(cha)) {
                int beforeLength = text.length();

                String afterText = text.replace(cha, "");

                int afterLength = afterText.length();

                return beforeLength - afterLength;
            }

            return 0;

        }

        private void setRunInfo(String runText, XWPFRun xwpfRun, Integer index) {
            if (runText.contains(placeholderPrefix)) {
                xwpfRun.setText(runText, 0);
                clear();
                this.firstRun = new RunRecord(xwpfRun, index);
            }

            if (runText.contains(placeholderSuffix)) {
                xwpfRun.setText(runText, 0);
                this.lastRun = new RunRecord(xwpfRun, index);
            }

            if (firstRun != null && lastRun == null && !Objects.equals(firstRun.runIndex, index)) {
                addMiddleRun(xwpfRun, index);
            }
        }

        private void clear() {
            firstRun = null;
            middleRuns.clear();
            lastRun = null;
        }

        public boolean canProcess() {
            return firstRun != null && lastRun != null;
        }

        /**
         * 处理占位符替换，前提条件：firstRun和lastRun不为null
         *
         * @param placeholderValueMap
         * @return
         */
        public boolean process(Map<String, String> placeholderValueMap) {

            assert firstRun != null && lastRun != null;
            boolean processed = false;
            if (firstRun.getRunIndex().equals(lastRun.getRunIndex())) {
                //前缀和后缀在同一个run中即firstRun == lastRun, 将前缀到后缀之间占位符提取出来并替换为真实值
                String text = firstRun.run.getText(firstRun.run.getTextPosition());
                int prefixIdx = text.indexOf(placeholderPrefix);
                int suffixIdx = text.indexOf(placeholderSuffix);
                String placeholder = text.substring(prefixIdx, suffixIdx + placeholderSuffix.length());
                if (placeholderValueMap.containsKey(placeholder)) {
                    String replaceValue = setRunData(placeholder, placeholderValueMap);

                    text = text.replaceAll(RegexUtil.encode(placeholder), replaceValue);
                    firstRun.run.setText(text, 0);
                    processed = true;
                } else if (Boolean.TRUE.equals(isReplaceAll)) {
                    firstRun.run.setText("", 0);
                }
            } else {
                /**
                 * 前缀和后缀不在同一个run中在firstRun != lastRun, 提取firstRun中前缀->middleRuns->lastRun后缀之间占位符,
                 * 并替换为真实值附加到firstRun中，删除middleRuns，删除lastRun中后缀及之前的字符
                 */
                String firstRunText = firstRun.getRun().getText(0);
                int prefixIdx = firstRunText.indexOf(placeholderPrefix);
                StringBuilder placeholder = new StringBuilder(firstRunText.substring(prefixIdx));
                for (RunRecord run : middleRuns) {
                    placeholder.append(run.getRun().getText(0));
                }

                String lastRunText = lastRun.run.getText(lastRun.run.getTextPosition());
                int suffixIdx = lastRunText.indexOf(placeholderSuffix);
                placeholder.append(lastRunText, 0, suffixIdx + placeholderSuffix.length());
                String ph = placeholder.toString();
                if (placeholderValueMap.containsKey(ph)) {
                    String replaceValue = setRunData(ph, placeholderValueMap);

                    firstRun.run.setText(firstRunText.substring(0, prefixIdx) + replaceValue, 0);
                    lastRun.run.setText(lastRunText.substring(suffixIdx + placeholderSuffix.length()), 0);
                    removeParagraphRun();
                    processed = true;
                } else if (Boolean.TRUE.equals(isReplaceAll)) {
                    firstRun.run.setText("", 0);
                    lastRun.run.setText("", 0);
                    removeParagraphRun();
                }
            }

            clear();
            return processed;
        }

        public void removeParagraphRun() {

            //移除 middleRuns对应数值
            for (int idx = lastRun.getRunIndex() - 1; idx > firstRun.runIndex; idx--) {
                paragraph.removeRun(idx);
            }

//            //middleRuns 最后一位下标 + 1的数值若为 } 则移除
//            String lastParagraphRunText = paragraph.getRuns().get(middleRuns.size()).getText(0);
//            if (lastParagraphRunText.contains(singleSuffix)) {
//                paragraph.removeRun(middleRuns.size());
//            }
//            //移除middleRuns对应数值
//            for (int idx = middleRuns.size() - 1; idx > -1; --idx) {
//                paragraph.removeRun(middleRuns.get(idx).getRunIndex());
//            }
//            //middleRuns 第一位一位下标 - 1的数值若为 { 则移除
//            int firstRunIndex = middleRuns.get(0).getRunIndex();
//            String paragraphRunText = paragraph.getRuns().get(firstRunIndex-1).getText(0);
//            if (paragraphRunText.contains(singlePrefix)) {
//                paragraph.removeRun(firstRunIndex - 1);
//            }

        }

        public String setRunData(String placeholder, Map<String, String> placeholderValueMap) {

            if (fieldTypeMap.containsKey(placeholder)) {
                String fieldType = fieldTypeMap.get(placeholder);

                if (fieldType.equals(ContractFieldType.DATE) || fieldType.equals(ContractFieldType.CHECK)) {
                    return transformData(placeholder, fieldType, placeholderValueMap);
                } else if (fieldType.equals(ContractFieldType.TABLE)) {
                    //替换表格
                    XmlCursor cursor = paragraph.getCTP().newCursor();

                    XWPFTable table = doc.insertNewTbl(cursor);
                    insertTableInfo(placeholder, table, placeholderValueMap.get(placeholder));
                    if (Boolean.TRUE.equals(isReplaceAll)) {
                        return "";
                    }
                    return placeholder;
                } else if (fieldType.equals(ContractFieldType.IMAGE)) {
                    //替换图片
                    insertImageInfo(firstRun.run, placeholderValueMap.get(placeholder));
                    if (Boolean.TRUE.equals(isReplaceAll)) {
                        return "";
                    }
                    return placeholder;
                } else {
                    return placeholderValueMap.get(placeholder);
                }
            }

            return null;
        }
    }

    private String transformData(String placeholder, String fieldType, Map<String, String> placeholderValueMap) {

        if (fieldType.equals(ContractFieldType.DATE)) {
            return transformDate(placeholderValueMap.get(placeholder));
        } else if (fieldType.equals(ContractFieldType.CHECK)) {
            List<String> checkList = JSON.parseArray(placeholderValueMap.get(placeholder), String.class);

            String value = "";
            for (String info : checkList) {
                value = value.concat(info + ";");
            }
            return value;
        }

        return placeholderValueMap.get(placeholder);
    }

    /**
     * 将时间戳转成日期
     *
     * @param text
     * @return
     */
    private String transformDate(String text) {
        if (StringUtils.contains(text, "-")) {
            return text;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(Long.valueOf(text));
        return simpleDateFormat.format(date);
    }

    /**
     * 把信息插入表格
     *
     * @param table
     * @param value 表格值-JSON格式
     */
    private void insertTableInfo(String placeholder, XWPFTable table, String value) {
        if (value == null && Boolean.FALSE.equals(isReplaceAll)) {
            return;
        }
        XWPFTableRow row = table.getRow(0);
        //存放表头列，避免数据错乱
        List<XWPFTableCell> tableCells = row.getTableCells();
        List<String> orderList = new ArrayList<>();
        for (XWPFTableCell tableCell : tableCells) {
            String tableCellString = tableCell.getText().trim();
            orderList.add(tableCellString.replaceAll(TABLE_PATTERN_2, "$2").trim());
        }
        for (int i = 0; i < orderList.size(); i++) {
            //设置表格字体样式
            row.getCell(i).removeParagraph(0);
            XWPFParagraph cellParagraph = row.getCell(i).addParagraph();
            XWPFRun cellParagraphRun = cellParagraph.createRun();
            setRunStyle(cellParagraphRun);
            cellParagraphRun.setText(orderList.get(i));
        }

        List<Map<String, String>> list;
        if (value == null) {
            list = Collections.emptyList();
        } else {
            list = (List<Map<String, String>>) JSONObject.parse(value);
        }

        for (int i = 0; i < list.size(); i++) {
            Map<String, String> valueMap = list.get(i);
            row = table.createRow();
            //按照表头顺序设值
            for (int mapNum = 0; mapNum < orderList.size(); mapNum++) {
                //设置表格字体样式
                row.getCell(mapNum).removeParagraph(0);
                XWPFParagraph cellParagraph = row.getCell(mapNum).addParagraph();
                XWPFRun cellParagraphRun = cellParagraph.createRun();
                setRunStyle(cellParagraphRun);
                cellParagraphRun.setText(valueMap.get(orderList.get(mapNum)));
            }
        }

    }

    /**
     * 插入图片
     *
     * @param run
     * @param value 表格值-JSON格式
     */
    private void insertImageInfo(XWPFRun run, String value) {
        if (Strings.isNullOrEmpty(value)) {
            return;
        }
        run.addBreak();

        String bucket = ctOssUtil.getBucket(null);
        InputStream imageInputStream = cloudClient.downloadFile(bucket, value);

        int width = 0;
        int height = 0;

        File file = null;
        try {
            file = FileHelper.inputStreamtoFile(imageInputStream, "png");
        } catch (IOException e) {
            log.error("流转换png文件失败, error : {}", Throwables.getStackTraceAsString(e));
            throw new RuntimeException("流转换png文件失败");
        }
        BufferedImage bufferedImage = null;
        try {
            bufferedImage = ImageIO.read(file);
            width = bufferedImage.getWidth();
            height = bufferedImage.getHeight();
            //再次获取流
            run.addPicture(cloudClient.downloadFile(bucket, value), XWPFDocument.PICTURE_TYPE_PNG, "", Units.toEMU(width), Units.toEMU(height));
        } catch (IOException e) {
            log.error("seal image file read fail, word filed:[{}]", value);
//            throw new ActionErrorException("图片文件读取失败");
        } catch (InvalidFormatException e) {
            log.error("word插入图片失败, error : {}", Throwables.getStackTraceAsString(e));
            throw new RuntimeException("word插入图片失败");
        } catch (NullPointerException e) {
            log.error("word插入图片失败, error : {}", Throwables.getStackTraceAsString(e));
            throw new RuntimeException("请检查图片格式");
        } finally {
            if (Objects.nonNull(file)) {
                file.delete();
            }
        }
    }

    @Data
    @AllArgsConstructor
    public class RunRecord {

        private XWPFRun run;
        private Integer runIndex;
    }

    /**
     * 扩展设置字体样式
     *
     * @param run
     */
    private void setRunStyle(XWPFRun run) {
        run.setFontFamily("宋体");
        //五号
        run.setFontSize(11);
//        //杂货-简易购销合同
//        final String groceriesSimplePurchaseAndSaleContract = "GROCERIES_SIMPLE_PURCHASE_AND_SALE_CONTRACT";
//        //生鲜-简易购销合同
//        final String seafoodSimplePurchaseAndSaleContract = "SEAFOOD_SIMPLE_PURCHASE_AND_SALE_CONTRACT";
//        //生鲜-冷库仓储合同
//        final String leaseContractOfFreshAndColdStorage = "LEASE_CONTRACT_OF_FRESH_AND_COLD_STORAGE";
//        //租赁合同
//        final String investmentPromotionLeasingContract = "INVESTMENT_PROMOTION_LEASING_CONTRACT";
//        //租赁扣点合同
//        final String leaseDeductionContract = "LEASE_DEDUCTION_CONTRACT";
//        switch (docxType) {
//            case groceriesSimplePurchaseAndSaleContract :
//                //五号
//                run.setFontSize(11);
//                break;
//            case seafoodSimplePurchaseAndSaleContract :
//            case leaseContractOfFreshAndColdStorage :
//                //小五
//                run.setFontSize(9);
//                break;
//            case investmentPromotionLeasingContract :
//            case leaseDeductionContract :
//                //小四
//                run.setFontSize(12);
//                break;
//        }
    }

    /**
     * 判断Word中的大纲级别，为大纲级别则不改变样式 可以通过getPPr().getOutlineLvl()直接提取，但需要注意，Word中段落级别，通过如下三种方式定义： 1、直接对段落进行定义； 2、对段落的样式进行定义； 3、对段落样式的基础样式进行定义。 因此，在通过“getPPr().getOutlineLvl()”提取时，需要依次在如上三处读取。
     *
     * @param doc
     * @param para
     * @return
     */
    private boolean isTitleLvl(XWPFDocument doc, XWPFParagraph para) {
        try {
            //判断该段落是否设置了大纲级别
            if (para.getCTP().getPPr().getOutlineLvl() != null) {
                return true;
            }

            //判断该段落的样式是否设置了大纲级别
            if (doc.getStyles().getStyle(para.getStyle()).getCTStyle().getPPr().getOutlineLvl() != null) {
                return true;
            }

            //判断该段落的样式的基础样式是否设置了大纲级别
            if (doc.getStyles().getStyle(doc.getStyles().getStyle(para.getStyle()).getCTStyle().getBasedOn().getVal()).getCTStyle().getPPr().getOutlineLvl() != null) {
                return true;
            }

            if (para.getStyleID() != null) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }

        return false;
    }

}
