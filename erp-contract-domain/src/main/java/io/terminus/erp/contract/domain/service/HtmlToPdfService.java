package io.terminus.erp.contract.domain.service;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.contract.domain.util.PDFUtil;
import io.terminus.trantor2.module.service.OSSService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.UUID;

/**
 * <AUTHOR>
 * @time 2025/7/23 15:11
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HtmlToPdfService {

    @Value("${gotenberg.server.addr:http://pdf-gotenberg-4aa88fa328.project-1281-staging.svc.cluster.local:3000}")
    private String gotenbergServerAddr;

    private final OSSService ossService;


    public String html2PDF(String html, String bucketName, String fileName) {
        final String ossFullFileName = fileName + ".pdf";

        InputStream inputStream = null;
        try {
            HttpResponse response = HttpRequest.post(URLUtil.completeUrl(gotenbergServerAddr,"/forms/chromium/convert/html"))
                    .form("file", html.getBytes(StandardCharsets.UTF_8), "index.html")
                    .timeout(1000*30)
                    .execute();

            if (response.getStatus() != 200) {
                log.error("html to pdf error,{}",response.body());
                throw new BusinessException("pdf生成失败");
            }

            inputStream = response.bodyStream();

            String url = ossService.uploadFileAndGetUrl(bucketName, ossFullFileName, inputStream, "application/pdf", null);
            return url;
        } catch (Exception e) {
            log.error("html to pdf error", e);
        }finally {
            IoUtil.close(inputStream);
        }
        return "";
    }


}
