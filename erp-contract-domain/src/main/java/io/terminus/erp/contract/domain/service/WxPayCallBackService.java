package io.terminus.erp.contract.domain.service;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.wxpay.sdk.WXPayUtil;
import io.terminus.erp.contract.domain.properties.WXPayProperties;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtEsignatureTrRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSignatoryAccountTrRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConSignatoryAccountRechargeRepo;
import io.terminus.erp.contract.spi.dict.tp.ConCtEsignatureTrSignWayDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryAccountTrSignatoryAccountStatusDict;
import io.terminus.erp.contract.spi.dict.tp.ConSignatoryAccountRechargeOrderStatusDict;
import io.terminus.erp.contract.spi.model.tp.po.ConCtEsignatureTrPO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignatoryAccountTrPO;
import io.terminus.erp.contract.spi.model.tp.po.ConSignatoryAccountRechargePO;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 微信支付回调服务
 *
 * @className: WxCallBackService
 * @author: charl
 * @date: 2023/11/7 10:03
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WxPayCallBackService {

    private final WXPayProperties wxPayProperties;

    private final ConSignatoryAccountRechargeRepo conSignatoryAccountRechargeRepo;

    private final ConCtSignatoryAccountTrRepo conCtSignatoryAccountTrRepo;

    private final ConCtEsignatureTrRepo conCtEsignatureTrRepo;

    public String wxPayCallBack(HttpServletRequest request) throws Exception {
        log.info("微信支付回调开始");
        Map<String, String> return_data = new HashMap<>();
        if(Objects.isNull(request) || Objects.isNull(request.getInputStream())) {
            log.error("回调参数为空！");
            return_data.put("return_code", "FAIL");
            return_data.put("return_msg", "回调参数为空");
            return WXPayUtil.mapToXml(return_data);
        }
        //读取参数
        InputStream inputStream;
        StringBuilder sb = new StringBuilder();
        inputStream = request.getInputStream();
        String s;
        BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        while ((s = in.readLine()) != null) {
            sb.append(s);
        }
        in.close();
        inputStream.close();
        if (StringUtils.isEmpty(sb.toString())) {
            log.error("回调参数为空！");
            return_data.put("return_code", "FAIL");
            return_data.put("return_msg", "回调参数为空");
            return WXPayUtil.mapToXml(return_data);
        }
        //解析xml成map
        Map<String, String> map = WXPayUtil.xmlToMap(sb.toString());
        log.info("微信支付回调参数:{}", map);
        // 签名错误，直接返回
        if (!WXPayUtil.isSignatureValid(map, wxPayProperties.getKey())) {
            log.error("微信支付回调：签名错误");
            return_data.put("return_code", "FAIL");
            return_data.put("return_msg", "签名错误");
            return WXPayUtil.mapToXml(return_data);
        }
        //判断返回return_code是否正确
        if (!map.get("return_code").equals("SUCCESS")) {
            log.error("微信支付回调：return_code错误：" + map.get("return_code"));
            return_data.put("return_code", "FAIL");
            return_data.put("return_msg", "return_code不正确");
            return WXPayUtil.mapToXml(return_data);
        }
        //判断返回result_code是否正确
        if (!map.get("result_code").equals("SUCCESS")) {
            log.error("微信支付回调：result_code错误：" + map.get("result_code"));
            return_data.put("return_code", "FAIL");
            return_data.put("return_msg", "result_code不正确");
            return WXPayUtil.mapToXml(return_data);
        }

        String orderno = map.get("out_trade_no");//商户订单号
        String transaction_id = map.get("transaction_id");//微信支付订单号
        String time_end = map.get("time_end");//支付完成时间yyyyMMddHHmmss
        BigDecimal total_fee = new BigDecimal(map.get("total_fee"));

        LambdaQueryWrapper<ConSignatoryAccountRechargePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConSignatoryAccountRechargePO::getOrderCode, orderno);
        ConSignatoryAccountRechargePO accountRechargePO = conSignatoryAccountRechargeRepo.selectOne(queryWrapper);
        if (Objects.isNull(accountRechargePO)) {
            log.info("微信支付回调，订单不存在,orderCode:{}", orderno);
            return_data.put("return_code", "FAIL");
            return_data.put("return_msg", "订单不存在");
            return WXPayUtil.mapToXml(return_data);
        }
        String orderStatus = accountRechargePO.getOrderStatus();
        // 订单已支付
        if (ConSignatoryAccountRechargeOrderStatusDict.PAID.equals(orderStatus)) {
            log.info("微信支付回调，订单已支付");
            return_data.put("return_code", "SUCCESS");
            return_data.put("return_msg", "OK");
            return WXPayUtil.mapToXml(return_data);
        }
        BigDecimal orderMoney = accountRechargePO.getOrderSum();
        BigDecimal totalMoney = orderMoney.multiply(BigDecimal.valueOf(100));
        // 微信支付金额不等于支付单金额，返回异常
        if (totalMoney.compareTo(total_fee) != 0) {
            log.info("微信支付回调，微信支付金额不等于支付单金额，wx={},order={}", total_fee, orderMoney);
            return_data.put("return_code", "FAIL");
            return_data.put("return_msg", "金额异常");
            // 更新支付单支付失败
            accountRechargePO.setOrderStatus(ConSignatoryAccountRechargeOrderStatusDict.PAID_FALUSE);
            accountRechargePO.setOrderResult("金额异常,wx=" + total_fee + ",order=" + orderMoney);
            conSignatoryAccountRechargeRepo.updateById(accountRechargePO);
            return WXPayUtil.mapToXml(return_data);
        }
        //更新订单信息
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            LocalDateTime parse = LocalDateTime.parse(time_end, formatter);
            accountRechargePO.setOrderStatus(ConSignatoryAccountRechargeOrderStatusDict.PAID);
            accountRechargePO.setTimeEnd(parse);
            accountRechargePO.setWxOrderCode(transaction_id);
            accountRechargePO.setOrderResult("微信支付回调：更新订单成功");
            conSignatoryAccountRechargeRepo.updateById(accountRechargePO);
            ConCtSignatoryAccountTrPO signatoryAccount = conCtSignatoryAccountTrRepo.selectById(accountRechargePO.getRelSignAccount());
            Long companyId = signatoryAccount.getRelCompany();
            ConCtEsignatureTrPO conCtEsignatureTrPO = conCtEsignatureTrRepo.selectOne(new LambdaQueryWrapper<ConCtEsignatureTrPO>()
                    .eq(ConCtEsignatureTrPO::getEntity, companyId).last("limit 0,1"));

            // UKEY的签署方式，更新生效时间和结束时间
            if (Objects.equals(ConCtEsignatureTrSignWayDict.UKEY, conCtEsignatureTrPO.getSignWay())) {
                LambdaUpdateWrapper<ConCtSignatoryAccountTrPO> accountUpdate = new LambdaUpdateWrapper<>();
                accountUpdate.eq(ConCtSignatoryAccountTrPO::getId, signatoryAccount.getId());
                LocalDateTime last = signatoryAccount.getSignatoryAccountEffectTime();
                if (Objects.isNull(signatoryAccount.getSignatoryAccountEffectTime())) {
                    last = LocalDateTime.now();
                }
                //事件签署，更新账户余额
                BigDecimal signatoryAccountAmount = BigDecimal.ZERO;
                if (Objects.nonNull(signatoryAccount.getSignatoryAccountAmount())) {
                    signatoryAccountAmount = signatoryAccount.getSignatoryAccountAmount();
                }
                BigDecimal money = signatoryAccountAmount.add(orderMoney);
                LocalDateTime oneYearLater = last.plusYears(1);
                accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAccountAmount, money);
                accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAccountEffectTime, last);
                accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAccountLoseTime, oneYearLater);
                accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAccountStatus, ConCtSignatoryAccountTrSignatoryAccountStatusDict.ACTIVE);
                int update = conCtSignatoryAccountTrRepo.update(null, accountUpdate);
                log.info("微信支付回调，UKEY更新账户有效期：" + update);
            } else {
                //事件签署，更新账户余额
                BigDecimal signatoryAccountAmount = BigDecimal.ZERO;
                if (Objects.nonNull(signatoryAccount.getSignatoryAccountAmount())) {
                    signatoryAccountAmount = signatoryAccount.getSignatoryAccountAmount();
                }
                BigDecimal money = signatoryAccountAmount.add(orderMoney);
                //使用乐观锁方式进行更新，防止并发更新
                LambdaUpdateWrapper<ConCtSignatoryAccountTrPO> accountUpdate = new LambdaUpdateWrapper<>();
                accountUpdate.eq(ConCtSignatoryAccountTrPO::getId, signatoryAccount.getId());
                accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAccountAmount, money);
                accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAmountTimeNews, LocalDateTime.now());
                accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAccountStatus, ConCtSignatoryAccountTrSignatoryAccountStatusDict.ACTIVE);
                int update = conCtSignatoryAccountTrRepo.update(null, accountUpdate);
                log.info("微信支付回调，更新账户余额状态：" + update);
            }

            return_data.put("return_code", "SUCCESS");
            return_data.put("return_msg", "OK");
            return WXPayUtil.mapToXml(return_data);
        } catch (Exception e) {
            log.error("update order error:{}", ExceptionUtil.stacktraceToString(e));
            accountRechargePO.setOrderStatus(ConSignatoryAccountRechargeOrderStatusDict.PAID_FALUSE);
            accountRechargePO.setOrderResult("微信支付回调：更新订单失败");
            conSignatoryAccountRechargeRepo.updateById(accountRechargePO);
            return_data.put("return_code", "FAIL");
            return_data.put("return_msg", "更新订单失败");
            return WXPayUtil.mapToXml(return_data);
        }
    }

}
