package io.terminus.erp.contract.domain.util;

import com.google.common.base.Throwables;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.parser.EventType;
import com.itextpdf.kernel.pdf.canvas.parser.PdfCanvasProcessor;
import com.itextpdf.kernel.pdf.canvas.parser.data.IEventData;
import com.itextpdf.kernel.pdf.canvas.parser.data.TextRenderInfo;
import com.itextpdf.kernel.pdf.canvas.parser.listener.LocationTextExtractionStrategy;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.Style;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.font.FontProvider;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import com.itextpdf.layout.property.VerticalAlignment;
import io.terminus.common.api.exception.BusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @className: PdfUtil
 * @author: charl
 * @date: 2023/10/17 16:37
 */
@Slf4j
public class PDFUtil {

    private final static String regEx_special = "\\&[a-zA-Z]{1,10};";
    private final static Pattern compile = Pattern.compile(regEx_special, Pattern.CASE_INSENSITIVE);

    private static final FontProvider FONT_PROVIDER = new FontProvider();
    public static final String PATH_OF_FONT_FANGZHENG_TTF = "/fangzheng_gbk.TTF";

    static {
        PdfFont FONT = getFangzhengFont();
        FONT_PROVIDER.addFont(FONT.getFontProgram(), PdfEncodings.IDENTITY_H);
    }

    public static PdfFont getFangzhengFont() {
        try {
            return PdfFontFactory.createFont(PATH_OF_FONT_FANGZHENG_TTF, PdfEncodings.IDENTITY_H, false);
        } catch (IOException e) {
            log.error("create font error:{}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 处理特殊字符，将Unicode特殊字符转换为HTML标签,根本原因字体库缺少
     * @param input 输入字符串
     * @return 处理后的字符串
     */
    private static String handleSpecialCharacters(String input) {
        if (input == null) {
            return null;
        }
        // 使用HTML sup标签处理上标字符
        input = input.replace("²", "<sup>2</sup>")
                    .replace("³", "<sup>3</sup>")
                    .replace("¹", "<sup>1</sup>");
        return input;
    }

    /**
     * 处理富文本 字体样式丢失 导致后台解析格式错乱，
     * 根本上解决，要换一个适配性好的富文本编辑器试试，目前客户使用的ueditor效果他们能接受
     * @param input 输入字符串
     * @return 处理后的字符串
     */
    private static String handleFontSize(String input) {
        if (input == null) {
            return null;
        }
        // 使用HTML sup标签处理上标字符
        input = input.replace("font-size:  !important", "font-size: 14px !important");
        return input;
    }


    /**
     * html转pdf
     *
     * @param htmlStr
     * @param outputStream
     * @throws IOException
     */
    public static void convertToPdf(String htmlStr, OutputStream outputStream) throws IOException {
        // 处理特殊字符
        htmlStr = handleFontSize(htmlStr);
        //读取Html文件流，查找出当中的&nbsp;或出现类似的符号空格字符
        Matcher matcher = compile.matcher(htmlStr);
        String replaceAll = matcher.replaceAll("");
        InputStream htmlStream = getStringStream(replaceAll);
        convertToPdf(htmlStream, outputStream);
    }

    /**
     * html转pdf
     *
     * @param srcInputStream  输入流
     * @param dstOutputStream 输出流
     * @date : 2022/11/15 14:07
     */
    public static void convertToPdf(InputStream srcInputStream, OutputStream dstOutputStream) throws IOException {

        if (srcInputStream == null) {
            log.error("转换失败!");
            return;
        }

        final Path tmpFilePath = Paths.get("/tmp", "convertToPdf_" + UUID.randomUUID() + ".pdf");
        final PdfWriter pdfWriter = new PdfWriter(tmpFilePath.toFile());
        pdfWriter.setCompressionLevel(CompressionConstants.DEFAULT_COMPRESSION);
        PdfDocument tmpPdfDoc = new PdfDocument(pdfWriter);

        //设置为A4大小
        tmpPdfDoc.setDefaultPageSize(PageSize.A4);
        //设置缓存


        //添加中文字体支持
        ConverterProperties properties = new ConverterProperties();
        properties.setFontProvider(FONT_PROVIDER);
        HtmlConverter.convertToPdf(srcInputStream, tmpPdfDoc, properties);

        tmpPdfDoc.close();

        // 第二遍：创建新的PDF文档并复制页面，第一遍直接设置页码获取的总页数不正确。

        // 临时文件，不带页码
        PdfDocument srcDoc = new PdfDocument(new PdfReader(tmpFilePath.toFile()));
        // 最终文件，带着页码
        PdfDocument dstDoc = new PdfDocument(new PdfWriter(dstOutputStream));
        dstDoc.addEventHandler(PdfDocumentEvent.END_PAGE, new PageNumberEventHandler(PATH_OF_FONT_FANGZHENG_TTF));

        srcDoc.copyPagesTo(1, srcDoc.getNumberOfPages(), dstDoc);

        srcDoc.close();
        dstDoc.close();
        tmpFilePath.toFile().delete();
    }


    /**
     * 添加页码
     */
    private static class PageNumberEventHandler implements IEventHandler {

        private PdfFont font;


        public PageNumberEventHandler(String fontPath) {
            try {
                this.font = PdfFontFactory.createFont(fontPath, PdfEncodings.IDENTITY_H, true);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public void handleEvent(Event event) {
            PdfDocumentEvent docEvent = (PdfDocumentEvent) event;
            PdfDocument pdf = docEvent.getDocument();
            PdfPage page = docEvent.getPage();
            int pageNumber = pdf.getPageNumber(page);
            int totalPages = pdf.getNumberOfPages();
            Rectangle pageSize = page.getPageSize(); // 宽 x 高
            PdfCanvas pdfCanvas = new PdfCanvas(page);
            Canvas canvas = new Canvas(pdfCanvas, pageSize);
            float x = pageSize.getWidth() / 2;
            float y = pageSize.getBottom() + 20;
            Paragraph p = new Paragraph(String.format("第 %d/%d 页", pageNumber, totalPages))
                    .setFont(font)
                    .setFontSize(10);
            canvas.showTextAligned(p, x, y, TextAlignment.CENTER);
        }
    }


    public static void main(String[] args) {
        try {
            // 测试HTML内容，包含特殊字符
            String htmlContent = "<html><body>" +
                    "<p>测试特殊字符：</p>" +
                    "<p>面积：100m²</p>" +
                    "<p>体积：50m³</p>" +
                    "<p>多少克平方米：60g/m²</p>" +
                    "</body></html>";

            // 获取classpath下的输出路径
            String outputPath = "/Users/<USER>/Download/" + "test_output.pdf";
            File outputFile = new File(outputPath);
            
            // 确保输出目录存在
            outputFile.getParentFile().mkdirs();

            // 创建输出流
            try (FileOutputStream outputStream = new FileOutputStream(outputFile)) {
                // 调用转换方法
                convertToPdf(htmlContent, outputStream);
                System.out.println("PDF文件已生成：" + outputFile.getAbsolutePath());
            }
        } catch (IOException e) {
            log.error("PDF转换失败", e);
        }
    }

    /**
     * 将一个字符串转化为输入流
     *
     * @param sInputString 字符串
     * @return
     */
    public static InputStream getStringStream(String sInputString) {
        if (sInputString != null && !sInputString.trim().isEmpty()) {
            try {
                return new ByteArrayInputStream(sInputString.getBytes());
            } catch (Exception e) {
                log.error("PDFUtil.getStringStream error {}", Throwables.getStackTraceAsString(e));
            }
        }
        return null;
    }


    /**
     * 测试用例：
     * 1. 最后一页只有一行，且在第一行
     * 2. 最后一页最后一行也有内容，看图片添加效果
     * 3. 最后一页最后有内容的段落后有空白的段落，看图片应该在最后一段有内容的段落后，而不是空白内容的段落后
     */
    public static Path addSignatureImg(File contractFile, List<SignatureImage> signatureImages) {

        PdfFont font = getFangzhengFont();

        // 创建 PDF 读取器和写入器
        PdfReader reader = null;
        Path path = Paths.get("/tmp/addSignatureImg_" + UUID.randomUUID() + ".pdf");
        try {
            reader = new PdfReader(contractFile);

            PdfWriter writer = new PdfWriter(Files.newOutputStream(path));
            PdfDocument pdfDoc = new PdfDocument(reader, writer);

            // 获取最后一页
            final int pageCount = pdfDoc.getNumberOfPages();
            final PdfPage lastPage = pdfDoc.getPage(pageCount);
            // 获取页面的内容区域
            final Rectangle pageSize = lastPage.getPageSize();

            final int NUM_OF_IMAGE = signatureImages.size();
            // 最后一段有内容的Y坐标
            final float lastContentY = getLastContentY(lastPage);
            // 签名段落顶部的外边距
            final int IMAGE_TOP_MARGIN = 30;
            // 签名图片高度
            final float IMG_HEIGHT = 16;
            // 签名的行间距
            final int PARAGRAPH_LINE_PADDING = 5;
            // 设置签名图片的位置，最后一段的的高度减去10，再减去图片的高度(假设每三个图片一行，每多一行，预留出来一行的高度)
            float imageY = (lastContentY - IMAGE_TOP_MARGIN - ((IMG_HEIGHT + PARAGRAPH_LINE_PADDING) * Math.floorDiv(NUM_OF_IMAGE + 2, 3)));

            // 内容区域的左侧外边距
            final int LEFT_MARGIN = 40;
            // 图片区域的宽度
            final float PARAGRAPH_WIDTH = pageSize.getWidth() - 2 * LEFT_MARGIN;


            try (Document document = new Document(lastPage.getDocument())) {
                Paragraph p = new Paragraph();
                p.setVerticalAlignment(VerticalAlignment.TOP);
                p.setFixedPosition(pageCount, LEFT_MARGIN, imageY, UnitValue.createPointValue(PARAGRAPH_WIDTH));
                for (SignatureImage signatureImage : signatureImages) {
                    // 把text和image再包一个段落的目的是，保障他们在同一行。否则在行尾，有可能会把text和image分到两行
                    Paragraph innerP = new Paragraph();
                    innerP.setMarginRight(5);

                    // 添加text
                    Text text = new Text(signatureImage.getNameToPrint());
                    Style style = new Style()
                            .setFontSize(14)
                            .setFontColor(ColorConstants.BLACK)
                            .setBackgroundColor(ColorConstants.WHITE)
                            .setVerticalAlignment(VerticalAlignment.TOP);
                    if(font != null) {
                        style.setFont(font);
                    }
                    text.addStyle(style);
                    innerP.add(text);

                    // 添加图片
                    File imageFile = signatureImage.getImageFile();
                    if(imageFile != null) {
                        ImageData img = ImageDataFactory.create(imageFile.getAbsolutePath());
                        // 图片固定是固定高度，但是宽度按照签名图片等比例缩放
                        final float IMAGE_WIDTH = IMG_HEIGHT * img.getWidth() / img.getHeight();

                        Image image = new Image(img);
                        image.scaleToFit(IMAGE_WIDTH, IMG_HEIGHT);
                        innerP.add(image);
                    }

                    p.add(innerP);
                }
                document.add(p);
            }

            // 关闭文档
            pdfDoc.close();
        } catch (IOException e) {
            log.error("addSignatureImg fail", e);
            throw new BusinessException("addSignatureImg fail", e);
        }
        return path;
    }

    /**
     * 取最后一段内容的地步位置Y
     * @param lastPage
     * @return
     */
    private static float getLastContentY(PdfPage lastPage) {
        // 计算最后一段的底部位置
        final float[] lastParagraphY = {lastPage.getPageSize().getHeight()}; // 初始化最后一段的 Y 坐标

        // 提取最后一段的位置
        LocationTextExtractionStrategy strategy = new LocationTextExtractionStrategy() {
            @Override
            public void eventOccurred(IEventData data, EventType type) {
                    if (type == EventType.RENDER_TEXT) {
                        TextRenderInfo renderInfo = (TextRenderInfo) data;
                        String content = renderInfo.getText();
                        // 找有最后一段有内容的段落所在的位置
                        if (Strings.isNotBlank(content)) {
                            // 页码
                            if(Pattern.compile("第\\s*\\d+/\\d+\\s*页").matcher(content).find()){
                                return;
                            }
                            Rectangle rect = renderInfo.getDescentLine().getBoundingRectangle();
                            float y = rect.getY();
                            // 异常处理
                            if(y < 0){
                                return;
                            }
                            lastParagraphY[0] = Math.min(lastParagraphY[0], y);
                        }
                    }
                }
        };
        PdfCanvasProcessor processor = new PdfCanvasProcessor(strategy);
        processor.processPageContent(lastPage);
        return lastParagraphY[0];
    }

    /**
     * 签名图片对象，用于记录签名图片URL
     */
    @Data
     public static class SignatureImage {

        private long userId;

        private String name;

        private String imageUrl;

        private File imageFile;

        /**
         * 如果没有图片，用*代替
         * @return res
         */
        public String getNameToPrint() {
            if (Strings.isNotBlank(imageUrl) && imageFile != null) {
                return name + "：";
            }
            return name + "： * ";
        }
    }



}
