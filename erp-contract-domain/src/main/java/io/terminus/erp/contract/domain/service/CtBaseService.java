package io.terminus.erp.contract.domain.service;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import io.terminus.cloud.storage.core.client.CloudClient;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.BaseModel;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.erp.contract.domain.util.FileHelper;
import io.terminus.erp.contract.domain.util.HtmlToPDFUtil;
import io.terminus.erp.contract.domain.util.PDFUtil;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSignTypeConfRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSignchannalCfRepo;
import io.terminus.erp.contract.spi.convert.tp.GenCtHeadTrExtConverter;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtHeadTrExtDTO;
import io.terminus.erp.contract.spi.model.tp.dto.RichTextToPdfDTO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignTypeConfPO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignchannalCfPO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSigntaskTrPO;
import io.terminus.erp.contract.spi.msg.CtSignMsg;
import io.terminus.erp.md.infrastructure.repo.base.GenAttachmentTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.base.GenComTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.ct.CtHeadTrRepo;
import io.terminus.erp.md.infrastructure.repo.vend.GenVendInfoMdRepo;
import io.terminus.erp.md.spi.model.po.base.GenAttachmentTypeCfPO;
import io.terminus.erp.md.spi.model.po.base.GenComTypeCfPO;
import io.terminus.erp.md.spi.model.po.ct.CtHeadTrPO;
import io.terminus.erp.md.spi.model.po.ct.GenCtAttachmentLinkTrPO;
import io.terminus.erp.md.spi.model.po.vend.GenUserVendLinkCfPO;
import io.terminus.erp.md.spi.model.po.vend.GenVendInfoMdPO;
import io.terminus.erp.md.spi.util.MD;
import io.terminus.trantor2.code.request.TakeCodeRequest;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.meta.objects.MetaKey;
import io.terminus.trantor2.module.service.OSSService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Strings;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同基础service
 *
 * @className: CtBaseService
 * @author: charl
 * @date: 2023/9/5 19:48
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CtBaseService {

    private final ConCtSignchannalCfRepo conCtSignchannalCfRepo;

    private final ConCtSignTypeConfRepo conCtSignTypeConfRepo;

    private final IdGenerator idGenerator;

    private final CtHeadTrRepo ctHeadTrRepo;

    private final GenCtHeadTrExtConverter genCtHeadTrExtConverter;

    private final GenAttachmentTypeCfRepo genAttachmentTypeCfRepo;

    private final CtOssUtil ctOssUtil;

    private final OSSService ossService;

    private final CloudClient cloudClient;

    private final GenComTypeCfRepo genComTypeCfRepo;

    private final GenVendInfoMdRepo genVendInfoMdRepo;

    private final  HtmlToPdfService htmlToPdfService;

    /**
     * 获取路由标识
     *
     * @param request
     * @return
     */
    public String getRouteKey(ConCtSigntaskTrPO request) {
        LambdaQueryWrapper<ConCtSignTypeConfPO> signTypeQueryWrapper = new LambdaQueryWrapper<>();
        signTypeQueryWrapper.eq(ConCtSignTypeConfPO::getId, request.getSignWay());
        ConCtSignTypeConfPO conCtSignTypeConfPO = conCtSignTypeConfRepo.selectOne(signTypeQueryWrapper);

        LambdaQueryWrapper<ConCtSignchannalCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSignchannalCfPO::getId, conCtSignTypeConfPO.getChannel());
        ConCtSignchannalCfPO conCtSignchannalCfPO = conCtSignchannalCfRepo.selectOne(queryWrapper);
        if (Objects.isNull(conCtSignchannalCfPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_ESIGNATURE_IS_NULL);
        }
        return conCtSignchannalCfPO.getRouteKey();
    }

    /**
     * 获取供应商公司对应的用户id
     *
     * @param companyId
     * @return
     */
    public Long getUserBySupplierCompany(Long companyId) {
        GenVendInfoMdPO queryVendInfo = new GenVendInfoMdPO();
        queryVendInfo.setPurComId(companyId);
        GenVendInfoMdPO genVendInfoMdPO = MD.queryByUk(queryVendInfo, GenVendInfoMdPO.class);
        if (Objects.isNull(genVendInfoMdPO)) {
            return null;
        }
        GenUserVendLinkCfPO userVendLinkcf = new GenUserVendLinkCfPO();
        userVendLinkcf.setVendId(genVendInfoMdPO.getId());
        GenUserVendLinkCfPO genUserVendLinkCfPO = MD.queryByUk(userVendLinkcf, GenUserVendLinkCfPO.class);
        if (Objects.isNull(genUserVendLinkCfPO)) {
            return null;
        }
        return genUserVendLinkCfPO.getUserId();
    }


    /**
     * 获取模型的编码
     *
     * @param modelClass
     * @return
     */
    public String generateModelCode(Class<? extends BaseModel> modelClass) {
        return generateModelCode(modelClass, null);
    }

    /**
     * 根据模型的MetaKey、TableName来拼接标识： {模块}${表名}
     *
     * @param modelClass
     * @param moduleName
     * @return
     */
    public String generateModelCode(Class<? extends BaseModel> modelClass, String moduleName) {
        TakeCodeRequest request = new TakeCodeRequest();
        request.setTeamId(TrantorContext.getTeamId());
        request.setAppId(TrantorContext.getAppId());
        MetaKey metaKey = modelClass.getAnnotation(MetaKey.class);
        TableName tableName = modelClass.getAnnotation(TableName.class);
        if (metaKey != null) {
            request.setModelKey(metaKey.key());
        } else if (!Strings.isNullOrEmpty(moduleName) && tableName != null) {
            request.setModelKey(moduleName + "$" + tableName.value());
        } else {
            throw new BusinessException("ct.gen.code.fail", "Fail to get metaKey from " + modelClass);
        }
        return idGenerator.nextCode(request);
    }

    /**
     * 查询合同
     *
     * @param id
     * @return
     */
    public GenCtHeadTrExtDTO selectContract(Long id) {
        final CtHeadTrPO ctHeadTrPO = ctHeadTrRepo.selectById(id);
        if (Objects.isNull(ctHeadTrPO)) {
            throw new BusinessException("合同不存在");
        }
        return genCtHeadTrExtConverter.convert(ctHeadTrPO);
    }

    /**
     * 富文本HTML转PDF
     *
     * @param request
     * @return
     */
    public RichTextToPdfDTO richTextToPdf(RichTextToPdfDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getRichText())) {
            throw new BusinessException("富文本内容为空，无法生成pdf.");
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("查询编制合同文件");
        List<GenAttachmentTypeCfPO> genAttachmentTypeCfPOS = genAttachmentTypeCfRepo.selectList(new LambdaQueryWrapper<>());
        Optional<GenAttachmentTypeCfPO> signedType = genAttachmentTypeCfPOS.stream().filter(v -> v.getAttachmentCode().equals("FTYPE0006")).findFirst();
        stopWatch.stop();
        if (!signedType.isPresent()) {
            throw new BusinessException("无法获取文件类型配置，请配置后再操作.");
        }
        stopWatch.start("html转pdf");

        String bucket = ctOssUtil.getBucket(null);

        // 合同名称
        CtHeadTrPO ctHeadTrPO = ctHeadTrRepo.selectById(request.getCtHeadRef());
        // 查询甲方
        GenComTypeCfPO genComTypeCfPO = genComTypeCfRepo.selectById(ctHeadTrPO.getPrtnA());
        // 查询乙方
        GenVendInfoMdPO genVendInfoMdPO = genVendInfoMdRepo.selectById(ctHeadTrPO.getPrtnB());
        String fileName = getTheDraftedFileName(ctHeadTrPO, genComTypeCfPO, genVendInfoMdPO);

        //String fileUrl = HtmlToPDFUtil.html2PDF(request.getRichText(), bucket, fileName, ossService);
        String fileUrl = htmlToPdfService.html2PDF(request.getRichText(), bucket, fileName);

        stopWatch.stop();
        request.setRichText("");
        request.setPdfFileLink(fileUrl);
        log.info("richTextToPdf :{}", stopWatch.prettyPrint());
        return request;
    }

    /**
     * 构造编制的合同文件的文件名
     *
     * @param ctHeadTrPO
     * @param genComTypeCfPO
     * @param genVendInfoMdPO
     * @return
     */
    private static String getTheDraftedFileName(CtHeadTrPO ctHeadTrPO, GenComTypeCfPO genComTypeCfPO, GenVendInfoMdPO genVendInfoMdPO) {
        return "合同:" + ctHeadTrPO.getCtCode() + " 甲方:" + genComTypeCfPO.getName() + " 乙方:" + genVendInfoMdPO.getName() + "的签署文件";
    }

    /**
     * 给合同文件追加签署图片:
     * 1. 下载合同文件
     * 2. 编辑合同文件追加图片，并保存在本地
     * 3. 上传OSS得到新合同文件连接
     *
     * @param attachmentLinks
     * @param signatoryImages: key是审批流程节点，value是签名图片连接（可直接下载）
     * @return List<GenCtAttachmentLinkTrPO>，将新的链接返回回去
     */
    public List<GenCtAttachmentLinkTrPO> appendSignImage(List<GenCtAttachmentLinkTrPO> attachmentLinks, LinkedList<PDFUtil.SignatureImage> signatoryImages) {

        // download file from URL，然后构造ImageData
        for (PDFUtil.SignatureImage signatureImage : signatoryImages) {
            if (Strings.isNullOrEmpty(signatureImage.getImageUrl())) {
                continue;
            }
            File file = ctOssUtil.downloadFileFromUrl(signatureImage.getImageUrl());
            signatureImage.setImageFile(file);
        }

        // 需要删除过程中产生的临时文件
        final List<File> oldContractFile = new ArrayList<>();
        final List<File> newContractFile = new ArrayList<>();

        // 生成了新合同文件后，需要把新链接更新到数据库中
        List<GenCtAttachmentLinkTrPO> newAttachmentLinkToUpdate = attachmentLinks.stream().map(v -> {
            URL url = null;
            String fileName = null;
            try {
                String attachmentUrl = v.getAttachmentUrl();
                url = new URL(attachmentUrl);
                String path = url.getPath();
                fileName = path.substring(path.lastIndexOf('/') + 1);
            } catch (MalformedURLException e) {
                log.error("URL不合法：", e);
                throw new BusinessException("URL不合法：", e);
            }
            File contractFile = ctOssUtil.getFileFromOSS(url, "pdf");
            Path newContractFilePath = PDFUtil.addSignatureImg(contractFile, signatoryImages);

            oldContractFile.add(contractFile);
            newContractFile.add(newContractFilePath.toFile());

            InputStream inputStream = null;
            try {
                inputStream = Files.newInputStream(newContractFilePath);
            } catch (IOException e) {
                log.error("open failed: ", e);
            }
            String fileUrl = ctOssUtil.uploadWithFileNameAndGetPublicUrl(inputStream, fileName, null);
            log.info("appendSignImage fileUrl: {}", fileUrl);
            GenCtAttachmentLinkTrPO po = new GenCtAttachmentLinkTrPO();
            po.setId(v.getId());
            po.setAttachmentUrl(JSON.toJSONString(Lists.newArrayList(fileUrl)));
            return po;
        }).collect(Collectors.toList());


        CtOssUtil.deleteFiles(oldContractFile);
        CtOssUtil.deleteFiles(newContractFile);

        return newAttachmentLinkToUpdate;
    }

}
