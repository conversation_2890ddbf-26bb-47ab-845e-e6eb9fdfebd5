package io.terminus.erp.contract.domain.service;

import io.terminus.cloud.storage.core.client.CloudClient;
import io.terminus.thirdparty.common.constant.ThirdPartyChannel;
import io.terminus.thirdparty.sign.api.client.SignClient;
import io.terminus.thirdparty.sign.api.dto.request.DownloadContractRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.sdk.common.utils.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 *
 * 回调服务
 *
 * @className: CtESignCallBackService
 * @author: charl
 * @date: 2023/10/15 14:38
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class CtESignCallBackService {

    private final SignClient signClient;
    private final CloudClient cloudClient;
    private final CtOssUtil ctOssUtil;
    @Value("${cloud.storage.publicBucket:terminus-new-trantor}")
    private String bucketName;

    public void qysCallBack(String contractId, String status) {
        // 签署完成
        if (status.equals("COMPLETE")) {
            File file = null;
            try {
                // 下载已签署文件包
                file = File.createTempFile(contractId, ".zip");
                OutputStream outputStream = new FileOutputStream(file);
                DownloadContractRequest downloadContractRequest = new DownloadContractRequest();
                downloadContractRequest.setContractId(Long.valueOf(contractId));
                downloadContractRequest.setChannel(ThirdPartyChannel.QYS_PRIVATE);
                downloadContractRequest.setOutputStream(outputStream);
                signClient.download(downloadContractRequest);
                IOUtils.safeClose(outputStream);
                String bucket = ctOssUtil.getBucket(null);
                cloudClient.uploadFile(bucket, file.getName(), new FileInputStream(file), null);
                String fileUrl = cloudClient.getFileUrl(bucket, file.getName());
                log.info("qys call back service success,contractId is: {}, status: {}, fileUrl: {}", contractId, status, fileUrl);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } finally {
                // 删除临时文件
                if (file != null) {
                    file.delete();
                }
            }

        }
    }

}
