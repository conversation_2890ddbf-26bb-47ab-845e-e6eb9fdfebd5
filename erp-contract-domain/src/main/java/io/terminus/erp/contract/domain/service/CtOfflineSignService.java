package io.terminus.erp.contract.domain.service;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryTrSignatoryStatusDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSignatoryTrSignatoryTypeDict;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignatoryTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSigntaskTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtHeadTrExtDTO;
import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import io.terminus.erp.md.spi.model.dto.ct.CtHeadTrDTO;
import io.terminus.erp.md.spi.model.po.base.GenComTypeCfPO;
import io.terminus.erp.md.spi.util.MD;
import io.terminus.trantor2.common.user.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;

/**
 * 纸板提交流程:
 * 包含签署任务创建相关逻辑
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CtOfflineSignService {

    private final CtBaseService ctBaseService;

    private final CtSignTaskService signTaskService;


    /**
     * 纸版提交流程：
     * 无需走审批，只有甲方签署(即便是多甲方也是只合同头上的甲方作为签署方)，且仅需要线下签署
     * @param request
     * @return
     */
    public ConCtSigntaskTrDTO createOfflineSignTaskByContract(CtHeadTrDTO request) {
        // 校验是否存在
        final long contractId = request.getId();
        signTaskService.checkSignTaskExisted(contractId);

        // 1.查询合同信息
        GenCtHeadTrExtDTO headTrExtDTO = ctBaseService.selectContract(contractId);
        // 2.创建签署任务
        ConCtSigntaskTrDTO signTask = signTaskService.createSignTask(request, headTrExtDTO, contractId, true);
        final long taskId = signTask.getId();
        // 3.甲方签署信息
        ConCtSignatoryTrDTO signatoryTrDTO = createPartyASignatoryForOffline(headTrExtDTO, taskId, signTask, contractId);

        // 4.合同文件
        signTaskService.createSignFile(taskId, signTask, contractId);

        // 5. 通知甲方
        signTaskService.noticeSign(Lists.newArrayList(signatoryTrDTO), MoreObjects.firstNonNull(headTrExtDTO.getCtName(), headTrExtDTO.getCtCode()), false);
        log.info("签署任务创建完成");
        return signTask;
    }


    /**
     * 纸板提交流：创建甲方签署方、签署文件，以及通知签署人进行签署
     * @param headTrExtDTO
     * @param taskId
     * @param signTask
     * @param contractId
     * @return
     */
    private ConCtSignatoryTrDTO createPartyASignatoryForOffline(GenCtHeadTrExtDTO headTrExtDTO, long taskId, ConCtSigntaskTrDTO signTask, long contractId) {
        GenComTypeCfPO genComTypeCfPO = MD.queryById(headTrExtDTO.getPrtnA(), GenComTypeCfPO.class);
        if (Objects.isNull(genComTypeCfPO)) {
            throw new BusinessException("甲方公司不存在");
        }
        // 甲方签署方信息
        ConCtSignatoryTrDTO signatoryTrDTO = buildPartyASignatoryForOffline(headTrExtDTO, taskId, genComTypeCfPO);
        signTask.setSignatories(Collections.singletonList(signatoryTrDTO));
        signTaskService.batchSaveSignatories(signTask);

        return signatoryTrDTO;
    }


    /**
     * 纸板提交流
     * @param headTrExtDTO
     * @param taskId
     * @param genComTypeCfPO
     * @return
     */
    static ConCtSignatoryTrDTO buildPartyASignatoryForOffline(GenCtHeadTrExtDTO headTrExtDTO, long taskId, GenComTypeCfPO genComTypeCfPO) {
        ConCtSignatoryTrDTO signatoryTrDTO = new ConCtSignatoryTrDTO();
        signatoryTrDTO.setSignTask(taskId);
        GenComTypeCfDTO genComTypeCfDTO = new GenComTypeCfDTO();
        genComTypeCfDTO.setId(genComTypeCfPO.getId());
        signatoryTrDTO.setSignatory(genComTypeCfDTO);
        signatoryTrDTO.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.WAITING);
        signatoryTrDTO.setSortNum(1);
        if (Objects.nonNull(headTrExtDTO.getExtWqCtDrafter())) {
            User user = new User();
            user.setId(headTrExtDTO.getExtWqCtDrafter());
            signatoryTrDTO.setAgent(user);
        } else {
            throw new BusinessException("缺少合同编制人");
        }
        signatoryTrDTO.setSignatoryType(ConCtSignatoryTrSignatoryTypeDict.PARTYA);
        // 保证线下签署的时候，只有一个签署方
        signatoryTrDTO.setSortNum(1);
        return signatoryTrDTO;
    }
}
