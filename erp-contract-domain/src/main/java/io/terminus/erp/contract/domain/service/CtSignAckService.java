package io.terminus.erp.contract.domain.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.terminus.cloud.storage.core.client.CloudClient;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.contract.domain.mq.dto.SignTaskMsg;
import io.terminus.erp.contract.domain.mq.producer.SignTaskProducer;
import io.terminus.erp.contract.infrastructure.repo.tp.*;
import io.terminus.erp.contract.spi.convert.tp.ConCtSigntaskTrConverter;
import io.terminus.erp.contract.spi.dict.tp.*;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSigntaskTrDTO;
import io.terminus.erp.contract.spi.model.tp.po.*;
import io.terminus.erp.contract.spi.msg.CtSignMsg;
import io.terminus.erp.md.spi.model.po.base.GenComTypeCfPO;
import io.terminus.erp.md.spi.util.MD;
import io.terminus.thirdparty.common.constant.ThirdPartyChannel;
import io.terminus.thirdparty.common.result.ThirdPartyResponse;
import io.terminus.thirdparty.common.util.JacksonUtils;
import io.terminus.thirdparty.sign.api.client.SignClient;
import io.terminus.thirdparty.sign.api.dto.CompanyDetailResponse;
import io.terminus.thirdparty.sign.api.dto.GetDownloadFileUrlResponse;
import io.terminus.thirdparty.sign.api.dto.GetDownloadFileUrlResponse.ThirdFile;
import io.terminus.thirdparty.sign.api.dto.QueryContractDetailResponse;
import io.terminus.thirdparty.sign.api.dto.request.DownloadContractRequest;
import io.terminus.thirdparty.sign.api.dto.request.GetDownloadFileUrlRequest;
import io.terminus.thirdparty.sign.esign.constant.ESignAckEventType;
import io.terminus.thirdparty.sign.esign.dto.OrgAuthAckRequest;
import io.terminus.thirdparty.sign.esign.dto.SignTaskAckRequest;
import io.terminus.thirdparty.sign.esign.dto.SignTaskAckResponse;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.utils.JsonUtil;
import io.terminus.trantor2.module.service.OSSService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 回调服务
 *
 * @className: CtSignAckService
 * @author: charl
 * @date: 2023/8/2 19:27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CtSignAckService {

    @Value("${cloud.storage.publicBucket:terminus-new-trantor}")
    private String bucketName;


    private final CtOssUtil ctOssUtil;
    private final ConCtEsignatureTrRepo conCtEsignatureTrRepo;
    private final ConCtSignatoryTrRepo conCtSignatoryTrRepo;
    private final CtSignTaskService signTaskService;
    private final ConCtSigntaskTrConverter conCtSigntaskTrConverter;
    private final ConCtSignfileTrRepo conCtSignfileTrRepo;
    private final CtBaseService ctBaseService;
    private final SignClient signClient;
    private final CloudClient cloudClient;
    private final SignTaskProducer signTaskProducer;
    private final CtSignTaskService ctSignTaskService;
    private final ConSignatoryAccountUsedRepo conSignatoryAccountUsedRepo;
    private final ConCtSignatoryAccountTrRepo conCtSignatoryAccountTrRepo;
    private final QysSignService qysSignService;
    private final OSSService ossService;

    public void orgAuthAck(OrgAuthAckRequest request) {
        //校验参数
        if (Objects.isNull(request)
                || Objects.isNull(request.getAuthFlowId())
                || StringUtils.isBlank(request.getOrgId())
                || Objects.isNull(request.getPsnId())) {
            throw new BusinessException(CtSignMsg.CON_CT_ESIGNATURE_IS_NULL);
        }
        if (!OrgAuthAckRequest.AUTHORIZE_FINISH.equalsIgnoreCase(request.getAction())) {
            throw new BusinessException(CtSignMsg.CON_CT_ESIGNATURE_IS_NULL);
        }
        //根据authFlowId查询SignatureBO
        LambdaQueryWrapper<ConCtEsignatureTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtEsignatureTrPO::getAuthFlowId, request.getAuthFlowId());
        List<ConCtEsignatureTrPO> conCtEsignatureTrPOS = conCtEsignatureTrRepo.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(conCtEsignatureTrPOS) || conCtEsignatureTrPOS.size() != 1) {
            log.error("signatureBO not unique");
            return;
        }
        if (ConCtEsignatureTrEsignatureStatusDict.SUCCESS.equalsIgnoreCase(conCtEsignatureTrPOS.get(0).getEsignatureStatus())) {
            log.info("signature status enable");
            return;
        }

        ConCtEsignatureTrPO conCtEsignatureTrPO = new ConCtEsignatureTrPO();
        conCtEsignatureTrPO.setId(conCtEsignatureTrPOS.get(0).getId());
        conCtEsignatureTrPO.setOrgId(request.getOrgId());
        conCtEsignatureTrPO.setPsnId(request.getPsnId());
        conCtEsignatureTrPO.setEsignatureStatus(ConCtEsignatureTrEsignatureStatusDict.SUCCESS);
        conCtEsignatureTrRepo.updateById(conCtEsignatureTrPO);
    }

    public SignTaskAckResponse eSignTaskAck(SignTaskAckRequest request) {
        //参数校验
        if (org.apache.commons.lang.StringUtils.isBlank(request.getAction())
                || org.apache.commons.lang.StringUtils.isBlank(request.getSignFlowId())) {
            log.error("require params is null:{}", JSON.toJSONString(request));
            return SignTaskAckResponse.builder().code("400").msg("require params is null").build();
        }

        SignTaskAckResponse res = new SignTaskAckResponse();
        //签署人签署完成通知回调
        // MISSON单词是e签包平台的单词拼写错误，正确的应该是MISSION。（https://open.esign.cn/doc/opendoc/pdf-sign3/mcm1c9487grz0ynt）
        if (ESignAckEventType.SIGN_MISSON_COMPLETE.equals(request.getAction())) {
            res = esignMissionCompleteFunc(request);
        }
        //流程结束回调
        if (ESignAckEventType.SIGN_FLOW_COMPLETE.equals(request.getAction())) {
            res = esignFlowComplete(request);
        }
        return res;
    }

    public SignTaskAckResponse esignMissionCompleteFunc(SignTaskAckRequest request) {
        //查询ConCtSignatoryTrPO
        ConCtSigntaskTrPO conCtSigntaskTrPO = signTaskService.getCtSignTaskByFlowId(request.getSignFlowId());
        if (Objects.isNull(conCtSigntaskTrPO)) {
            log.error("sign task is null,signFlowId:{}", request.getSignFlowId());
            return SignTaskAckResponse.builder().code("400").msg("sign task is null").build();
        }

        //查询ConCtSignatoryTrPO
        ConCtSignatoryTrPO conCtSignatoryTrPO = getCtSignatory(request, conCtSigntaskTrPO);
        if (Objects.isNull(conCtSignatoryTrPO)) {
            log.error("signatories info is null,request:{}", JSON.toJSONString(request));
            return SignTaskAckResponse.builder().code("400").msg("signatories info is null").build();
        }
        // 更新签署方状态

        String signatoriesStatusDict = request.getSignResult() == 2 ? ConCtSignatoryTrSignatoryStatusDict.SIGNED : ConCtSignatoryTrSignatoryStatusDict.REJECTED;

        if (signatoriesStatusDict.equals(ConCtSignatoryTrSignatoryStatusDict.SIGNED)) {
            // 更新签署人状态为已签署
            ctSignTaskService.updateSignatoryToSigned(conCtSigntaskTrPO.getId());
            // 针对签署任务的后置处理逻辑
            postUpdateSignTask(request, conCtSigntaskTrPO);
        }

        if (signatoriesStatusDict.equals(ConCtSignatoryTrSignatoryStatusDict.REJECTED)) {
            // 更新签署人状态为已关闭
            ctSignTaskService.updateSignatoryRejected(conCtSigntaskTrPO.getId());
            // 更新签署任务状态为已关闭
            ctSignTaskService.updateSignTaskToClosed(conCtSigntaskTrPO.getId());
            // 发送签署任务关闭事件
            ctSignTaskService.publishSignTaskClosed(conCtSigntaskTrPO);
        }
        return SignTaskAckResponse.builder().code("200").msg("success").build();
    }

    private ConCtSignatoryTrPO getCtSignatory(SignTaskAckRequest request, ConCtSigntaskTrPO conCtSigntaskTrPO) {
        LambdaQueryWrapper<ConCtSignatoryTrPO> querySignatoryWrapper = new LambdaQueryWrapper<>();
        querySignatoryWrapper.eq(ConCtSignatoryTrPO::getSortNum, request.getSignOrder());
        querySignatoryWrapper.eq(ConCtSignatoryTrPO::getSignTask, conCtSigntaskTrPO.getId());
        ConCtSignatoryTrPO conCtSignatoryTrPO = conCtSignatoryTrRepo.selectOne(querySignatoryWrapper);
        return conCtSignatoryTrPO;
    }

    /**
     * 1. 检查是否全部签署，如全部签署则更新签署任务状态为已签署
     * 2. 检查当前顺序的签署人是否都已签署，如是，则推进下一个顺序的签署人为待签署状态
     * @param request
     * @param conCtSigntaskTrPO
     */
    private void postUpdateSignTask(SignTaskAckRequest request, ConCtSigntaskTrPO conCtSigntaskTrPO) {
        // 是否是最后一个签署人
        // 所有的签署方
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = queryCtSignatories(conCtSigntaskTrPO.getId());

        // 是否所有签署人都已签署
        boolean allSigned = ctSignTaskService.isAllSigned(conCtSignatoryTrPOS);
        if (allSigned) {
            log.info("signTask [{}] is all signed, begin to mark sign task signed", conCtSigntaskTrPO.getId());
            ctSignTaskService.updateSignTaskToSigned(conCtSigntaskTrPO.getId());
            downloadSignFile(conCtSigntaskTrPO);
        } else {

            // fixme :
            // 1. 需要检查当前顺序下的都签署了，才会设置下一个顺序的为待签署
            // 2. 下一个顺序的签署人可能会有多个

            //未全部签署情况:
            //1.定义待执行状态更新的签署方任务信息
            ConCtSignatoryTrPO nextSignatoriesBO = new ConCtSignatoryTrPO();
            //2.对全部签署方信息按照sortNum排序
            List<ConCtSignatoryTrPO> sortedSignatoriesBOList = conCtSignatoryTrPOS.stream().sorted(Comparator.comparing(ConCtSignatoryTrPO::getSortNum)).collect(Collectors.toList());
            for (int i = 0; i < sortedSignatoriesBOList.size(); i++) {
                //2.1当前签署方信息
                if (request.getSignOrder() == sortedSignatoriesBOList.get(i).getSortNum()) {
                    nextSignatoriesBO = sortedSignatoriesBOList.get(i + 1);
                }
            }
            //3.更新下一个签署方状态信息
            ctSignTaskService.updateSignatoryToWaiting(nextSignatoriesBO.getId());
        }
    }



    public void downloadSignFile(ConCtSigntaskTrPO conCtSigntaskTrPO) {
        log.info("step in downloadSignFile");
        //校验参数
        if (StringUtils.isBlank(conCtSigntaskTrPO.getSignFlowId()) || Objects.isNull(conCtSigntaskTrPO.getId())) {
            throw new BusinessException("params is null");
        }
        //查询当前签署任务文件信息
        LambdaQueryWrapper<ConCtSignfileTrPO> signFileQueryWrapper = new LambdaQueryWrapper<>();
        signFileQueryWrapper.eq(ConCtSignfileTrPO::getSignTask, conCtSigntaskTrPO.getId());
        List<ConCtSignfileTrPO> signFileBOList = conCtSignfileTrRepo.selectList(signFileQueryWrapper);
        if (CollectionUtils.isEmpty(signFileBOList)) {
            log.error("当前签署任务:{},不存在签署文件信息", conCtSigntaskTrPO.getId());
            return;
        }

        // 获取路由标识
        String routeKey = ctBaseService.getRouteKey(conCtSigntaskTrPO);

        //从e签宝下载
        if (routeKey.equals(ThirdPartyChannel.E_SIGN_CLOUD)) {
            GetDownloadFileUrlRequest downloadFileUrlRequest = new GetDownloadFileUrlRequest();
            downloadFileUrlRequest.setSignFlowId(conCtSigntaskTrPO.getSignFlowId());
            downloadFileUrlRequest.setChannel(routeKey);
            ThirdPartyResponse<GetDownloadFileUrlResponse> downloadFileResponse = signClient.getDownloadFileUrl(downloadFileUrlRequest);
            if (!downloadFileResponse.isSuccess()) {
                log.error("download signed file fail:{}", downloadFileResponse.getMessage());
                throw new BusinessException("download signed file fail");
            }

            GetDownloadFileUrlResponse fileUrlResponse = downloadFileResponse.getData();
            uploadFile2OSS(fileUrlResponse.getThirdFiles(), signFileBOList);
        }
        //从契约锁下载
        User currentUser = TrantorContext.getCurrentUser();
        if (routeKey.equals(ThirdPartyChannel.QYS_PRIVATE)) {
            for (ConCtSignfileTrPO conCtSignfileTrPO : signFileBOList) {
                //从契约锁下载
                GetDownloadFileUrlRequest downloadFileUrlRequest = new GetDownloadFileUrlRequest();
                downloadFileUrlRequest.setSignFlowId(conCtSignfileTrPO.getThirdFileId());
                downloadFileUrlRequest.setOperatorMobile(currentUser.getMobile());
                // 代表长期有效
                downloadFileUrlRequest.setExpireTime(0l);
                downloadFileUrlRequest.setChannel(routeKey);
                ThirdPartyResponse<GetDownloadFileUrlResponse> downloadFileResponse = signClient.getDownloadFileUrl(downloadFileUrlRequest);
                if (!downloadFileResponse.isSuccess()) {
                    log.error("download signed file fail:{}", downloadFileResponse.getMessage());
                    throw new BusinessException("download signed file fail");
                }
                uploadFile2OSS(downloadFileResponse.getData(), conCtSignfileTrPO);
            }
        }
    }

    private void uploadFile2OSS(GetDownloadFileUrlResponse fileUrlResponse, ConCtSignfileTrPO signfileTrPO) {
        //从e签宝读取文件
        URL urlFile = null;
        try {
            urlFile = new URL(fileUrlResponse.getDownloadUrl());
            HttpURLConnection httpUrl = (HttpURLConnection) urlFile.openConnection();
            httpUrl.connect();
            BufferedInputStream bis = new BufferedInputStream(httpUrl.getInputStream());
            //用于上传和下载oss的文件名
            String upAndDownFileName = String.valueOf(RandomUtils.nextLong());
            String fileOssURL = ctOssUtil.uploadWithFileName(bis, upAndDownFileName, null);
            log.info("ossUploadUrl is:{}", fileOssURL);
            signfileTrPO.setSignedFile(fileOssURL);
            conCtSignfileTrRepo.updateById(signfileTrPO);
        } catch (Exception e) {
            log.error("upload file 2 oss error:", e.getCause());
            throw new BusinessException("upload file 2 oss error");
        }
    }

    private void uploadFile2OSS(List<ThirdFile> esFiles, List<ConCtSignfileTrPO> signFileBOList) {
        log.info("signFileBOList is:{}", JacksonUtils.obj2jsonIgnoreException(signFileBOList));
        log.info("esFiles is:{}", JacksonUtils.obj2jsonIgnoreException(esFiles));
        List<ConCtSignfileTrPO> updateSignFileList = new ArrayList<>();
        //根据签署文件ID分组
        Map<String, List<ConCtSignfileTrPO>> fileIdMap = signFileBOList.stream().collect(Collectors.groupingBy(ConCtSignfileTrPO::getThirdFileId));
        for (ThirdFile esFile : esFiles) {
            try {
                if (fileIdMap.containsKey(esFile.getFileId())) {
                    //从e签宝读取文件
                    URL urlFile = new URL(esFile.getDownloadUrl());
                    HttpURLConnection httpUrl = (HttpURLConnection) urlFile.openConnection();
                    httpUrl.connect();
                    BufferedInputStream bis = new BufferedInputStream(httpUrl.getInputStream());
                    //用于上传和下载oss的文件名
                    String upAndDownFileName = String.valueOf(RandomUtils.nextLong());
                    String fileOssURL = ctOssUtil.uploadWithFileName(bis, upAndDownFileName, null);
                    log.info("ossUploadUrl is:{}", fileOssURL);
                    //获取当前签署文件对象信息
                    ConCtSignfileTrPO existSignFile = fileIdMap.get(esFile.getFileId()).get(0);
                    //构建更新签署文件对象集合
                    ConCtSignfileTrPO updateSignFile = new ConCtSignfileTrPO();
                    updateSignFile.setId(existSignFile.getId());
                    updateSignFile.setSignedFile(fileOssURL);
                    updateSignFileList.add(updateSignFile);
                }
            } catch (Exception e) {
                log.error("download file fail:{}", e);
            }
        }
        //更新签署文件信息
        conCtSignfileTrRepo.updateBatch(updateSignFileList);
    }


    public SignTaskAckResponse esignFlowComplete(SignTaskAckRequest signTaskAckRequestTO) {
        log.info("step in EsignFlowCompleteFuncImpl");
        //查询ConCtSignatoryTrPO
        ConCtSigntaskTrPO conCtSigntaskTrPO = signTaskService.getCtSignTaskByFlowId(signTaskAckRequestTO.getSignFlowId());
        if (Objects.isNull(conCtSigntaskTrPO)) {
            log.error("sign task is null,signFlowId:{}", signTaskAckRequestTO.getSignFlowId());
            return SignTaskAckResponse.builder().code("400").msg("sign task is null").build();
        }
        //signTaskAckRequestTO.getSignResult()==2-已完成: 所有签署人完成签署；
        if ("2".equals(signTaskAckRequestTO.getSignFlowStatus())) {
            //签署完成通知,直接返回
            // 任务状态推送外部
            ctSignTaskService.publishSignTaskClosed(conCtSigntaskTrPO);
            return SignTaskAckResponse.builder().code("200").msg("success").build();
        }
        //查询ConCtSignatoryTrPO
        LambdaQueryWrapper<ConCtSignatoryTrPO> querySignatoryWrapper = new LambdaQueryWrapper<>();
        querySignatoryWrapper.eq(ConCtSignatoryTrPO::getSignatoryStatus, ConCtSignatoryTrSignatoryStatusDict.WAITING);
        querySignatoryWrapper.eq(ConCtSignatoryTrPO::getSignTask, conCtSigntaskTrPO.getId());
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrRepo.selectList(querySignatoryWrapper);
        if (CollectionUtils.isEmpty(conCtSignatoryTrPOS)) {
            log.error("signatories info is null,request:{}", JSON.toJSONString(signTaskAckRequestTO));
            return SignTaskAckResponse.builder().code("400").msg("signatories info is null").build();
        }

        // signTaskAckRequestTO.getSignResult()==3-已撤销: 发起方撤销签署任务；
        // signTaskAckRequestTO.getSignResult()==5-已过期: 签署截止日到期后触发；
        // signTaskAckRequestTO.getSignResult()==7-已拒签
        String signatoriesStatusDict;
        if ("3".equals(signTaskAckRequestTO.getSignFlowStatus()) || "7".equals(signTaskAckRequestTO.getSignFlowStatus())) {
            signatoriesStatusDict = ConCtSignatoryTrSignatoryStatusDict.REJECTED;
        } else {
            signatoriesStatusDict = ConCtSignatoryTrSignatoryStatusDict.CLOSED;
        }
        //1.更新签署方状态
        conCtSignatoryTrPOS.forEach(signatories -> {
            ConCtSignatoryTrPO updateSignatoriesBO = new ConCtSignatoryTrPO();
            updateSignatoriesBO.setId(signatories.getId());
            updateSignatoriesBO.setSignatoryStatus(signatoriesStatusDict);
            updateSignatoriesBO.setUpdatedAt(LocalDateTime.now());
            conCtSignatoryTrRepo.updateById(updateSignatoriesBO);
        });
        //2.更新签署任务状态为已关闭，合同状态为已创建
        //2.1更新签署任务状态
        ctSignTaskService.updateSignTaskToClosed(conCtSigntaskTrPO.getId());

        // 任务状态推送外部
        ctSignTaskService.publishSignTaskClosed(conCtSigntaskTrPO);

        return SignTaskAckResponse.builder().code("200").msg("success").build();
    }

    /**
     * 契约锁回调接口
     *
     * @param contractFlowId
     */
    public void qysCallback(String contractFlowId) throws IOException {
        // 下载契约锁文件
        ThirdPartyResponse<QueryContractDetailResponse> contractDetail = signClient.getContractDetail(contractFlowId, ThirdPartyChannel.QYS_PRIVATE);
        if (Objects.isNull(contractDetail)) {
            log.error("qys contract detail response is null");
            return;
        }
        // 查询签署任务
        ConCtSigntaskTrPO conCtSigntaskTrPO = signTaskService.getCtSignTaskByFlowId(contractFlowId);
        if (Objects.isNull(conCtSigntaskTrPO)) {
            log.error("sign task is null,signFlowId:{}", contractFlowId);
            return;
        }
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = conCtSigntaskTrConverter.po2Dto(conCtSigntaskTrPO);
        // 状态
        String status = contractDetail.getData().getStatus().name();

        switch (status) {
            // 撤回
            case "RECALLED":
                // 拒绝
            case "REJECTED":
                // 签署拒绝，推送外部消息
                publishSignTaskRejectedMessage(conCtSigntaskTrDTO);
                break;
            // 完成
            case "COMPLETE":
                processCompleteCallback(contractFlowId, conCtSigntaskTrDTO);
                break;
            // 终止
            case "TERMINATED":
                break;
        }
    }

    private void processCompleteCallback(String contractFlowId, ConCtSigntaskTrDTO task) throws IOException {
        // 查询该签署任务所有签署方
        List<ConCtSignatoryTrPO> ctSignatories = queryCtSignatories(task.getId());
        // 按照甲乙双方进行分组
        Map<String, List<ConCtSignatoryTrPO>> signatoriesGroupByType = ctSignatories.stream().collect(Collectors.groupingBy(ConCtSignatoryTrPO::getSignatoryType));
        // 上传已签署文件
        transferSignedFileToOSS(contractFlowId, task, signatoriesGroupByType);
        // 签署完成，更新签署方状态，如果是最后一个签署方，联动修改签署任务以及合同状态
        ctSignTaskService.processSignatoryWhenESignSuccessAck(task);

        // 更新甲方、乙方各签署方的账户；
        for (List<ConCtSignatoryTrPO> valueList : signatoriesGroupByType.values()) {
            for (ConCtSignatoryTrPO signatory : valueList) {
                updateESignAccount(task, signatory);
            }
        }

    }

    /**
     * 修改账户余额以及记录
     * @param task
     * @param signatoryTrPO
     */
    private void updateESignAccount(ConCtSigntaskTrDTO task, ConCtSignatoryTrPO signatoryTrPO) {
        GenComTypeCfPO genComTypeCfPO = MD.queryById(signatoryTrPO.getSignatory(), GenComTypeCfPO.class);

        // 查询乙方签署账户
        ConCtSignatoryAccountTrPO signatoryAccount = queryCtSignatoryAccount(genComTypeCfPO.getId());
        if (Objects.isNull(signatoryAccount)) {
            return;
        }
        ThirdPartyResponse<CompanyDetailResponse> companyDetail = signClient.getCompanyDetail(genComTypeCfPO.getName(), ThirdPartyChannel.QYS_PRIVATE);
        if (!companyDetail.isSuccess()) {
            return;
        }
        Long companyId = companyDetail.getData().getId();
        ThirdPartyResponse<String> signWayResponse = signClient.getCompanySignWay(companyId, null, null, ThirdPartyChannel.QYS_PRIVATE);

        log.info("updateESignAccount getCompanySignWay: {}", signWayResponse);
        if (!signWayResponse.isSuccess()) {
            throw new BusinessException(signWayResponse.getMessage());

        }

        // 添加扣费记录
        // 计算账户金额
        if (ConSignatoryAccountUsedSignTypeDict.EVENT.equals(signWayResponse.getData())){
            updateAccountBalance(signatoryAccount);
            addEventTypeAccountLog(signatoryAccount, task);
        }else if (ConSignatoryAccountUsedSignTypeDict.UKEY.equals(signWayResponse.getData())){
            addUkeyTypeAccountLog(signatoryAccount, task);
        }else {
            throw new BusinessException("未知的签署方式");
        }
    }

    /**
     * 更新账户余额，减少余额（1元），增加已使用金额（1元）
     * @param signatoryAccount
     */
    private void updateAccountBalance(ConCtSignatoryAccountTrPO signatoryAccount) {
        BigDecimal signatoryAccountAmount = signatoryAccount.getSignatoryAccountAmount();
        BigDecimal sumAmt = signatoryAccountAmount.subtract(BigDecimal.ONE);
        BigDecimal signatoryAccountAmountUsed = signatoryAccount.getSignatoryAccountAmountUsed();
        BigDecimal addAmt = signatoryAccountAmountUsed.add(BigDecimal.ONE);
        // 更新账户余额（乐观锁方式更新）
        LambdaUpdateWrapper<ConCtSignatoryAccountTrPO> accountUpdate = new LambdaUpdateWrapper<>();
        accountUpdate.eq(ConCtSignatoryAccountTrPO::getId, signatoryAccount.getId());
        accountUpdate.eq(ConCtSignatoryAccountTrPO::getSignatoryAccountAmount, signatoryAccountAmount);
        accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAccountAmount, sumAmt);
        accountUpdate.set(ConCtSignatoryAccountTrPO::getSignatoryAccountAmountUsed, addAmt);
        conCtSignatoryAccountTrRepo.update(null, accountUpdate);
    }

    /**
     * EVENT类型是指非套餐账户类型，每次签章要扣减账户余额。消费金额为1
     * @param signatoryAccount
     * @param conCtSigntaskTrPO
     */
    private void addEventTypeAccountLog(ConCtSignatoryAccountTrPO signatoryAccount, ConCtSigntaskTrDTO conCtSigntaskTrPO) {
        addAccountLog(signatoryAccount, conCtSigntaskTrPO, BigDecimal.ONE, ConSignatoryAccountUsedSignTypeDict.EVENT);
    }

    /**
     * UKEY类型是指套餐账户类型，不扣减账户余额。消费金额为0
     * @param signatoryAccount
     * @param conCtSigntaskTrPO
     */
    private void addUkeyTypeAccountLog(ConCtSignatoryAccountTrPO signatoryAccount, ConCtSigntaskTrDTO conCtSigntaskTrPO) {
        addAccountLog(signatoryAccount, conCtSigntaskTrPO, BigDecimal.ZERO, ConSignatoryAccountUsedSignTypeDict.UKEY);
    }

    private void addAccountLog(ConCtSignatoryAccountTrPO signatoryAccount, ConCtSigntaskTrDTO conCtSigntaskTrPO, BigDecimal amt, String type) {
        // 添加扣费记录
        ConSignatoryAccountUsedPO accountUsedPO = new ConSignatoryAccountUsedPO();
        accountUsedPO.setRelSignAccount(signatoryAccount.getId());
        accountUsedPO.setRelBillId(conCtSigntaskTrPO.getSignBillId());
        accountUsedPO.setDeductionAmt(amt);
        accountUsedPO.setDeductionTime(LocalDateTime.now());
        accountUsedPO.setRelBillType(ConSignatoryAccountUsedRelBillTypeDict.CONTRACT);
        accountUsedPO.setSignType(type);
        conSignatoryAccountUsedRepo.insert(accountUsedPO);
    }

    private ConCtSignatoryAccountTrPO queryCtSignatoryAccount(Long companyId) {
        LambdaQueryWrapper<ConCtSignatoryAccountTrPO> accountQuery = new LambdaQueryWrapper<>();
        accountQuery.eq(ConCtSignatoryAccountTrPO::getRelCompany, companyId);
        return conCtSignatoryAccountTrRepo.selectOne(accountQuery);
    }

    private List<ConCtSignatoryTrPO> queryCtSignatories(Long taskId) {
        LambdaQueryWrapper<ConCtSignatoryTrPO> signatoryQuery = new LambdaQueryWrapper<>();
        signatoryQuery.eq(ConCtSignatoryTrPO::getSignTask, taskId);
        return conCtSignatoryTrRepo.selectList(signatoryQuery);
    }

    private void publishSignTaskRejectedMessage(ConCtSigntaskTrDTO task) {
        SignTaskMsg signTaskMsg = SignTaskMsg.builder()
                .signBillType(task.getSignBillType())
                .signTaskId(task.getId())
                .signBillCode(task.getSignBillCode())
                .signBillId(task.getSignBillId())
                .signStatus(ConCtSigntaskTrSignStatusDict.REJECTED).build();
        signTaskProducer.sendMsg(signTaskMsg);
    }

    /**
     * 将契约锁回掉的文件保存起来
     *
     * @param contractFlowId             合同ID
     * @param conCtSigntaskTrDTO     签约任务信息
     * @param conCtSignatoryTrPOSMap 签约供应商信息
     * @throws IOException ex
     */
    private void transferSignedFileToOSS(String contractFlowId, ConCtSigntaskTrDTO conCtSigntaskTrDTO, Map<String, List<ConCtSignatoryTrPO>> conCtSignatoryTrPOSMap) throws IOException {
        // 获取甲方和乙方的签署方式 是线上签署还是线下签署
        ConCtSignatoryTrPO partyA = conCtSignatoryTrPOSMap.get(ConCtSignatoryTrSignatoryTypeDict.PARTYA).get(0);
        ConCtSignatoryTrPO partyB = conCtSignatoryTrPOSMap.get(ConCtSignatoryTrSignatoryTypeDict.PARTYB).get(0);
        String partySignTypeA = partyA.getSignType();
        String partySignTypeB = partyB.getSignType();
        // 下载签署文件，并上传oss
        File tempFile = null;
        InputStream contentStream = null;
        List<File> decompression = null;
        FileInputStream fileInputStream = null;
        try {
            // 1. 根据契约锁的合同ID 获取已经签署好的文件压缩包
            tempFile = File.createTempFile(contractFlowId, ".zip");
            DownloadContractRequest request = new DownloadContractRequest();
            request.setContractId(Long.valueOf(contractFlowId));
            request.setOutputStream(Files.newOutputStream(tempFile.toPath()));
            request.setChannel(ThirdPartyChannel.QYS_PRIVATE);
            ThirdPartyResponse downloadResponse = signClient.download(request);
            // 2. 判断是否成功了
            if (!downloadResponse.isSuccess()) {
                log.error("invoke qys download error:{}", downloadResponse.getMessage());
                throw new RuntimeException(downloadResponse.getMessage());
            }
            log.info("invoke qys download request:{},response:{}", JsonUtil.toJson(request), JsonUtil.toJson(downloadResponse));
            // 3. 将契约锁的文件下载回来 然后转存到SRM平台
            String bucket = ctOssUtil.getBucket(true);
            // 4. 将文件进行解压
            contentStream = Files.newInputStream(tempFile.toPath());
            decompression = decompression(contentStream);
            Map<String, File> fileMap = decompression.stream().collect(Collectors.toMap(File::getName, e -> e));
            // 5. 获取所有的签署文件
            LambdaQueryWrapper<ConCtSignfileTrPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ConCtSignfileTrPO::getSignTask, conCtSigntaskTrDTO.getId());
            List<ConCtSignfileTrPO> conCtSignfileTrPOS = conCtSignfileTrRepo.selectList(queryWrapper);
            // 6. 更新签署文件
            for (ConCtSignfileTrPO conCtSignfileTrPO : conCtSignfileTrPOS) {
                String fileName = conCtSignfileTrPO.getFileName() + ".pdf";
                File file = fileMap.get(fileName);
                if (file == null) {
                    throw new BusinessException("签署文件不存在");
                }
                fileInputStream = new FileInputStream(file);

                MockMultipartFile mockMultipartFile = new MockMultipartFile(fileName, fileName, "application/octet-stream", fileInputStream);
                String url = ossService.uploadFileAndGetUrl(bucket, mockMultipartFile.getOriginalFilename(),
                        mockMultipartFile.getInputStream(), mockMultipartFile.getContentType(), false);

                // 甲方是线上签署
                if (Objects.equals(partySignTypeA, ConCtSignatoryTrPO.SIGN_TYPE_ONLINE)) {
                    conCtSignfileTrPO.setSignedFile(url);
                }
                // 乙方是线上签署
                if (Objects.equals(partySignTypeB, ConCtSignatoryTrPO.SIGN_TYPE_ONLINE)) {
                    conCtSignfileTrPO.setSigningFile(url);
                }

                conCtSignfileTrRepo.updateById(conCtSignfileTrPO);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (Objects.nonNull(tempFile)) {
                tempFile.delete();
            }
            if (Objects.nonNull(contentStream)) {
                contentStream.close();
            }
            if (CollectionUtils.isNotEmpty(decompression)) {
                for (File file : decompression) {
                    file.delete();
                }
            }
            if (Objects.nonNull(fileInputStream)) {
                fileInputStream.close();
            }
        }
    }

    public List<File> decompression(InputStream contentStream) {
        List<File> responesList = new ArrayList<>();
        try (ZipArchiveInputStream zipArchiveInputStream = new ZipArchiveInputStream(contentStream)) {
            ZipArchiveEntry zipArchiveEntry = null;
            while ((zipArchiveEntry = zipArchiveInputStream.getNextZipEntry()) != null) {
                String fileName = zipArchiveEntry.getName();
                File file = new File(fileName);
                if (fileName.endsWith("/")) {
                    boolean mkdir = file.mkdir();
                    if (!mkdir) {
                        throw new BusinessException("系统异常");
                    }
                    continue;
                } else {
                    BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(Files.newOutputStream(file.toPath()));
                    byte[] bytes = new byte[1024];
                    int num;
                    while ((num = zipArchiveInputStream.read(bytes, 0, bytes.length)) > 0) {
                        bufferedOutputStream.write(bytes, 0, num);
                    }
                    bufferedOutputStream.close();
                }
                responesList.add(file);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return responesList;
    }
}
