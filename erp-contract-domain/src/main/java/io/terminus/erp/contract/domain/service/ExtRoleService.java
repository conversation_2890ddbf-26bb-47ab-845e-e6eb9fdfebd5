package io.terminus.erp.contract.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.erp.contract.infrastructure.repo.role.ConOrgRoleTrRepo;
import io.terminus.erp.contract.infrastructure.repo.role.ConRoleItemTrRepo;
import io.terminus.erp.contract.infrastructure.repo.role.DbOrgAdmOrgCfRepo;
import io.terminus.erp.contract.spi.model.role.dto.ConOrgRoleTrDTO;
import io.terminus.erp.contract.spi.model.role.dto.ConRoleItemTrDTO;
import io.terminus.erp.contract.spi.model.role.dto.RoleDTO;
import io.terminus.erp.contract.spi.model.role.po.ConOrgRoleTrPO;
import io.terminus.erp.contract.spi.model.role.po.ConRoleItemTrPO;
import io.terminus.iam.api.request.role.RoleByKeyFindParams;
import io.terminus.iam.api.request.role.RolePageParams;
import io.terminus.iam.api.response.PageResult;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ExtRoleService {

    private final IAMClient iamClient;

    private final ConOrgRoleTrRepo conOrgRoleTrRepo;

    private final ConRoleItemTrRepo conRoleItemTrRepo;

    private final DbOrgAdmOrgCfRepo dbOrgAdmOrgCfRepo;

    public Paging<RoleDTO> pageRole(Pageable pageable) {
        String key = pageable.getConditionValue("key"); // 合同编码
        String name = pageable.getConditionValue("name"); // 合同编码

        RolePageParams rolePageParams = new RolePageParams();
        if (StringUtils.isNotBlank(key)) {
            rolePageParams.setKey(key);
        }
        if (StringUtils.isNotBlank(name)) {
            rolePageParams.setName(name);
        }
        rolePageParams.setNo(pageable.getPageNo());
        rolePageParams.setSize(pageable.getPageSize());
        try {
            PageResult<Role> rolePageResult = iamClient.roleClient().pageRole(rolePageParams).execute();

            List<Role> roleList = rolePageResult.getData();
            List<RoleDTO> roleDTOList = roleList.stream()
                    .map(role -> RoleDTO.builder()
                            .id(role.getId())
                            .key(role.getKey())
                            .name(role.getName())
                            .build())
                    .collect(Collectors.toList());
                    
            return new Paging<>(rolePageResult.getTotal(), roleDTOList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public RoleDTO findByIdRole(RoleDTO roleDTO) {
        try {
            Role role = iamClient.roleClient().getRoleById(roleDTO.getId()).execute();
            return RoleDTO.builder()
                    .id(role.getId())
                    .key(role.getKey())
                    .name(role.getName())
                    .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }   
    }

    public void contractSave(ConOrgRoleTrDTO conOrgRoleTrDTO) {
        // 3. 检查角色行中是否有重复的roleId
        if (conOrgRoleTrDTO.getRoleLine() != null && !conOrgRoleTrDTO.getRoleLine().isEmpty()) {
            List<String> roleIds = conOrgRoleTrDTO.getRoleLine().stream()
                    .map(ConRoleItemTrDTO::getRoleKey)
                    .collect(Collectors.toList());
            if (roleIds.size() != roleIds.stream().distinct().count()) {
                throw new BusinessException("角色行中存在重复的角色");
            }
        }

        // 4. 转换为PO对象并保存/更新头信息
        ConOrgRoleTrPO conOrgRoleTrPO;
        if (conOrgRoleTrDTO.getId() != null) {
            // 更新逻辑
            conOrgRoleTrPO = conOrgRoleTrRepo.selectById(conOrgRoleTrDTO.getId());
            if (conOrgRoleTrPO == null) {
                throw new BusinessException("组织角色信息不存在");
            }
            conOrgRoleTrPO.setOrgCode(conOrgRoleTrDTO.getOrgCode());
            conOrgRoleTrPO.setOrgName(conOrgRoleTrDTO.getOrgName());
            conOrgRoleTrPO.setDesc(conOrgRoleTrDTO.getDesc());
            conOrgRoleTrRepo.updateById(conOrgRoleTrPO);
        } else {
            // 新增逻辑
            conOrgRoleTrPO = new ConOrgRoleTrPO();
            conOrgRoleTrPO.setOrgCode(conOrgRoleTrDTO.getOrgCode());
            conOrgRoleTrPO.setOrgName(conOrgRoleTrDTO.getOrgName());
            conOrgRoleTrPO.setDesc(conOrgRoleTrDTO.getDesc());
            conOrgRoleTrRepo.insert(conOrgRoleTrPO);
        }

        // 5. 处理角色行信息
        if (conOrgRoleTrDTO.getId() != null) {
            // 删除原有的角色行
            LambdaQueryWrapper<ConRoleItemTrPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ConRoleItemTrPO::getConOrgRoleTrId, conOrgRoleTrPO.getId());
            conRoleItemTrRepo.delete(wrapper);
        }

        // 6. 保存新的角色行信息
        if (conOrgRoleTrDTO.getRoleLine() != null && !conOrgRoleTrDTO.getRoleLine().isEmpty()) {
            for (ConRoleItemTrDTO roleItem : conOrgRoleTrDTO.getRoleLine()) {
                ConRoleItemTrPO roleItemPO = new ConRoleItemTrPO();
                Role role;
                try {
                    RoleByKeyFindParams roleByKeyFindParams = new RoleByKeyFindParams();
                    roleByKeyFindParams.setKey(roleItem.getRoleKey());
                    role = iamClient.roleClient().getRoleByKey(roleByKeyFindParams).execute();
                } catch (Exception e) {
                    throw new BusinessException("查询角色信息失败");
                }
                if (Objects.isNull(role)) {
                    throw new BusinessException("角色信息不存在");
                }
                roleItemPO.setRoleName(role.getName());
                roleItemPO.setRoleKey(role.getKey());
                roleItemPO.setConOrgRoleTrId(conOrgRoleTrPO.getId());
                conRoleItemTrRepo.insert(roleItemPO);
            }
        }
    }

    public void contractDelete(ConOrgRoleTrDTO conOrgRoleTrDTO) {
        // 1. 根据组织ID查询组织角色信息
        ConOrgRoleTrPO conOrgRoleTrPO = conOrgRoleTrRepo.selectById(conOrgRoleTrDTO.getId());
        if (conOrgRoleTrPO == null) {
            throw new BusinessException("组织角色信息不存在");
        }

        // 2. 删除角色行信息
        LambdaQueryWrapper<ConRoleItemTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConRoleItemTrPO::getConOrgRoleTrId, conOrgRoleTrPO.getId());
        conRoleItemTrRepo.delete(wrapper);

        // 3. 删除组织角色头信息
        conOrgRoleTrRepo.deleteById(conOrgRoleTrPO.getId());
    }

}
