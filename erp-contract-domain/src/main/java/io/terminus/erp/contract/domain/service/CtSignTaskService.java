package io.terminus.erp.contract.domain.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.base.Throwables;
import io.terminus.cloud.storage.core.client.CloudClient;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.RootModel;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.erp.contract.domain.mq.dto.SignTaskMsg;
import io.terminus.erp.contract.domain.mq.producer.SignTaskProducer;
import io.terminus.erp.contract.infrastructure.repo.tp.*;
import io.terminus.erp.contract.spi.convert.tp.*;
import io.terminus.erp.contract.spi.dict.tp.*;
import io.terminus.erp.contract.spi.model.tp.dto.*;
import io.terminus.erp.contract.spi.model.tp.po.*;
import io.terminus.erp.contract.spi.msg.CtSignMsg;
import io.terminus.erp.md.infrastructure.repo.base.GenAttachmentTypeCfRepo;
import io.terminus.erp.md.infrastructure.repo.ct.GenCtAttachmentLinkTrRepo;
import io.terminus.erp.md.infrastructure.repo.ct.GenCtPartnerLinkTrRepo;
import io.terminus.erp.md.spi.convert.base.GenComTypeCfConverter;
import io.terminus.erp.md.spi.convert.ct.GenCtAttachmentLinkTrConverter;
import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import io.terminus.erp.md.spi.model.dto.ct.CtHeadTrDTO;
import io.terminus.erp.md.spi.model.po.base.GenAttachmentTypeCfPO;
import io.terminus.erp.md.spi.model.po.base.GenComTypeCfPO;
import io.terminus.erp.md.spi.model.po.ct.GenCtAttachmentLinkTrPO;
import io.terminus.erp.md.spi.model.po.ct.GenCtPartnerLinkTrPO;
import io.terminus.erp.md.spi.model.po.vend.GenVendInfoMdPO;
import io.terminus.erp.md.spi.util.MD;
import io.terminus.notice.sdk.req.task.NoticeTaskCreateReq0;
import io.terminus.notice.sdk.service.NoticeService;
import io.terminus.thirdparty.common.result.ThirdPartyResponse;
import io.terminus.thirdparty.sign.api.client.SignClient;
import io.terminus.thirdparty.sign.api.dto.CreateESignResponse;
import io.terminus.thirdparty.sign.api.dto.FileUploadResponse;
import io.terminus.thirdparty.sign.api.dto.GetSignUrlResponse;
import io.terminus.thirdparty.sign.api.dto.request.CreateESignRequest;
import io.terminus.thirdparty.sign.api.dto.request.CreateESignRequest.PartyA;
import io.terminus.thirdparty.sign.api.dto.request.CreateESignRequest.PartyB;
import io.terminus.thirdparty.sign.api.dto.request.FileUploadRequest;
import io.terminus.thirdparty.sign.api.dto.request.GetSignUrlRequest;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.common.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @className: CtSignService
 * @author: charl
 * @date: 2023/7/31 16:46
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CtSignTaskService {


    public static final String NAME_混合签署 = "混合签署";
    public static final String NAME_线下签署 = "线下签署";
    public static final String NAME_契约锁线上签署 = "契约锁线上签署";

    public static final String SIGNWAY_OFFLINE_CODE = "CTP202312211118321";

    private final SignClient signClient;

    private final ConCtSigntaskTrConverter conCtSigntaskTrConverter;

    private final ConCtSigntaskTrRepo conCtSigntaskTrRepo;

    private final ConCtSignatoryTrRepo conCtSignatoryTrRepo;

    private final ConCtSignatoryTrConverter conCtSignatoryTrConverter;

    private final ConCtSignfileTrRepo conCtSignfileTrRepo;

    private final ConCtSignfileTrConverter conCtSignfileTrConverter;

    private final ConCtSignTypeConfRepo conCtSignTypeConfRepo;

    private final ConCtSignTypeConfConverter conCtSignTypeConfConverter;

    private final ConCtEsignatureTrRepo conCtEsignatureTrRepo;

    private final ConCtSignrecordTrRepo conCtSignrecordTrRepo;

    private final ConCtEsignuserTrRepo conCtEsignuserTrRepo;

    private final SignTaskProducer signTaskProducer;

    private final CloudClient cloudClient;

    private final GenComTypeCfConverter genComTypeCfConverter;

    private final ConCtSignchannalCfRepo conCtSignchannalCfRepo;

    private final UserService userService;

    private final CtErpService ctErpService;

    private final GenAttachmentTypeCfRepo genAttachmentTypeCfRepo;

    private final GenCtAttachmentLinkTrRepo genCtAttachmentLinkTrRepo;

    private final IdGenerator idGenerator;

    private final GenCtPartnerLinkTrExtConverter ctPartnerLinkTrExtConverter;

    private final GenCtPartnerLinkTrRepo genCtPartnerLinkTrRepo;

    private final CtBaseService baseService;

    private final NoticeService noticeService;

    private final GenCtAttachmentLinkTrConverter genCtAttachmentLinkTrConverter;


    public ConCtSigntaskTrDTO save(ConCtSigntaskTrDTO ctSigntaskTrDTO) {
        if (ctSigntaskTrDTO.getId() == null) {
            return create(ctSigntaskTrDTO, ctSigntaskTrDTO.getCode());
        }
        update(ctSigntaskTrDTO);
        return ctSigntaskTrDTO;
    }

    public ConCtSigntaskTrDTO queryCtSignTaskById(Long id) {
        return conCtSigntaskTrConverter.po2Dto(conCtSigntaskTrRepo.selectById(id));
    }

    public ConCtSigntaskTrDTO create(ConCtSigntaskTrDTO task, String signCode) {
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrConverter.dto2Po(task);
        conCtSigntaskTrPO.setId(null);
        conCtSigntaskTrPO.setCode(signCode);
        conCtSigntaskTrPO.setCreatedAt(null);
        conCtSigntaskTrPO.setUpdatedAt(null);
        conCtSigntaskTrRepo.insert(conCtSigntaskTrPO);
        task.setId(conCtSigntaskTrPO.getId());
        return task;
    }

    public void update(ConCtSigntaskTrDTO task) {
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrConverter.dto2Po(task);
        conCtSigntaskTrRepo.updateById(conCtSigntaskTrPO);
    }

    /**
     * 批量保存签署人:处理签署人状态和顺序逻辑
     * 1. 电子的顺序为1
     * 2. 线下类型的先甲方，后乙方
     * 3. sortNum最小签署方设置为待签署
     *
     * @param signTask
     * @return
     */
    public ConCtSigntaskTrDTO batchSaveSignatories(ConCtSigntaskTrDTO signTask) {
        if (Objects.isNull(signTask) || CollectionUtils.isEmpty(signTask.getSignatories())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_IS_NULL);
        }
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrConverter.dto2PoList(signTask.getSignatories());
        // 设置关联任务id
        conCtSignatoryTrPOS.forEach(v -> v.setSignTask(signTask.getId()));
        conCtSignatoryTrRepo.insertBatch(conCtSignatoryTrPOS);
        return upSignatorieSignTypeAndSortNum(signTask.getId());
    }

    /**
     * 设置签署任务的签署方式、签署方的签署方式、签署顺序等
     *
     * @param id
     * @return
     */
    private ConCtSigntaskTrDTO upSignatorieSignTypeAndSortNum(long id) {
        final ConCtSigntaskTrDTO signTask = getSignTaskDetail(id);
        // 甲方签署信息
        final List<ConCtSignatoryTrDTO> signatories = signTask.getSignatories();

        final List<ConCtSignatoryTrDTO> hasEsignPartyAList = signatories.stream()
                .filter(signatory -> signatory.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYA) && Objects.nonNull(signatory.geteSign()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasEsignPartyAList)) {
            return signTask;
        }
        // 乙方签署信息
        final List<ConCtSignatoryTrDTO> hasEsignPartyBList = signatories.stream()
                .filter(signatory -> signatory.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYB) && Objects.nonNull(signatory.geteSign()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasEsignPartyBList)) {
            return signTask;
        }

        final Map<String, ConCtSignTypeConfPO> conCtSignTypeConfPOMap = getAllSignTypes();

        Map<String, List<ConCtSignatoryTrDTO>> signatoryBySignatoryTypeMap = signatories.stream().collect(Collectors.groupingBy(ConCtSignatoryTrDTO::getSignatoryType));

        // true 线上；false 线下
        final boolean aIsOnline = !org.springframework.util.CollectionUtils.isEmpty(hasEsignPartyAList);
        final boolean bIsOnline = !org.springframework.util.CollectionUtils.isEmpty(hasEsignPartyBList);

        ConCtSignatoryTrDTO partyA = signatoryBySignatoryTypeMap.get(ConCtSignatoryTrSignatoryTypeDict.PARTYA).stream().findFirst().orElse(null);
        ConCtSignatoryTrDTO partyB = signatoryBySignatoryTypeMap.get(ConCtSignatoryTrSignatoryTypeDict.PARTYB).stream().findFirst().orElse(null);

        if (!aIsOnline && !bIsOnline) {
            // 无需线上签署
            updateSignTaskSignType(signTask, conCtSignTypeConfPOMap, NAME_线下签署);
        }

        // 两个都是线上
        if (aIsOnline && bIsOnline) {
            updateSignTaskSignType(signTask, conCtSignTypeConfPOMap, NAME_契约锁线上签署);
        }
        // 一个线上、一个线下签署
        if (!Objects.equals(aIsOnline, bIsOnline)) {
            updateSignTaskSignType(signTask, conCtSignTypeConfPOMap, NAME_混合签署);
        }

        // 更新签署方的签署方式
        updateConCtSignatorySignType(partyA, aIsOnline);
        updateConCtSignatorySignType(partyB, bIsOnline);

        // 更新签署方的签署顺序
        updateSignatorySortNum(partyA, partyB, aIsOnline, bIsOnline);

        // 第一签署人状态修改为待签署
        List<ConCtSignatoryTrDTO> aAndB = Lists.list(partyA, partyB);
        TreeMap<Integer, List<ConCtSignatoryTrDTO>> sortedMap = aAndB.stream()
                .collect(Collectors.groupingBy(ConCtSignatoryTrDTO::getSortNum, TreeMap::new, Collectors.toList()));
        // sortNum第一的签署人状态，设置为待签署
        sortedMap.firstEntry()
                .getValue()
                .forEach(v -> updateSignatureStatus(v.getId(), ConCtSignatoryTrSignatoryStatusDict.WAITING));
        // 其余签署顺序任务为不展示状态
        sortedMap.higherEntry(sortedMap.firstKey())
                .getValue()
                .forEach(v -> updateSignatureStatus(v.getId(), ConCtSignatoryTrSignatoryStatusDict.NOT_DISPLAY));

        return signTask;
    }


    private Map<String, ConCtSignTypeConfPO> getAllSignTypes() {
        List<ConCtSignTypeConfPO> conCtSignTypeConfPOS = conCtSignTypeConfRepo.selectList(null);
        return conCtSignTypeConfPOS.stream().collect(Collectors.toMap(ConCtSignTypeConfPO::getName, Function.identity()));
    }

    /**
     * 批量保存签署文件
     *
     * @param ctSigntaskTrDTO
     * @return
     */
    public ConCtSigntaskTrDTO batchSaveSignFile(ConCtSigntaskTrDTO ctSigntaskTrDTO) {
        if (Objects.isNull(ctSigntaskTrDTO) || CollectionUtils.isEmpty(ctSigntaskTrDTO.getSignFiles())) {
            throw new BusinessException("签署文件为空");
        }
        List<ConCtSignfileTrDTO> signFiles = ctSigntaskTrDTO.getSignFiles();
        signFiles.forEach(v -> v.setSignTask(ctSigntaskTrDTO.getId()));
        List<ConCtSignfileTrPO> conCtSignfileTrPOS = conCtSignfileTrConverter.dto2PoList(signFiles);
        conCtSignfileTrRepo.insertBatch(conCtSignfileTrPOS);
        return ctSigntaskTrDTO;
    }

    public ConCtSignatoryTrDTO rejectSignatory(ConCtSignatoryTrDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_PARAM_IS_NULL);
        }
        ConCtSignatoryTrPO conCtSignatoryTrPO = conCtSignatoryTrRepo.selectById(request.getId());
        if (Objects.isNull(conCtSignatoryTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_IS_NULL);
        }
        if (!conCtSignatoryTrPO.getSignatoryStatus().equals(ConCtSignatoryTrSignatoryStatusDict.WAITING)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_STATUS_IS_NOT_WAITING);
        }
        conCtSignatoryTrPO.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.REJECTED);
        // 更新签署方签署状态为拒签
        int i = conCtSignatoryTrRepo.updateById(conCtSignatoryTrPO);
        if (i <= 0) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_UPDATE_FAIL);
        }
        // 后续的签署方直接关闭
        LambdaQueryWrapper<ConCtSignatoryTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSignatoryTrPO::getSignTask, conCtSignatoryTrPO.getSignTask());
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrRepo.selectList(queryWrapper);
        List<ConCtSignatoryTrPO> collect = conCtSignatoryTrPOS.stream().filter(v -> !v.getId().equals(request.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            collect.forEach(v -> {
                v.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.CLOSED);
            });
            conCtSignatoryTrRepo.updateBatch(collect);
        }
        request.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.REJECTED);
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(conCtSignatoryTrPO.getSignTask());
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_IS_NULL);
        }
        // 更新关联签署任务为拒签
        ConCtSigntaskTrPO signtaskTrPO = new ConCtSigntaskTrPO();
        signtaskTrPO.setId(conCtSignatoryTrPO.getSignTask());
        signtaskTrPO.setSignStatus(ConCtSigntaskTrSignStatusDict.REJECTED);
        LambdaUpdateWrapper<ConCtSigntaskTrPO> signtaskUpdateWrapper = new LambdaUpdateWrapper();
        signtaskUpdateWrapper.eq(ConCtSigntaskTrPO::getId, conCtSignatoryTrPO.getSignTask());
        signtaskUpdateWrapper.set(ConCtSigntaskTrPO::getSignStatus, ConCtSigntaskTrSignStatusDict.REJECTED);
        conCtSigntaskTrRepo.update(signtaskTrPO, signtaskUpdateWrapper);

        // 推送签署拒绝变更消息
        SignTaskMsg signTaskMsg = SignTaskMsg.builder()
                .signBillType(conCtSigntaskTrPO.getSignBillType())
                .signBillCode(conCtSigntaskTrPO.getSignBillCode())
                .signTaskId(conCtSigntaskTrPO.getId())
                .signStatus(ConCtSignatoryTrSignatoryStatusDict.REJECTED).build();
        signTaskProducer.sendMsg(signTaskMsg);
        return request;
    }

    public ConCtSigntaskTrDTO closeSignTask(ConCtSigntaskTrDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_PARAM_IS_NULL);
        }
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(request.getId());
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_IS_NULL);
        }
        conCtSigntaskTrPO.setSignStatus(ConCtSigntaskTrSignStatusDict.CLOSED);
        conCtSigntaskTrRepo.updateById(conCtSigntaskTrPO);

        LambdaQueryWrapper<ConCtSignatoryTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSignatoryTrPO::getSignTask, conCtSigntaskTrPO.getId());
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrRepo.selectList(queryWrapper);
        if (Objects.isNull(conCtSignatoryTrPOS)) {
            return request;
        }
        conCtSignatoryTrPOS.forEach(v -> {
            v.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.CLOSED);
        });
        conCtSignatoryTrRepo.updateBatch(conCtSignatoryTrPOS);
        // 推送状态变更消息
        SignTaskMsg signTaskMsg = SignTaskMsg.builder()
                .signBillType(request.getSignBillType())
                .signTaskId(request.getId())
                .signBillCode(request.getSignBillCode())
                .signBillId(request.getSignBillId())
                .signStatus(ConCtSignatoryTrSignatoryStatusDict.CLOSED)
                .build();
        signTaskProducer.sendMsg(signTaskMsg);
        return request;
    }

    public CtHeadTrDTO closeSignTaskByContract(CtHeadTrDTO request) {
        LambdaQueryWrapper<ConCtSigntaskTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSigntaskTrPO::getSignBillId, request.getId());
        queryWrapper.eq(ConCtSigntaskTrPO::getSignBillType, ConCtSigntaskTrSignBillTypeDict.CONTRACT);
        // 存在一个合同对应多个签署任务的情况，所以需要按照创建时间倒序排序，取最新的签署任务
        List<ConCtSigntaskTrPO> conCtSigntaskTrPOS = conCtSigntaskTrRepo.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(conCtSigntaskTrPOS)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_IS_NULL);
        }
        Optional<ConCtSigntaskTrPO> first = conCtSigntaskTrPOS.stream().sorted(Comparator.comparing(ConCtSigntaskTrPO::getCreatedAt).reversed()).findFirst();
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = conCtSigntaskTrConverter.po2Dto(first.get());
        this.closeSignTask(conCtSigntaskTrDTO);
        return request;
    }


    public ConCtSigntaskTrDTO signingSignTaskByTask(ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(request.getId());
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException("签署任务不存在");
        }
        LambdaQueryWrapper<ConCtSignatoryTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSignatoryTrPO::getSignTask, conCtSigntaskTrPO.getId());
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrRepo.selectList(queryWrapper);

        Optional<ConCtSignatoryTrPO> first = conCtSignatoryTrPOS.stream().filter(v -> v.getSignatoryStatus().equals(ConCtSignatoryTrSignatoryStatusDict.WAITING)).findFirst();
        if (!first.isPresent()) {
            throw new BusinessException("签署方不存在");
        }
        ConCtSignatoryTrDTO conCtSignatoryTrDTO = conCtSignatoryTrConverter.po2Dto(first.get());
        // 调用签署服务
        this.signingSignTask(conCtSignatoryTrDTO);
        return request;
    }

    public ConCtSignatoryTrDTO signingSignTask(ConCtSignatoryTrDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_PARAM_IS_NULL);
        }
        ConCtSignatoryTrPO conCtSignatoryTrPO = conCtSignatoryTrRepo.selectById(request.getId());
        if (Objects.isNull(conCtSignatoryTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_IS_NULL);
        }
        // 签署方状态必须是待签署/签署中
        if (!conCtSignatoryTrPO.getSignatoryStatus().equals(ConCtSignatoryTrSignatoryStatusDict.WAITING) && !conCtSignatoryTrPO.getSignatoryStatus()
                .equals(ConCtSignatoryTrSignatoryStatusDict.SIGNING)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_STATUS_IS_NOT_WAITING_OR_REJECTED);
        }
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(conCtSignatoryTrPO.getSignTask());
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_IS_NULL);
        }
        ConCtSignTypeConfPO conCtSignTypeConfPO = null;
        final Long signWay = conCtSigntaskTrPO.getSignWay();
        final Long taskId = conCtSigntaskTrPO.getId();
        if (Objects.nonNull(signWay)) {
            conCtSignTypeConfPO = conCtSignTypeConfRepo.selectById(signWay);
        }

        // 判断是否需要校验电子签名，（线上签署需要校验电子签名）
        ConCtEsignatureTrPO conCtEsignatureTrPO = null;
        if (Objects.nonNull(conCtSignTypeConfPO) && conCtSignTypeConfPO.getVerifyESign()) {
            // 校验电子签名
            if (Objects.isNull(request.geteSign())) {
                throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_ESIGN_IS_NULL);
            }
            conCtEsignatureTrPO = conCtEsignatureTrRepo.selectById(request.geteSign());
            if (Objects.isNull(conCtEsignatureTrPO)) {
                throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_ESIGN_IS_NULL);
            }
            // 电子签章必须是启用状态
            if (!conCtEsignatureTrPO.getEsignatureStatus().equals(ConCtEsignatureTrEsignatureStatusDict.SUCCESS)) {
                throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_ESIGN_IS_DISABLED);
            }
        }
        // 校验签署顺序，前方的签署方必须是已签署状态
        List<ConCtSignatoryTrDTO> conCtSignatoryTrPOS = getCtSignatories(conCtSignatoryTrPO.getSignTask());
        conCtSignatoryTrPOS.stream().filter(v -> (v.getSortNum() < conCtSignatoryTrPO.getSortNum())).forEach(v -> {
            if (!v.getSignatoryStatus().equals(ConCtSignatoryTrSignatoryStatusDict.SIGNED)) {
                throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_STATUS_IS_NOT_SIGNED);
            }
        });

        // 创建签署记录
        LambdaQueryWrapper<ConCtSignfileTrPO> fileQuery = new LambdaQueryWrapper<>();
        fileQuery.eq(ConCtSignfileTrPO::getSignTask, taskId);
        List<ConCtSignfileTrPO> signfileTrPOS = conCtSignfileTrRepo.selectList(fileQuery);
        for (ConCtSignfileTrPO signfileTrPO : signfileTrPOS) {
            ConCtSignrecordTrPO conCtSignrecordTrPO = new ConCtSignrecordTrPO();
            conCtSignrecordTrPO.setSignTask(taskId);
            conCtSignrecordTrPO.setSignatory(conCtSignatoryTrPO.getId());
            conCtSignrecordTrPO.setSignFile(signfileTrPO.getId());
            conCtSignrecordTrPO.setSignTime(LocalDateTime.now());
            conCtSignrecordTrPO.setEsignature(Objects.isNull(conCtEsignatureTrPO) ? null : conCtEsignatureTrPO.getId());
            conCtSignrecordTrPO.setSingUser(TrantorContext.getCurrentUserId());
            conCtSignrecordTrRepo.insert(conCtSignrecordTrPO);
        }

        //List<String> signedFileList = signfileTrPOS.stream().map(ConCtSignfileTrPO::getSignedFile).filter(Objects::nonNull).collect(Collectors.toList());
        //if (Objects.isNull(conCtEsignatureTrPO) && CollUtil.isNotEmpty(signedFileList)) {
        //    // 保存签署文件到合同已签署文件
        //    saveSignedFile(conCtSigntaskTrPO, signedFileList);
        //}

        // 更新签署方状态
        updateSignatoryToSigned(conCtSignatoryTrPO.getId());

        // 查询同级签署中的（多甲方自动签署的时候会存在这种情况）
        List<ConCtSignatoryTrDTO> sameSorts = conCtSignatoryTrPOS.stream()
                .filter(v -> v.getSortNum().equals(conCtSignatoryTrPO.getSortNum()) && !v.getId().equals(conCtSignatoryTrPO.getId())).collect(Collectors.toList());

        // 同级存在待签署的签署方，不做任何处理
        Optional<ConCtSignatoryTrDTO> sameSig = sameSorts.stream().filter(v -> v.getSignatoryStatus().equals(ConCtSignatoryTrSignatoryStatusDict.WAITING)).findAny();
        if (sameSig.isPresent()) {
            return request;
        }

        // 判断是否是最后一个签署人
        if (isLastSignatory(conCtSignatoryTrPOS, conCtSignatoryTrPO.getSortNum())) {
            // 是最后一个签署人，更新签署任务状态为已完成
            updateSignTaskToSigned(taskId);

            // 签署完成，推送外部消息
            ctErpService.updateContractStatus(conCtSigntaskTrPO.getSignBillId(), CtHeadStatusDict.SIGN_CONFORM); // 一切ok
            publishSignTaskSignedMessage(conCtSigntaskTrPO);
        } else {
            // 不是最后一个签署人，更新下一个签署任务状态为签署中
            Optional<ConCtSignatoryTrDTO> nextOption = conCtSignatoryTrPOS.stream().filter(v -> (v.getSortNum() > conCtSignatoryTrPO.getSortNum())).min(Comparator.comparing(ConCtSignatoryTrDTO::getSortNum));
            if (nextOption.isPresent()) {
                ConCtSignatoryTrDTO nextSign = nextOption.get();
                updateSignatoryToWaiting(nextSign.getId());
            }
        }

        return request;
    }

    private void saveSignedFile(ConCtSigntaskTrPO conCtSigntaskTrPO, List<String> signedFileList) {
        GenAttachmentTypeCfPO genAttachmentTypeCfPO = getTheSignedFileAttachmentType();
        GenCtAttachmentLinkTrPO genCtAttachmentLinkTrPO = new GenCtAttachmentLinkTrPO();
        genCtAttachmentLinkTrPO.setDocRef(conCtSigntaskTrPO.getSignBillId());
        genCtAttachmentLinkTrPO.setAttachmentType(genAttachmentTypeCfPO.getId());
        genCtAttachmentLinkTrPO.setAttachmentUrl(JSON.toJSONString(signedFileList));
        // 获取原始的文件信息
        LambdaQueryWrapper<GenCtAttachmentLinkTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GenCtAttachmentLinkTrPO::getDocRef, conCtSigntaskTrPO.getSignBillId());
        List<GenCtAttachmentLinkTrPO> genCtAttachmentLinkTrPOS = genCtAttachmentLinkTrRepo.selectList(wrapper);
        for (GenCtAttachmentLinkTrPO ctAttachmentLinkTrPO : genCtAttachmentLinkTrPOS) {
            Long attachmentType = ctAttachmentLinkTrPO.getAttachmentType();
            if (Objects.equals(attachmentType, genCtAttachmentLinkTrPO.getAttachmentType())) {
                genCtAttachmentLinkTrPO.setId(ctAttachmentLinkTrPO.getId());
                genCtAttachmentLinkTrPO.setCreatedBy(ctAttachmentLinkTrPO.getCreatedBy());
                genCtAttachmentLinkTrPO.setCreatedAt(ctAttachmentLinkTrPO.getCreatedAt());
            }
        }

        if (Objects.isNull(genCtAttachmentLinkTrPO.getId())) {
            genCtAttachmentLinkTrRepo.insert(genCtAttachmentLinkTrPO);
        } else {
            genCtAttachmentLinkTrRepo.updateById(genCtAttachmentLinkTrPO);
        }
    }

    private GenAttachmentTypeCfPO getTheSignedFileAttachmentType() {
        List<GenAttachmentTypeCfPO> genAttachmentTypeCfPOS = genAttachmentTypeCfRepo.selectList(new LambdaQueryWrapper<>());
        Optional<GenAttachmentTypeCfPO> signedType = genAttachmentTypeCfPOS.stream().filter(v -> v.getAttachmentName().equals("已签署文件")).findFirst();
        return signedType.orElse(null);
    }

    /**
     * fixme: 不仅仅要判断是否有sortNum大的，还要判断当前sortNum的都已经签署
     *
     * @param conCtSignatoryTrPOS
     * @param sortNum
     * @return
     */
    public boolean isLastSignatory(List<ConCtSignatoryTrDTO> conCtSignatoryTrPOS, Integer sortNum) {
        return conCtSignatoryTrPOS.stream().noneMatch(v -> v.getSortNum() > sortNum);
    }

    public void updateSignatoryToSigned(ConCtSignatoryTrDTO conCtSignatoryTrDTO) {
        final Long taskId = conCtSignatoryTrDTO.getSignTask();
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(taskId);
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_IS_NULL);
        }
        // 校验签署顺序，前方的签署方必须是已签署状态
        List<ConCtSignatoryTrDTO> conCtSignatoryTrPOS = getCtSignatories(taskId);
        conCtSignatoryTrPOS.stream().filter(v -> (v.getSortNum() < conCtSignatoryTrDTO.getSortNum())).forEach(v -> {
            if (!v.getSignatoryStatus().equals(ConCtSignatoryTrSignatoryStatusDict.SIGNED)) {
                throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_STATUS_IS_NOT_SIGNED);
            }
        });

        // 创建签署记录
        LambdaQueryWrapper<ConCtSignfileTrPO> fileQuery = new LambdaQueryWrapper<>();
        fileQuery.eq(ConCtSignfileTrPO::getSignTask, taskId);
        List<ConCtSignfileTrPO> signfileTrPOS = conCtSignfileTrRepo.selectList(fileQuery);
        for (ConCtSignfileTrPO signfileTrPO : signfileTrPOS) {
            ConCtSignrecordTrPO conCtSignrecordTrPO = new ConCtSignrecordTrPO();
            conCtSignrecordTrPO.setSignTask(taskId);
            conCtSignrecordTrPO.setSignatory(conCtSignatoryTrDTO.getId());
            conCtSignrecordTrPO.setSignFile(signfileTrPO.getId());
            conCtSignrecordTrPO.setSignTime(LocalDateTime.now());
            conCtSignrecordTrPO.setEsignature(Objects.isNull(conCtSignatoryTrDTO.geteSign()) ? null : conCtSignatoryTrDTO.geteSign());
            conCtSignrecordTrPO.setSingUser(TrantorContext.getCurrentUserId());
            conCtSignrecordTrRepo.insert(conCtSignrecordTrPO);
        }

        // 更新签署方状态
        updateSignatoryToSigned(conCtSignatoryTrDTO.getId());

        // 判断是否是最后一个签署方
        if (isLastSignatory(conCtSignatoryTrPOS, conCtSignatoryTrDTO.getSortNum())) {
            // 是最后一个签署方，更新签署任务状态为已完成
            conCtSigntaskTrPO.setSignStatus(ConCtSigntaskTrSignStatusDict.SIGNED);
            conCtSigntaskTrRepo.updateById(conCtSigntaskTrPO);

            // 签署完成，推送外部消息
            ctErpService.updateContractStatus(conCtSigntaskTrPO.getSignBillId(), CtHeadStatusDict.SIGN_CONFORM);
            publishSignTaskSignedMessage(conCtSigntaskTrPO);
        } else {
            // 不是最后一个签署人，更新下一个签署任务状态为签署中
            Optional<ConCtSignatoryTrDTO> nextOption = conCtSignatoryTrPOS.stream().filter(v -> (v.getSortNum() > conCtSignatoryTrDTO.getSortNum()))
                    .min(Comparator.comparing(ConCtSignatoryTrDTO::getSortNum));
            if (nextOption.isPresent()) {
                ConCtSignatoryTrDTO nextSign = nextOption.get();
                updateSignatoryToWaiting(nextSign.getId());
            }
        }
    }

    /**
     * 契约锁签署成功回掉，需改签署方状态，并且当是最后一个签署方的时候，同步修改签署任务和签署合同的状态
     *
     * @param signTaskTrDTO
     */
    public void processSignatoryWhenESignSuccessAck(ConCtSigntaskTrDTO signTaskTrDTO) {
        // 1. 查询签署任务
        final Long taskId = signTaskTrDTO.getId();
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(taskId);
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException("签署任务为不存在");
        }
        // 校验签署顺序，前方的签署方必须是已签署状态
        List<ConCtSignatoryTrDTO> conCtSignatoryTrDTOS = getCtSignatories(taskId);
        log.info("updateSignatoryBySignTask conCtSignatoryTrDTOS {}", conCtSignatoryTrDTOS.toString());
        // 将待签署 并且是 线上签署的状态更新
        List<ConCtSignatoryTrDTO> waitingSignList = conCtSignatoryTrDTOS.stream()
                .filter(conCtSignatoryTrDTO -> conCtSignatoryTrDTO.getSignatoryStatus().equals(ConCtSignatoryTrSignatoryStatusDict.WAITING))
                .filter(conCtSignatoryTrDTO -> Objects.equals(conCtSignatoryTrDTO.getSignType(), ConCtSignatoryTrPO.SIGN_TYPE_ONLINE))
                .collect(Collectors.toList());

        for (ConCtSignatoryTrDTO conCtSignatoryTrPO : waitingSignList) {
            // 创建签署记录
            LambdaQueryWrapper<ConCtSignfileTrPO> fileQuery = new LambdaQueryWrapper<>();
            fileQuery.eq(ConCtSignfileTrPO::getSignTask, taskId);
            List<ConCtSignfileTrPO> signfileTrPOS = conCtSignfileTrRepo.selectList(fileQuery);
            for (ConCtSignfileTrPO signfileTrPO : signfileTrPOS) {
                ConCtSignrecordTrPO conCtSignrecordTrPO = new ConCtSignrecordTrPO();
                conCtSignrecordTrPO.setSignTask(taskId);
                conCtSignrecordTrPO.setSignatory(conCtSignatoryTrPO.getId());
                conCtSignrecordTrPO.setSignFile(signfileTrPO.getId());
                conCtSignrecordTrPO.setSignTime(LocalDateTime.now());
                conCtSignrecordTrPO.setEsignature(Objects.isNull(conCtSignatoryTrPO.geteSign()) ? null : conCtSignatoryTrPO.geteSign());
                conCtSignrecordTrPO.setSingUser(TrantorContext.getCurrentUserId());
                conCtSignrecordTrRepo.insert(conCtSignrecordTrPO);
            }

            // 更新签署方状态为已签署
            updateSignatoryToSigned(conCtSignatoryTrPO.getId());

            // 判断是否是最后一个签署方
            if (isLastSignatory(conCtSignatoryTrDTOS, conCtSignatoryTrPO.getSortNum())) {
                // 是最后一个签署方，更新签署任务状态为已完成
                updateSignTaskToSigned(taskId);

                // 签署完成 1. 修改合同状态
                ctErpService.updateContractStatus(conCtSigntaskTrPO.getSignBillId(), CtHeadStatusDict.SIGN_CONFORM);
                publishSignTaskSignedMessage(conCtSigntaskTrPO);
            } else {
                // 不是最后一个签署人，更新下一个签署任务状态为签署中
                Optional<ConCtSignatoryTrDTO> nextOption = conCtSignatoryTrDTOS.stream().filter(v -> (v.getSortNum() > conCtSignatoryTrPO.getSortNum()))
                        .min(Comparator.comparing(ConCtSignatoryTrDTO::getSortNum));
                if (nextOption.isPresent()) {
                    ConCtSignatoryTrDTO nextSign = nextOption.get();
                    updateSignatoryToWaiting(nextSign.getId());
                }
            }
        }
    }

    /**
     * 发布签名任务完成的消息
     * <p>
     * 当签名任务完成后，本方法将构建一条签名任务完成的消息，并通过签名任务生产者发送该消息
     *
     * @param conCtSigntaskTrPO 签名任务对象，包含签名任务的相关信息
     */
    public void publishSignTaskSignedMessage(ConCtSigntaskTrPO conCtSigntaskTrPO) {
        // 构建签名任务完成的消息
        SignTaskMsg signTaskMsg = SignTaskMsg.builder()
                .signBillType(conCtSigntaskTrPO.getSignBillType()) // 设置签名单据类型
                .signTaskId(conCtSigntaskTrPO.getId()) // 设置签名任务ID
                .signBillCode(conCtSigntaskTrPO.getSignBillCode()) // 设置签名单据编码
                .signBillId(conCtSigntaskTrPO.getSignBillId()) // 设置签名单据ID
                .signStatus(ConCtSigntaskTrSignStatusDict.SIGNED) // 设置签名状态为已完成
                .build();
        // 发送签名任务消息
        signTaskProducer.sendMsg(signTaskMsg);
    }


    /**
     * 将指定的签约任务标记为已签定状态
     *
     * @param id 签约任务的ID
     */
    public void updateSignTaskToSigned(Long id) {
        // 创建一个更新操作的包装器，并设置筛选条件
        LambdaUpdateWrapper<ConCtSigntaskTrPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ConCtSigntaskTrPO::getId, id); // 根据ID筛选需要更新的记录

        // 设置需要更新的字段值
        updateWrapper.set(ConCtSigntaskTrPO::getSignStatus, ConCtSigntaskTrSignStatusDict.SIGNED); // 设置签约状态为已签定
        updateWrapper.set(ConCtSigntaskTrPO::getFinishTime, LocalDateTime.now()); // 设置完成时间为当前时间
        updateWrapper.set(ConCtSigntaskTrPO::getUpdatedAt, LocalDateTime.now()); // 设置更新时间为当前时间

        // 执行更新操作
        conCtSigntaskTrRepo.update(null, updateWrapper);
    }

    /**
     * 将指定的签约任务标记为已关闭状态
     *
     * @param id 签约任务的ID
     */
    public void updateSignTaskToClosed(Long id) {
        // 创建一个更新操作的包装器，并设置筛选条件
        LambdaUpdateWrapper<ConCtSigntaskTrPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ConCtSigntaskTrPO::getId, id); // 根据ID筛选需要更新的记录

        // 设置需要更新的字段值
        updateWrapper.set(ConCtSigntaskTrPO::getSignStatus, ConCtSigntaskTrSignStatusDict.CLOSED); // 设置签约状态为已签定
        updateWrapper.set(ConCtSigntaskTrPO::getUpdatedAt, LocalDateTime.now()); // 设置更新时间为当前时间
        // 执行更新操作
        conCtSigntaskTrRepo.update(null, updateWrapper);
    }

    /**
     * 将指定的签约代表状态更新为“已签字”
     * 此方法通过签署代表的ID更新数据库中的记录，设置其签署状态为已签字，
     * 并记录签署时间和更新时间
     *
     * @param id 签约代表的ID
     */
    public void updateSignatoryToSigned(Long id) {
        LambdaUpdateWrapper<ConCtSignatoryTrPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ConCtSignatoryTrPO::getId, id);
        updateWrapper.set(ConCtSignatoryTrPO::getSignatoryStatus, ConCtSignatoryTrSignatoryStatusDict.SIGNED);
        updateWrapper.set(ConCtSignatoryTrPO::getSignTime, LocalDateTime.now());
        updateWrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
        conCtSignatoryTrRepo.update(null, updateWrapper);
    }


    /**
     * 更新签署人状态为已拒绝
     *
     * @param id 签署人记录的ID
     */
    public void updateSignatoryRejected(Long id) {
        LambdaUpdateWrapper<ConCtSignatoryTrPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ConCtSignatoryTrPO::getId, id);
        updateWrapper.set(ConCtSignatoryTrPO::getSignatoryStatus, ConCtSignatoryTrSignatoryStatusDict.REJECTED);
        updateWrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
        conCtSignatoryTrRepo.update(null, updateWrapper);
    }


    public void updateSignatoryToWaiting(Long id) {
        ConCtSignatoryTrPO nextUpdateSignatoriesBO = new ConCtSignatoryTrPO();
        nextUpdateSignatoriesBO.setId(id);
        //3.1更新状态为待签署
        nextUpdateSignatoriesBO.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.WAITING);
        nextUpdateSignatoriesBO.setUpdatedAt(LocalDateTime.now());
        conCtSignatoryTrRepo.updateById(nextUpdateSignatoriesBO);
    }

    public void publishSignTaskClosed(ConCtSigntaskTrPO conCtSigntaskTrPO) {
        // 签署任务关闭，通知外部系统
        SignTaskMsg signTaskMsg = SignTaskMsg.builder()
                .signBillType(conCtSigntaskTrPO.getSignBillType())
                .signBillCode(conCtSigntaskTrPO.getSignBillCode())
                .signBillId(conCtSigntaskTrPO.getSignBillId())
                .signTaskId(conCtSigntaskTrPO.getId())
                .signStatus(ConCtSigntaskTrSignStatusDict.CLOSED).build();
        signTaskProducer.sendMsg(signTaskMsg);
    }

    public ConCtSignatoryTrDTO eSigningSign(ConCtSignatoryTrDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId()) || Objects.isNull(request.geteSign())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_PARAM_IS_NULL);
        }
        ConCtSignatoryTrPO conCtSignatoryTrPO = conCtSignatoryTrRepo.selectById(request.getId());
        if (Objects.isNull(conCtSignatoryTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_IS_NULL);
        }
        // 签署链接不为空，说明已经创建了签署任务，直接返回即可
        if (StringUtils.isNotEmpty(conCtSignatoryTrPO.getSignUrl())) {
            return request;
        }
        // 查询签署任务
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(conCtSignatoryTrPO.getSignTask());
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_IS_NULL);
        }
        // 查询电子签名
        ConCtEsignatureTrPO conCtEsignatureTrPO = conCtEsignatureTrRepo.selectById(request.geteSign());
        if (Objects.isNull(conCtEsignatureTrPO) || StringUtils.isEmpty(conCtEsignatureTrPO.getPsnId()) || StringUtils.isEmpty(conCtEsignatureTrPO.getOrgId())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_ESIGN_IS_NULL);
        }
        // 查询电子签名渠道
        LambdaQueryWrapper<ConCtSignchannalCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSignchannalCfPO::getId, conCtEsignatureTrPO.getCertificateAuthority());
        ConCtSignchannalCfPO conCtSignchannalCfPO = conCtSignchannalCfRepo.selectOne(queryWrapper);
        if (Objects.isNull(conCtSignchannalCfPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_ESIGNATURE_IS_NULL);
        }
        String routeKey = conCtSignchannalCfPO.getRouteKey();

        // 查询授权用户
        LambdaQueryWrapper<ConCtEsignuserTrPO> eSignUserQuery = new LambdaQueryWrapper<>();
        eSignUserQuery.eq(ConCtEsignuserTrPO::getESign, conCtEsignatureTrPO.getId());
        List<ConCtEsignuserTrPO> conCtEsignuserTrPOS = conCtEsignuserTrRepo.selectList(eSignUserQuery);

        // 查询签署文件
        LambdaQueryWrapper<ConCtSignfileTrPO> fileQuery = new LambdaQueryWrapper<>();
        fileQuery.eq(ConCtSignfileTrPO::getSignTask, conCtSignatoryTrPO.getSignTask());
        List<ConCtSignfileTrPO> signfileTrPOS = conCtSignfileTrRepo.selectList(fileQuery);
        List<String> docs = new ArrayList<>();
        for (ConCtSignfileTrPO signFilePO : signfileTrPOS) {
            String signFileUrl = signFilePO.getSignFile();
            InputStream inputStream = cloudClient.downloadFile(signFileUrl, signFilePO.getFileName());
            File tempFile = null;
            try {
                tempFile = File.createTempFile(signFilePO.getFileName(), ".pdf");
                FileUtils.copyInputStreamToFile(inputStream, tempFile);
                FileUploadRequest fileUploadRequest = new FileUploadRequest();
                fileUploadRequest.setFileName(signFilePO.getFileName());
                fileUploadRequest.setFile(tempFile);
                fileUploadRequest.setChannel(routeKey);
                // 上传文件
                ThirdPartyResponse<FileUploadResponse> fileUploadResponse = signClient.fileUpload(fileUploadRequest);
                if (!fileUploadResponse.isSuccess()) {
                    throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_FILE_UPLOAD_FAIL);
                }
                String fileId = fileUploadResponse.getData().getFileId();
                signFilePO.setThirdFileId(fileId);
                conCtSignfileTrRepo.updateById(signFilePO);
                docs.add(fileId);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } finally {
                // 删除临时文件
                if (Objects.nonNull(tempFile)) {
                    tempFile.delete();
                }
            }
        }

        // 查询签署方
        LambdaQueryWrapper<ConCtSignatoryTrPO> signatoryQuery = new LambdaQueryWrapper<>();
        signatoryQuery.eq(ConCtSignatoryTrPO::getSignTask, conCtSignatoryTrPO.getSignTask());
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrRepo.selectList(signatoryQuery);
        List<ConCtSignatoryTrPO> partners = conCtSignatoryTrPOS.stream().filter(v -> !v.getId().equals(conCtSignatoryTrPO.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(partners) || partners.size() != 1) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_IS_NULL);
        }
        ConCtSignatoryTrPO partner = partners.get(0);
        GenComTypeCfPO genComTypeCfPO = MD.queryById(partner.getSignatory(), GenComTypeCfPO.class);

        PartyA partyA = new PartyA();
        partyA.setCreatorOrgId(conCtEsignatureTrPO.getOrgId());
        partyA.setCreatorPsnId(conCtEsignatureTrPO.getPsnId());
        partyA.setCreatorOrgName(conCtEsignatureTrPO.getCompanyName());
        partyA.setSignOrder((long) conCtSignatoryTrPO.getSortNum());
        partyA.setAgentName(conCtEsignatureTrPO.getAgentName());
        partyA.setAgentMobile(conCtEsignatureTrPO.getAgentContactDetails());

        PartyB partyB = new PartyB();
        partyB.setOrgName(genComTypeCfPO.getName());
        partyB.setSignOrder(partner.getSortNum());
        Long agent = partner.getAgent();
        User user = userService.findById(agent);
        //代办人用户信息
        partyB.setAgentName(user.getUsername());
        partyB.setAgentMobile(user.getMobile());

        CreateESignRequest createESignRequest = new CreateESignRequest();
        createESignRequest.setSubject(conCtSigntaskTrPO.getTitle());
        //todo 契约锁契约锁用印流程id
        createESignRequest.setThirdPartyId("");
        //todo 回调地址
        createESignRequest.setCallBackUrl("");
        createESignRequest.setFileIds(docs);
        createESignRequest.setCustomBizNum(conCtSigntaskTrPO.getId() + "");
        createESignRequest.setPartyA(partyA);
        createESignRequest.setPartyB(partyB);
        createESignRequest.setChannel(routeKey);

        ThirdPartyResponse<CreateESignResponse> signResponse = signClient.createSignTask(createESignRequest);
        if (!signResponse.isSuccess() || Objects.isNull(signResponse.getData())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_CREATE_FAIL);
        }
        String flowId = signResponse.getData().getFlowId();
        conCtSigntaskTrPO.setSignFlowId(flowId);
        conCtSigntaskTrRepo.updateById(conCtSigntaskTrPO);

        // 获取乙方签署地址
        GetSignUrlRequest partyBSignUrl = new GetSignUrlRequest();
        partyBSignUrl.setSignFlowId(flowId);
        partyBSignUrl.setOrgName(genComTypeCfPO.getName());
        partyBSignUrl.setSignOrder((long) partner.getSortNum());
        partyBSignUrl.setAgentMobile(user.getMobile());
        partyBSignUrl.setCallBackUrl("");
        partyBSignUrl.setChannel(routeKey);
        ThirdPartyResponse<GetSignUrlResponse> partyBSignUrlResponse = signClient.getSignUrl(partyBSignUrl);
        if (!partyBSignUrlResponse.isSuccess() || Objects.isNull(partyBSignUrlResponse.getData())) {
            throw new RuntimeException("获取乙方签署链接失败");
        }
        String signUrl = partyBSignUrlResponse.getData().getSignUrl();
        //更新乙方签署链接
        partner.setSignUrl(signUrl);
        partner.setUpdatedAt(LocalDateTime.now());
        conCtSignatoryTrRepo.updateById(partner);

        //获取甲方签署页面
        GetSignUrlRequest partyASignUrl = new GetSignUrlRequest();
        partyASignUrl.setSignFlowId(flowId);
        partyASignUrl.setOrgName(conCtEsignatureTrPO.getCompanyName());
        partyASignUrl.setSignOrder((long) conCtSignatoryTrPO.getSortNum());
        partyASignUrl.setOrgId(conCtEsignatureTrPO.getOrgId());
        partyASignUrl.setPsnId(conCtEsignatureTrPO.getPsnId());
        partyASignUrl.setAgentMobile(conCtEsignatureTrPO.getAgentContactDetails());
        partyASignUrl.setCallBackUrl("");
        partyASignUrl.setChannel(routeKey);
        ThirdPartyResponse<GetSignUrlResponse> partyASignUrlResponse = signClient.getSignUrl(partyASignUrl);
        if (!partyASignUrlResponse.isSuccess() || Objects.isNull(partyASignUrlResponse.getData())) {
            throw new RuntimeException("获取甲方签署链接失败");
        }
        //更新甲方签署链接
        String partyAUrl = partyASignUrlResponse.getData().getSignUrl();
        conCtSignatoryTrPO.setSignUrl(partyAUrl);
        conCtSignatoryTrPO.setUpdatedAt(LocalDateTime.now());
        conCtSignatoryTrRepo.updateById(conCtSignatoryTrPO);

        request.setSignUrl(partyAUrl);
        return request;
    }

    public ConCtSigntaskTrDTO checkSignTaskInProgress(ConCtSigntaskTrDTO request) {
        LambdaQueryWrapper<ConCtSigntaskTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSigntaskTrPO::getSignBillCode, request.getSignBillCode());
        queryWrapper.in(ConCtSigntaskTrPO::getSignStatus, Arrays.asList(ConCtSigntaskTrSignStatusDict.SIGNING, ConCtSigntaskTrSignStatusDict.SIGNED));
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectOne(queryWrapper);
        if (Objects.nonNull(conCtSigntaskTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_IS_EXIST);
        }
        request.setSignStatus(ConCtSigntaskTrSignStatusDict.SIGNING);
        request.setSponsor(TrantorContext.getCurrentUser().getId());
        return request;
    }

    public ConCtSigntaskTrDTO querySignTaskInfo(ConCtSigntaskTrDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_PARAM_IS_NULL);
        }
        // 查询签署任务信息
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = getCtSignTaskByFlowId(request);
        // 查询签署方式
        ConCtSignTypeConfDTO conCtSignTypeConfDTO = getCtSignType(conCtSigntaskTrDTO);
        // 查询签署方
        List<ConCtSignatoryTrDTO> ctSignatories = getCtSignatories(request.getId());
        // 查询签署文件
        List<ConCtSignfileTrDTO> ctSignFiles = getCtSignFiles(request);

        AtomicReference<String> signatoryStatus = new AtomicReference<>(conCtSigntaskTrDTO.getSignStatus());
        ctSignatories.forEach(v -> {
            GenComTypeCfPO genComTypeCfPO = MD.queryById(v.getSignatory().getId(), GenComTypeCfPO.class);
            GenComTypeCfDTO genComTypeCfDTO = genComTypeCfConverter.po2Dto(genComTypeCfPO);
            if (Objects.nonNull(v.getAgent()) && Objects.nonNull(v.getAgent().getId())) {
                User user = null;
                try {
                    user = userService.findById(v.getAgent().getId());
                } catch (Exception e) {
                    log.error("查询用户信息失败: {}", v, e);
                }
                v.setAgent(user);
            }
            v.setSignatory(genComTypeCfDTO);
            if (v.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYA)) {

                if (!ConCtSignatoryTrSignatoryStatusDict.NOT_DISPLAY.equals(v.getSignatoryStatus()) && !ConCtSignatoryTrSignatoryStatusDict.WAITING.equals(v.getSignatoryStatus())) {
                    signatoryStatus.set(v.getSignatoryStatus());
                }
            }
        });

        conCtSigntaskTrDTO.setSignStatus(signatoryStatus.get());
        conCtSigntaskTrDTO.setSignWay(Objects.isNull(conCtSignTypeConfDTO) ? null : conCtSignTypeConfDTO.getId());
        conCtSigntaskTrDTO.setSignFiles(ctSignFiles);
        conCtSigntaskTrDTO.setSignatories(ctSignatories);
        return conCtSigntaskTrDTO;
    }

    private ConCtSignTypeConfDTO getCtSignType(ConCtSigntaskTrDTO conCtSigntaskTrDTO) {
        ConCtSignTypeConfPO conCtSignTypeConfPO = null;
        if (Objects.nonNull(conCtSigntaskTrDTO.getSignWay())) {
            conCtSignTypeConfPO = conCtSignTypeConfRepo.selectById(Long.valueOf(conCtSigntaskTrDTO.getSignWay()));
        }
        ConCtSignTypeConfDTO conCtSignTypeConfDTO = conCtSignTypeConfConverter.po2Dto(conCtSignTypeConfPO);
        return conCtSignTypeConfDTO;
    }

    private ConCtSigntaskTrDTO getCtSignTaskByFlowId(ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(request.getId());
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_IS_NULL);
        }
        return conCtSigntaskTrConverter.po2Dto(conCtSigntaskTrPO);
    }

    private List<ConCtSignfileTrDTO> getCtSignFiles(ConCtSigntaskTrDTO request) {
        LambdaQueryWrapper<ConCtSignfileTrPO> signFileQuery = new LambdaQueryWrapper<>();
        signFileQuery.eq(ConCtSignfileTrPO::getSignTask, request.getId());
        List<ConCtSignfileTrPO> conCtSignfileTrPOS = conCtSignfileTrRepo.selectList(signFileQuery);
        if (CollectionUtils.isEmpty(conCtSignfileTrPOS)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNFILE_IS_NULL);
        }
        return conCtSignfileTrConverter.po2DtoList(conCtSignfileTrPOS);
    }

    private List<ConCtSignatoryTrDTO> getCtSignatories(Long taskId) {
        LambdaQueryWrapper<ConCtSignatoryTrPO> signatoryQuery = new LambdaQueryWrapper<>();
        signatoryQuery.eq(ConCtSignatoryTrPO::getSignTask, taskId);
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrRepo.selectList(signatoryQuery);
        if (CollectionUtils.isEmpty(conCtSignatoryTrPOS)) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_SIGNATORY_IS_NULL);
        }
        return conCtSignatoryTrConverter.po2DtoList(conCtSignatoryTrPOS);

    }

    /**
     * 供应商端查看签署任务详情的时候，实时根据是否有电子签章账户来透出是线上签署还是线下签署
     *
     * @param request
     * @return
     */
    public ConCtSigntaskTrDTO querySupplierSignTaskInfo(ConCtSigntaskTrDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            throw new BusinessException(CtSignMsg.CON_CT_SIGN_TASK_PARAM_IS_NULL);
        }
        // 查询签署任务信息
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = getCtSignTaskByFlowId(request);
        // 查询签署方
        LambdaQueryWrapper<ConCtSignatoryTrPO> signatoryQuery = new LambdaQueryWrapper<>();
        signatoryQuery.eq(ConCtSignatoryTrPO::getSignTask, request.getId());
        signatoryQuery.eq(ConCtSignatoryTrPO::getSignatoryType, ConCtSignatoryTrSignatoryTypeDict.PARTYB);
        signatoryQuery.last("limit 0,1");
        ConCtSignatoryTrPO conCtSignatoryTrPO = conCtSignatoryTrRepo.selectOne(signatoryQuery);
        if (Objects.isNull(conCtSignatoryTrPO)) {
            throw new BusinessException("签署方乙方不存在");
        }
        ConCtSignatoryTrDTO conCtSignatoryTrDTO = conCtSignatoryTrConverter.po2Dto(conCtSignatoryTrPO);
        // 查询签署文件
        LambdaQueryWrapper<ConCtSignfileTrPO> signFileQuery = new LambdaQueryWrapper<>();
        signFileQuery.eq(ConCtSignfileTrPO::getSignTask, request.getId());
        List<ConCtSignfileTrPO> conCtSignfileTrPOS = conCtSignfileTrRepo.selectList(signFileQuery);
        if (CollectionUtils.isEmpty(conCtSignfileTrPOS)) {
            throw new BusinessException("签署文件为空");
        }
        List<ConCtSignfileTrDTO> conCtSignfileTrDTOS = conCtSignfileTrConverter.po2DtoList(conCtSignfileTrPOS);
        // 查询签署方信息
        GenComTypeCfPO genComTypeCfPO = MD.queryById(conCtSignatoryTrDTO.getSignatory().getId(), GenComTypeCfPO.class);
        GenComTypeCfDTO genComTypeCfDTO = genComTypeCfConverter.po2Dto(genComTypeCfPO);
        if (Objects.nonNull(conCtSignatoryTrDTO.getAgent()) && Objects.nonNull(conCtSignatoryTrDTO.getAgent().getId())) {
            User user = null;
            try {
                user = userService.findById(conCtSignatoryTrDTO.getAgent().getId());
            } catch (Exception e) {
                log.error("查询用户信息失败", e);
            }
            conCtSignatoryTrDTO.setAgent(user);
        }
        if (conCtSignatoryTrDTO.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYB)) {
            if (!Objects.equals(conCtSignatoryTrDTO.getSignatoryStatus(), "NOT_DISPLAY") && !Objects.equals(conCtSignatoryTrDTO.getSignatoryStatus(), "WAITING")) {
                conCtSigntaskTrDTO.setSignStatus(conCtSignatoryTrDTO.getSignatoryStatus());
            }
        }
        conCtSignatoryTrDTO.setSignatory(genComTypeCfDTO);
        // 设置签署方式 这个地方不要用之前的签约方式 就看供应商的有没有电子签章
        List<ConCtSignTypeConfPO> conCtSignTypeConfPOS = conCtSignTypeConfRepo.selectList(null);
        Map<String, ConCtSignTypeConfPO> ctSignTypeConfPOMap = conCtSignTypeConfPOS.stream().collect(Collectors.toMap(ConCtSignTypeConfPO::getName, Function.identity()));
        if (Objects.isNull(conCtSignatoryTrDTO.geteSign())) {
            ConCtSignTypeConfPO conCtSignTypeConfPO = ctSignTypeConfPOMap.get("线下签署");
            if (Objects.isNull(conCtSignTypeConfPO)) {
                throw new BusinessException("签署方式查询失败");
            }
            conCtSigntaskTrDTO.setSignWay(conCtSignTypeConfPO.getId());
        } else {
            ConCtSignTypeConfPO conCtSignTypeConfPO = ctSignTypeConfPOMap.get("契约锁线上签署");
            if (Objects.isNull(conCtSignTypeConfPO)) {
                throw new BusinessException("签署方式查询失败");
            }
            conCtSigntaskTrDTO.setSignWay(conCtSignTypeConfPO.getId());
        }
        conCtSigntaskTrDTO.setSignFiles(conCtSignfileTrDTOS);
        conCtSigntaskTrDTO.setSignatories(Lists.newArrayList(conCtSignatoryTrDTO));
        return conCtSigntaskTrDTO;
    }

    public ConCtSigntaskTrDTO getSignTaskDetail(Long taskId) {
        // 查询签署任务信息
        ConCtSigntaskTrPO conCtSigntaskTrPO = conCtSigntaskTrRepo.selectById(taskId);
        if (Objects.isNull(conCtSigntaskTrPO)) {
            throw new BusinessException("签署任务为空");
        }
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = conCtSigntaskTrConverter.po2Dto(conCtSigntaskTrPO);
        // 查询签署任务签署方信息
        LambdaQueryWrapper<ConCtSignatoryTrPO> signatoryQuery = new LambdaQueryWrapper<>();
        signatoryQuery.eq(ConCtSignatoryTrPO::getSignTask, taskId);
        List<ConCtSignatoryTrPO> conCtSignatoryTrPOS = conCtSignatoryTrRepo.selectList(signatoryQuery);
        if (CollectionUtils.isEmpty(conCtSignatoryTrPOS)) {
            throw new BusinessException("签署方为空");
        }

        List<ConCtSignatoryTrDTO> conCtSignatoryTrDTOS = new ArrayList<>();
        for (ConCtSignatoryTrPO conCtSignatoryTrPO : conCtSignatoryTrPOS) {
            ConCtSignatoryTrDTO conCtSignatoryTrDTO = conCtSignatoryTrConverter.po2Dto(conCtSignatoryTrPO);
            GenComTypeCfPO genComTypeCfPO = MD.queryById(conCtSignatoryTrPO.getSignatory(), GenComTypeCfPO.class);
            GenComTypeCfDTO genComTypeCfDTO = genComTypeCfConverter.po2Dto(genComTypeCfPO);
            conCtSignatoryTrDTO.setSignatory(genComTypeCfDTO);
            conCtSignatoryTrDTO.seteSign(conCtSignatoryTrPO.geteSign());
            conCtSignatoryTrDTOS.add(conCtSignatoryTrDTO);
        }
        conCtSigntaskTrDTO.setSignatories(conCtSignatoryTrDTOS);
        // 查询签署文件
        LambdaQueryWrapper<ConCtSignfileTrPO> signFileQuery = new LambdaQueryWrapper<>();
        signFileQuery.eq(ConCtSignfileTrPO::getSignTask, taskId);
        List<ConCtSignfileTrPO> conCtSignfileTrPOS = conCtSignfileTrRepo.selectList(signFileQuery);

        List<ConCtSignfileTrDTO> conCtSignfileTrDTOS = conCtSignfileTrConverter.po2DtoList(conCtSignfileTrPOS);
        conCtSigntaskTrDTO.setSignFiles(conCtSignfileTrDTOS);
        return conCtSigntaskTrDTO;
    }


    public boolean isAllSigned(List<ConCtSignatoryTrPO> conCtSignatoryTrPOS) {
        return conCtSignatoryTrPOS.stream().allMatch(v -> ConCtSignatoryTrSignatoryStatusDict.SIGNED.equals(v.getSignatoryStatus()));
    }


    public boolean isCurrentSortNumAllSigned(int sortNum, List<ConCtSignatoryTrPO> conCtSignatoryTrPOS) {

        // todo
        return false;
    }


    public boolean isNextSortNumAllSigned(int sortNum, List<ConCtSignatoryTrPO> conCtSignatoryTrPOS) {
        // todo
        return isAllSigned(getNextSortNumSignatories(sortNum, conCtSignatoryTrPOS));
    }

    public List<ConCtSignatoryTrPO> getNextSortNumSignatories(int sortNum, List<ConCtSignatoryTrPO> conCtSignatoryTrPOS) {
        // todo

        return Lists.emptyList();
    }

    /**
     * 过滤需要盖章的合同文件，并创建签署任务中的签署文件
     *
     * @param taskId
     * @param signTask
     * @param contractId
     */
    public void createSignFile(long taskId, ConCtSigntaskTrDTO signTask, long contractId) {
        // 查询所有的合同文件
        LambdaQueryWrapper<GenCtAttachmentLinkTrPO> attachQueryWrapper = new LambdaQueryWrapper<>();
        attachQueryWrapper.eq(GenCtAttachmentLinkTrPO::getDocRef, contractId);
        List<GenCtAttachmentLinkTrPO> genCtAttachmentLinkTrPOS = genCtAttachmentLinkTrRepo.selectList(attachQueryWrapper);
        if (CollectionUtils.isEmpty(genCtAttachmentLinkTrPOS)) {
            throw new BusinessException("签署文件为空");
        }
        // 准备生成签署文件(是否需要签署 = 是)
        List<ConCtSignfileTrDTO> signFiles = new ArrayList<>();
        // 需要签署的合同文件

        for (GenCtAttachmentLinkTrPO ctFile : genCtAttachmentLinkTrPOS) {
            GenAttachmentTypeCfPO genAttachmentTypeCfPO = genAttachmentTypeCfRepo.selectById(ctFile.getAttachmentType());
            if (!Boolean.TRUE.equals(genAttachmentTypeCfPO.getExtWqStamped())) {
                // 无需盖章跳过
                continue;
            }
            final String stampPositioningMethod = genAttachmentTypeCfPO.getExtWqStampPositioningMethod();

            String attachmentUrlStr = ctFile.getAttachmentUrl(); // 一切ok
            List<String> attachmentUrlList = JSON.parseArray(attachmentUrlStr, String.class);
            // 合同文件拆分生成签约文件
            for (String attachmentUrl : attachmentUrlList) {
                ConCtSignfileTrDTO file = buildConSignFile(taskId, attachmentUrl, stampPositioningMethod);
                signFiles.add(file);
            }
        }
        // 拼装签署文件信息
        signTask.setSignFiles(signFiles);
        batchSaveSignFile(signTask);
    }

    private static ConCtSignfileTrDTO buildConSignFile(long taskId, String attachmentUrl, String stampPositioningMethod) {
        ConCtSignfileTrDTO file = new ConCtSignfileTrDTO();
        // 这个地方获取的名字是 xxx.pdf 这个地方需要删除一下啊
        String fileName = attachmentUrl
                .substring(attachmentUrl.lastIndexOf("/") + 1)
                .replace(".pdf", "")
                .replace(".PDF", "");

        file.setFileName(fileName);
        file.setSignFile(attachmentUrl);
        file.setSignTask(taskId);
        // 指定盖章定位方式
        file.setStampPositioningMethod(stampPositioningMethod);
        return file;
    }

    private void createSignatories(GenCtHeadTrExtDTO headTrExtDTO, long taskId, ConCtSigntaskTrDTO signTask) {

        ConCtSignatoryTrDTO partyASignatory = buildPartyASignatory(headTrExtDTO, taskId);

        ConCtSignatoryTrDTO partyBSignatory = buildPartyBSignatory(headTrExtDTO, taskId);

        signTask.setSignatories(Lists.list(partyASignatory, partyBSignatory));

        batchSaveSignatories(signTask);
    }

    private ConCtSignatoryTrDTO buildPartyBSignatory(GenCtHeadTrExtDTO headTrExtDTO, long taskId) {
        Long prtnB = headTrExtDTO.getPrtnB();
        GenVendInfoMdPO genVendInfoMdPO = MD.queryById(prtnB, GenVendInfoMdPO.class);
        if (Objects.isNull(genVendInfoMdPO)) {
            throw new BusinessException("供应商不存在");
        }
        // 乙方签署方信息
        ConCtSignatoryTrDTO signatoryTrDTO = new ConCtSignatoryTrDTO();
        signatoryTrDTO.setSignTask(taskId);
        GenComTypeCfDTO genComTypeCfDTO = new GenComTypeCfDTO();
        genComTypeCfDTO.setId(genVendInfoMdPO.getPurComId());
        signatoryTrDTO.setSignatory(genComTypeCfDTO);
        signatoryTrDTO.setSortNum(2);
        ConCtEsignatureTrPO validCtEsignature = getValidCtEsignature(genVendInfoMdPO);
        if (validCtEsignature != null) {
            signatoryTrDTO.seteSign(validCtEsignature.getId());
        }
        signatoryTrDTO.setSignatoryType(ConCtSignatoryTrSignatoryTypeDict.PARTYB);
        return signatoryTrDTO;
    }

    /**
     * 认证成功状态的电子签章账户才是有效的账户
     *
     * @param genVendInfoMdPO
     * @return
     */
    private ConCtEsignatureTrPO getValidCtEsignature(GenVendInfoMdPO genVendInfoMdPO) {
        ConCtEsignatureTrPO esignatureTrPO = conCtEsignatureTrRepo.selectOne(new QueryWrapper<ConCtEsignatureTrPO>().eq("entity", genVendInfoMdPO.getPurComId()));
        if (Objects.nonNull(esignatureTrPO) && "SUCCESS".equals(esignatureTrPO.getEsignatureStatus())) {
            return esignatureTrPO;
        }
        return null;
    }

    /**
     * @param request
     * @param headTrExtDTO
     * @param contractId
     * @param isOffline:   是否是线下签署类型
     * @return
     */
    public ConCtSigntaskTrDTO createSignTask(CtHeadTrDTO request, GenCtHeadTrExtDTO headTrExtDTO, long contractId, boolean isOffline) {
        ConCtSigntaskTrDTO signTask = new ConCtSigntaskTrDTO();
        String taskCode = idGenerator.nextCode(ConCtSigntaskTrPO.class, signTask);
        signTask.setCode(taskCode);
        signTask.setTitle(request.getCtCode() + "的签署任务");
        signTask.setCompany(headTrExtDTO.getPrtnA());
        signTask.setSignBillId(contractId);
        signTask.setSignBillCode(request.getCtCode());
        signTask.setSponsor(TrantorContext.getCurrentUser().getId());
        signTask.setSignStatus(ConCtSigntaskTrSignStatusDict.SIGNING);
        signTask.setSignBillType(ConCtSigntaskTrSignBillTypeDict.CONTRACT);
        signTask.setOrgRef(headTrExtDTO.getPurOrgCodeApplicable());
        signTask.setOrgRuleId(headTrExtDTO.getRuleAuthId());

        if (isOffline) {
            ConCtSignTypeConfPO conCtSignTypeConfPO = conCtSignTypeConfRepo.selectByCode(SIGNWAY_OFFLINE_CODE);
            if (Objects.nonNull(conCtSignTypeConfPO)) {
                signTask.setSignWay(conCtSignTypeConfPO.getId());
            }
        }

        return create(signTask, taskCode);
    }

    public void checkSignTaskExisted(long billId) {
        // 校验是否存在
        LambdaQueryWrapper<ConCtSigntaskTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSigntaskTrPO::getSignBillId, billId);
        queryWrapper.in(ConCtSigntaskTrPO::getSignStatus, Arrays.asList(ConCtSigntaskTrSignStatusDict.SIGNING));
        List<ConCtSigntaskTrPO> conCtSigntaskTrPOS = conCtSigntaskTrRepo.selectList(queryWrapper);
        if (!org.springframework.util.CollectionUtils.isEmpty(conCtSigntaskTrPOS)) {
            throw new BusinessException("签署任务已经存在");
        }
    }

    /**
     * 1. 无论是多甲方还是单甲方，都只有一个甲方签署方
     * 2. 如果是多甲方，选主甲方作为签署方；其中，
     * - 如果所有甲方都有电子账户，则签署方是线上。
     * - 如果有一个甲方没有电子账户，则走线下签署，仅创建主甲方的签署方。业务上让这个主甲方来代替其他甲方线下签署。
     * <p>
     * 要ConCtSignatoryTrDTO是否存在eSign字段来表达是否线上
     *
     * @param contract
     * @param taskId
     * @return
     */
    private ConCtSignatoryTrDTO buildPartyASignatory(GenCtHeadTrExtDTO contract, long taskId) {

        GenComTypeCfPO partyACompany = MD.queryById(contract.getPrtnA(), GenComTypeCfPO.class);

        ConCtSignatoryTrDTO theMainPartyASignatory = buildPartyASignatory(contract, taskId, partyACompany);
        ConCtEsignatureTrPO theMainPartyAeSignature = conCtEsignatureTrRepo.selectOne(new QueryWrapper<ConCtEsignatureTrPO>().eq("entity", partyACompany.getId()));

        if (theMainPartyAeSignature == null) {
            // 没有电子签名，走线下签署
            return theMainPartyASignatory;
        }

        theMainPartyASignatory.seteSign(theMainPartyAeSignature.getId());

        long contractId = contract.getId();
        LambdaQueryWrapper<GenCtPartnerLinkTrPO> partyQuery = new LambdaQueryWrapper<>();
        partyQuery.eq(GenCtPartnerLinkTrPO::getDocRef, contractId);
        List<GenCtPartnerLinkTrPO> partnerList = genCtPartnerLinkTrRepo.selectList(partyQuery);

        // 没有关联甲方
        if (CollectionUtils.isEmpty(partnerList)) {
            return theMainPartyASignatory;
        }

        Set<Long> relPartys = partnerList.stream().map(RootModel::getId).collect(Collectors.toSet());
        List<GenCtPartnerLinkTrPO> genCtPartnerLinkTrPOS = MD.queryByIds(relPartys, GenCtPartnerLinkTrPO.class);
        List<GenCtPartnerLinkTrExtDTO> genCtPartnerLinkTrExtDTOS = ctPartnerLinkTrExtConverter.convertToExtDtoList(genCtPartnerLinkTrPOS);
        Set<Long> companyIdList = genCtPartnerLinkTrExtDTOS.stream().map(GenCtPartnerLinkTrExtDTO::getExtWqRelCompany).collect(Collectors.toSet());
        List<GenComTypeCfPO> companyList = MD.queryByIds(companyIdList, GenComTypeCfPO.class);
        if (org.springframework.util.CollectionUtils.isEmpty(companyList)) {
            throw new BusinessException("甲方公司不存在");
        }

        boolean existPartyANotHasESign = companyList.stream()
                .anyMatch(partACompany -> conCtEsignatureTrRepo.selectOne(new QueryWrapper<ConCtEsignatureTrPO>().eq("entity", partACompany.getId())) == null);
        if (existPartyANotHasESign) {
            theMainPartyASignatory.seteSign(null);
            return theMainPartyASignatory;
        }
        return theMainPartyASignatory;
    }

    private static ConCtSignatoryTrDTO buildPartyASignatory(GenCtHeadTrExtDTO contract, long taskId, GenComTypeCfPO partACompany) {
        ConCtSignatoryTrDTO signatoryTrDTO = new ConCtSignatoryTrDTO();
        signatoryTrDTO.setSignTask(taskId);
        GenComTypeCfDTO genComTypeCfDTO = new GenComTypeCfDTO();
        genComTypeCfDTO.setId(partACompany.getId());
        signatoryTrDTO.setSignatory(genComTypeCfDTO);
        signatoryTrDTO.setSortNum(1);
        if (Objects.nonNull(contract.getExtWqCtDrafter())) {
            User user = new User();
            user.setId(contract.getExtWqCtDrafter());
            signatoryTrDTO.setAgent(user);
        }
        signatoryTrDTO.setSignatoryType(ConCtSignatoryTrSignatoryTypeDict.PARTYA);
        signatoryTrDTO.setSignatoryStatus(ConCtSignatoryTrSignatoryStatusDict.SIGNING);
        return signatoryTrDTO;
    }

    /**
     * 编制合同审批通过，创建签署任务
     *
     * @param contractId
     * @return
     */
    public ConCtSigntaskTrDTO createSignTaskByContract(long contractId) {

        // 0.判断是否已创建签署任务
        checkSignTaskExisted(contractId);
        // 1.查询并检查合同信息
        GenCtHeadTrExtDTO contract = baseService.selectContract(contractId);
        // 2.创建签署任务
        ConCtSigntaskTrDTO signTask = createSignTask(contract, contract, contractId, false);
        final long taskId = signTask.getId();
        // 3.甲方签署信息&乙方签署信息
        createSignatories(contract, taskId, signTask);
        // 4.签署文件
        createSignFile(taskId, signTask, contractId);
        log.info("签署任务创建完成");
        return signTask;
    }

    /**
     * esign : 是否已自动签署电子签
     *
     * @param signatories
     * @param name
     * @param esign
     */
    public void noticeSign(List<ConCtSignatoryTrDTO> signatories, String name, boolean esign) {
        try {
            List<User> users;
            if (esign) {
                users = signatories.stream().filter(v -> v.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYA) && Objects.isNull(v.geteSign()))
                        .map(ConCtSignatoryTrDTO::getAgent).collect(Collectors.toList());
            } else {
                users = signatories.stream().filter(v -> v.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYA))
                        .map(ConCtSignatoryTrDTO::getAgent).collect(Collectors.toList());
            }
            // fixme： 乙方的agent字段都为空，这里实际发不出去
            Optional<User> any = signatories.stream().filter(v -> v.getSignatoryType().equals(ConCtSignatoryTrSignatoryTypeDict.PARTYB))
                    .map(ConCtSignatoryTrDTO::getAgent).findAny();
            any.ifPresent(users::add);
            NoticeTaskCreateReq0 req = new NoticeTaskCreateReq0();
            req.setTaskName("签署任务已创建,待签署");
            req.setNoticeTargetIds(users.stream().map(User::getId).collect(Collectors.toList()));
            req.setNoticeBusinessCode("sign_task_waiting_signed");
            HashMap<String, Object> params = new HashMap<>();
            params.put("合同名称", name);
            req.setParams(params);
            noticeService.createTaskByScene(req);
        } catch (Throwable e) {
            log.error("通知签署失败 合同 {} cause {}", name, Throwables.getStackTraceAsString(e));
        }
    }

    void updateSignTaskSignType(ConCtSigntaskTrDTO request, Map<String, ConCtSignTypeConfPO> conCtSignTypeConfPOMap, String signTypeName) {
        ConCtSigntaskTrPO conCtSigntaskTrPO = new ConCtSigntaskTrPO();
        conCtSigntaskTrPO.setId(request.getId());
        ConCtSignTypeConfPO conCtSignTypeConfPO = conCtSignTypeConfPOMap.get(signTypeName);
        if (Objects.isNull(conCtSignTypeConfPO)) {
            throw new BusinessException("签署方式查询异常");
        }
        conCtSigntaskTrPO.setSignWay(conCtSignTypeConfPO.getId());
        conCtSigntaskTrRepo.updateById(conCtSigntaskTrPO);
    }

    void updateSignatureStatus(long id, String status) {
        ConCtSignatoryTrPO conCtSignatoryTrPO = new ConCtSignatoryTrPO();
        conCtSignatoryTrPO.setId(id);
        conCtSignatoryTrPO.setSignatoryStatus(status);
        conCtSignatoryTrRepo.updateById(conCtSignatoryTrPO);
    }

    /**
     * 更新签署方的签署类型 线上签署还是线下签署
     *
     * @param signatory 签署方
     * @param flag      是否为线上签署
     */
    public void updateConCtSignatorySignType(ConCtSignatoryTrDTO signatory, boolean flag) {
        if (signatory == null) {
            return;
        }
        String signType = flag ? ConCtSignatoryTrPO.SIGN_TYPE_ONLINE : ConCtSignatoryTrPO.SIGN_TYPE_OFFLINE;
        LambdaUpdateWrapper<ConCtSignatoryTrPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ConCtSignatoryTrPO::getId, signatory.getId());
        wrapper.set(ConCtSignatoryTrPO::getSignType, signType);
        wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
        conCtSignatoryTrRepo.update(null, wrapper);
    }

    /**
     * 更新签署顺序：
     * <p>
     * 1. 线上签署的顺序是1，也就是：
     * - 甲线上、乙线下：甲1，乙2；
     * - 甲线下、乙线上：甲2，乙1；
     * - 甲线上、乙线上：甲1，乙1；
     * 2. 线下签署的顺序是甲先，乙后：
     * - 甲线下、乙线下：甲2，乙3；
     */
    public void updateSignatorySortNum(ConCtSignatoryTrDTO partyA, ConCtSignatoryTrDTO partyB, boolean isAOnline, boolean isBOnline) {

        if (isAOnline && isBOnline) {
            partyA.setSortNum(1);
            partyB.setSortNum(2);
            LambdaUpdateWrapper<ConCtSignatoryTrPO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(ConCtSignatoryTrPO::getId, partyA.getId());
            wrapper.set(ConCtSignatoryTrPO::getSortNum, partyA.getSortNum());
            wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
            conCtSignatoryTrRepo.update(null, wrapper);
            wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(ConCtSignatoryTrPO::getId, partyB.getId());
            wrapper.set(ConCtSignatoryTrPO::getSortNum, partyB.getSortNum());
            wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
            conCtSignatoryTrRepo.update(null, wrapper);
        }

        if (!isAOnline && !isBOnline) {
            partyA.setSortNum(2);
            partyB.setSortNum(3);
            LambdaUpdateWrapper<ConCtSignatoryTrPO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(ConCtSignatoryTrPO::getId, partyA.getId());
            wrapper.set(ConCtSignatoryTrPO::getSortNum, partyA.getSortNum());
            wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
            conCtSignatoryTrRepo.update(null, wrapper);
            wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(ConCtSignatoryTrPO::getId, partyB.getId());
            wrapper.set(ConCtSignatoryTrPO::getSortNum, partyB.getSortNum());
            wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
            conCtSignatoryTrRepo.update(null, wrapper);
        }

        if (isAOnline && !isBOnline) {
            partyA.setSortNum(1);
            partyB.setSortNum(2);
            LambdaUpdateWrapper<ConCtSignatoryTrPO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(ConCtSignatoryTrPO::getId, partyA.getId());
            wrapper.set(ConCtSignatoryTrPO::getSortNum, partyA.getSortNum());
            wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
            conCtSignatoryTrRepo.update(null, wrapper);
            wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(ConCtSignatoryTrPO::getId, partyB.getId());
            wrapper.set(ConCtSignatoryTrPO::getSortNum, partyB.getSortNum());
            wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
            conCtSignatoryTrRepo.update(null, wrapper);
        }

        if (!isAOnline && isBOnline) {
            partyA.setSortNum(2);
            partyB.setSortNum(1);
            LambdaUpdateWrapper<ConCtSignatoryTrPO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(ConCtSignatoryTrPO::getId, partyA.getId());
            wrapper.set(ConCtSignatoryTrPO::getSortNum, partyA.getSortNum());
            wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
            conCtSignatoryTrRepo.update(null, wrapper);
            wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(ConCtSignatoryTrPO::getId, partyB.getId());
            wrapper.set(ConCtSignatoryTrPO::getSortNum, partyB.getSortNum());
            wrapper.set(ConCtSignatoryTrPO::getUpdatedAt, LocalDateTime.now());
            conCtSignatoryTrRepo.update(null, wrapper);

        }

    }

    public ConCtSigntaskTrPO getCtSignTaskByFlowId(String flowId) {
        LambdaQueryWrapper<ConCtSigntaskTrPO> querySignTaskWrapper = new LambdaQueryWrapper<>();
        querySignTaskWrapper.eq(ConCtSigntaskTrPO::getSignFlowId, flowId);
        return conCtSigntaskTrRepo.selectOne(querySignTaskWrapper);
    }
}
