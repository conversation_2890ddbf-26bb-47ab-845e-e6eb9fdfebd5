package io.terminus.erp.contract.domain.mq.consumer;

import cn.hutool.json.JSONUtil;
import io.terminus.common.rocketmq.annotation.MQConsumer;
import io.terminus.common.rocketmq.annotation.MQSubscribe;
import io.terminus.erp.contract.domain.mq.dto.SignTaskMsg;
import io.terminus.erp.contract.domain.mq.producer.ContractProducer;
import io.terminus.erp.contract.domain.service.CtErpService;
import io.terminus.erp.contract.spi.dict.tp.ConCtSigntaskTrSignBillTypeDict;
import io.terminus.erp.contract.spi.dict.tp.ConCtSigntaskTrSignStatusDict;
import io.terminus.erp.contract.spi.dict.tp.CtHeadStatusDict;
import io.terminus.erp.md.spi.model.dto.ct.CtHeadTrDTO;
import io.terminus.erp.md.spi.model.dto.ct.GenCtAttachmentLinkTrDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

/**
 * @className: SignTaskConsumer
 * @author: charl
 * @date: 2023/9/6 13:54
 */
@Component
@Slf4j
@AllArgsConstructor
@MQConsumer(consumerGroup = "TRANTOR2Consumer")
public class SignTaskConsumer {

    private final CtErpService ctErpService;

    // 订阅自定义配置的独立topic
    @MQSubscribe(tag = "SIGN_TASK_TAG", topic = "TRANTOR2_TOPIC")
    public void consumerSignTaskInfo(SignTaskMsg signTaskMsg) {
        log.info("consumerSignTaskInfo message start!");
        // 只消费合同的消息
        if (!signTaskMsg.getSignBillType().equals(ConCtSigntaskTrSignBillTypeDict.CONTRACT)) {
            return;
        }
        // 签署任务关闭，合同失效
        if (signTaskMsg.getSignStatus().equals(ConCtSigntaskTrSignStatusDict.CLOSED)) {
            log.info("sign task close, message is:{}", JSONUtil.toJsonStr(signTaskMsg));
            ctErpService.updateContractStatus(signTaskMsg.getSignBillId(), CtHeadStatusDict.CANCELLED);
        }
        // 签署完成，消费完成消息
        if (signTaskMsg.getSignStatus().equals(ConCtSigntaskTrSignStatusDict.SIGNED)) {
            log.info("sign task complete, message is:{}", JSONUtil.toJsonStr(signTaskMsg));
            // 保存已签署文件到合同
            ctErpService.addContractFile(signTaskMsg.getSignBillId(), signTaskMsg.getSignTaskId());
        }
        // 签署拒绝
        if (signTaskMsg.getSignStatus().equals(ConCtSigntaskTrSignStatusDict.REJECTED)) {
            // 1.更新合同状态为拒绝
            ctErpService.updateContractStatus(signTaskMsg.getSignBillId(), CtHeadStatusDict.REJECTED);
        }
    }

}
