package io.terminus.erp.contract.domain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtEsignatureTrRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtEsignuserTrRepo;
import io.terminus.erp.contract.infrastructure.repo.tp.ConCtSignchannalCfRepo;
import io.terminus.erp.contract.spi.convert.tp.ConCtEsignatureTrConverter;
import io.terminus.erp.contract.spi.dict.tp.ConCtEsignatureTrEsignatureStatusDict;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtEsignatureTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConEsignEmpDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConEsignEmpDTO.OrgEmployeeMdDTO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtEsignatureTrPO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtEsignuserTrPO;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignchannalCfPO;
import io.terminus.erp.contract.spi.msg.CtSignMsg;
import io.terminus.thirdparty.common.result.ThirdPartyResponse;
import io.terminus.thirdparty.sign.api.client.SignClient;
import io.terminus.thirdparty.sign.api.dto.AuthResponse;
import io.terminus.thirdparty.sign.api.dto.request.AuthRequest;
import io.terminus.thirdparty.sign.esign.properties.ESignProperties;
import io.terminus.trantor2.common.dto.Paging;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 电子签章账户服务
 * @className: CtEsignatureService
 * @author: charl
 * @date: 2023/7/31 16:46
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CtEsignatureService {

    private final SignClient signClient;

    private final ConCtEsignatureTrRepo conCtEsignatureTrRepo;

    private final ConCtEsignatureTrConverter conCtEsignatureTrConverter;

    private final ConCtEsignuserTrRepo conCtEsignuserTrRepo;

    private final ConCtSignchannalCfRepo conCtSignchannalCfRepo;

    private final ESignProperties eSignProperties;

    private final IdGenerator idGenerator;

    private final String ORG_AUTH_CALL_BACK = "/api/service/erp-contract/controller/ct/orgAuthAck";

    public Long save(ConCtEsignatureTrDTO ctEsignatureTrDTO) {
        if (Objects.isNull(ctEsignatureTrDTO.getId())) {
            return create(ctEsignatureTrDTO);
        }
        update(ctEsignatureTrDTO);
        return ctEsignatureTrDTO.getId();
    }

    public ConCtEsignatureTrDTO queryCtSignById(Long id) {
        return conCtEsignatureTrConverter.po2Dto(conCtEsignatureTrRepo.selectById(id));
    }

    public Long create(ConCtEsignatureTrDTO ctEsignatureTrDTO) {
        ConCtEsignatureTrPO conCtEsignatureTrPO = conCtEsignatureTrConverter.dto2Po(ctEsignatureTrDTO);
        String code = ctEsignatureTrDTO.getCode() == null ? idGenerator.nextCode(ConCtEsignatureTrPO.class, conCtEsignatureTrPO) : conCtEsignatureTrPO.getCode();
        conCtEsignatureTrPO.setCode(code);
        conCtEsignatureTrPO.setName(ctEsignatureTrDTO.getCompanyName()+"_电子章");
        conCtEsignatureTrPO.setCreatedAt(LocalDateTime.now());
        conCtEsignatureTrPO.setUpdatedAt(LocalDateTime.now());
        conCtEsignatureTrRepo.insert(conCtEsignatureTrPO);
        ctEsignatureTrDTO.setId(conCtEsignatureTrPO.getId());
        return conCtEsignatureTrPO.getId();
    }

    public void update(ConCtEsignatureTrDTO ctEsignatureTrDTO) {
        ctEsignatureTrDTO.setName(ctEsignatureTrDTO.getCompanyName()+"_电子章");
        ConCtEsignatureTrPO conCtEsignatureTrPO = conCtEsignatureTrConverter.dto2Po(ctEsignatureTrDTO);
        conCtEsignatureTrRepo.updateById(conCtEsignatureTrPO);
    }

    public ConCtEsignatureTrDTO signAuth(ConCtEsignatureTrDTO request) {
        ConCtEsignatureTrDTO conCtEsignatureTrDTO = queryCtSignById(request.getId());
        if (Objects.isNull(conCtEsignatureTrDTO) || Objects.isNull(conCtEsignatureTrDTO.getCertificateAuthority())) {
            throw new BusinessException(CtSignMsg.CON_CT_ESIGNATURE_IS_NULL);
        }
        LambdaQueryWrapper<ConCtSignchannalCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSignchannalCfPO::getId, request.getCertificateAuthority().getId());
        ConCtSignchannalCfPO conCtSignchannalCfPO = conCtSignchannalCfRepo.selectOne(queryWrapper);
        if (Objects.isNull(conCtSignchannalCfPO)) {
            throw new BusinessException(CtSignMsg.CON_CT_ESIGNATURE_IS_NULL);
        }
        AuthRequest authRequest = new AuthRequest();
        authRequest.setChannel(conCtSignchannalCfPO.getRouteKey());
        authRequest.setOrgName(request.getCompanyName());
        authRequest.setRegisterNo(request.getRegisterSocialCreditCode());
        authRequest.setLegalPerson(request.getLegalPersonMame());
        authRequest.setLicence(request.getBusinessLicenseScanAttachment());
        authRequest.setAgent(request.getAgentName());
        authRequest.setAgentMobile(request.getAgentContactDetails());
        authRequest.setAgentCardNo(request.getAgentLicenseNumber());
        String callbackUrl = eSignProperties.getCallBackUrl().concat(ORG_AUTH_CALL_BACK);
        authRequest.setNotifyUrl(callbackUrl);
        ThirdPartyResponse<AuthResponse> authResponse = signClient.orgAuth(authRequest);
        if (!authResponse.isSuccess()) {
            throw new BusinessException(authResponse.getMessage());
        }
        ConCtEsignatureTrPO conCtEsignatureTrPO = conCtEsignatureTrConverter.dto2Po(conCtEsignatureTrDTO);
        conCtEsignatureTrPO.setAuthUrl(authResponse.getData().getAuthUrl());
        conCtEsignatureTrPO.setAuthFlowId(authResponse.getData().getAuthFlowId());
        conCtEsignatureTrPO.setEsignatureStatus(ConCtEsignatureTrEsignatureStatusDict.REGISTERING);
        conCtEsignatureTrRepo.updateById(conCtEsignatureTrPO);
        conCtEsignatureTrDTO.setAuthFlowId(authResponse.getData().getAuthFlowId());
        return conCtEsignatureTrDTO;
    }

    public void grandUserAuth(ConEsignEmpDTO request) {
        if (Objects.isNull(request.getEsignature()) || CollectionUtils.isEmpty(request.getAuthorizedUsers())) {
            throw new BusinessException(CtSignMsg.CON_CT_ESIGNATURE_IS_NULL);
        }
        // 删除老的关联关系
        Long esignId = Long.valueOf(request.getEsignature());
        LambdaQueryWrapper<ConCtEsignuserTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtEsignuserTrPO::getESign, esignId);
        conCtEsignuserTrRepo.delete(queryWrapper);

        List<ConCtEsignuserTrPO> insertList = new ArrayList<>(request.getAuthorizedUsers().size());
        for (OrgEmployeeMdDTO authorizedUser : request.getAuthorizedUsers()) {
            ConCtEsignuserTrPO esignuserTrPO = new ConCtEsignuserTrPO();
            esignuserTrPO.setESign(esignId);
            esignuserTrPO.setAuthorizedUser(authorizedUser.getId());
            insertList.add(esignuserTrPO);
        }
        // 新增绑定关系
        conCtEsignuserTrRepo.insertBatch(insertList);
    }

    public Paging<Long> authedUserList(ConEsignEmpDTO request) {
        if (Objects.isNull(request) || Objects.isNull(request.getEsignature())) {
            throw new BusinessException(CtSignMsg.CON_CT_ESIGNATURE_IS_NULL);
        }
        LambdaQueryWrapper<ConCtEsignuserTrPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtEsignuserTrPO::getESign, Long.valueOf(request.getEsignature()));
        List<ConCtEsignuserTrPO> conCtEsignuserTrPOS = conCtEsignuserTrRepo.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(conCtEsignuserTrPOS)) {
            return Paging.empty();
        }
        List<Long> collect = conCtEsignuserTrPOS.stream().map(v -> v.getAuthorizedUser()).collect(Collectors.toList());
        return new Paging<>(collect);
    }

}
