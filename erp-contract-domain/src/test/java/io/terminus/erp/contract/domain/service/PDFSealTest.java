package io.terminus.erp.contract.domain.service;

import io.terminus.erp.contract.domain.util.PDFKeywordSearchUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

// iText imports
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import com.itextpdf.io.image.ImageDataFactory;

/**
 * PDF盖章功能测试类
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Slf4j
public class PDFSealTest {

    /**
     * 测试PDF关键字搜索和盖章功能
     */
    @Test
    public void testPdfSealFunction() {
        String pdfFilePath = "/Users/<USER>/Downloads/合同_HT-WL-20250716001 甲方_滨州市北海魏桥固废处置有限公司 乙方_淄博三迪经贸有限公司的签署文件.pdf";
        String sealImagePath = "/Users/<USER>/Downloads/images.jpeg";
        
        try {
            log.info("开始测试PDF盖章功能");
            log.info("PDF文件: {}", pdfFilePath);
            log.info("印章图片: {}", sealImagePath);
            
            // 读取PDF文件
            FileInputStream pdfInputStream = new FileInputStream(pdfFilePath);
            byte[] pdfBytes = readInputStreamToBytes(pdfInputStream);
            pdfInputStream.close();
            
            // 读取印章图片
            FileInputStream sealInputStream = new FileInputStream(sealImagePath);
            byte[] sealImageBytes = readInputStreamToBytes(sealInputStream);
            sealInputStream.close();
            
            // 定义搜索关键字
            List<String> keywords = Arrays.asList(
                "需方（章）",
                "需方(章)",
                "甲方（章）", 
                "甲方(章)"
            );
            
            String companyName = "滨州市北海魏桥固废处置有限公司aaa"; // 从文件名提取的公司名称
            
            // 搜索关键字位置
            InputStream searchStream = new ByteArrayInputStream(pdfBytes);
            PDFKeywordSearchUtil.KeywordPosition position = PDFKeywordSearchUtil.searchKeywordsFromLastPage(
                searchStream, keywords, companyName);
            
            if (position == null) {
                // 如果没有找到关键字，使用最后一行有文字的位置
                InputStream defaultStream = new ByteArrayInputStream(pdfBytes);
                position = PDFKeywordSearchUtil.getLastPageDefaultPosition(defaultStream);
                log.info("未找到关键字，将在最后一行文字位置盖章: {}", position);
            } else {
                log.info("找到关键字，将在指定位置盖章: {}", position);
            }
            
            if (position != null) {
                // 在指定位置添加印章
                InputStream pdfStream = new ByteArrayInputStream(pdfBytes);
                addSealAtPosition(pdfStream, sealImageBytes, position, pdfFilePath);
            } else {
                log.error("无法确定盖章位置");
            }
            
        } catch (Exception e) {
            log.error("测试PDF盖章功能时发生错误", e);
        }
    }

    /**
     * 在指定位置添加印章
     */
    private void addSealAtPosition(InputStream inputStream, byte[] sealImageBytes, 
                                 PDFKeywordSearchUtil.KeywordPosition position, String originalFilePath) {
        try {
            // 创建输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            // 处理PDF并添加印章
            PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputStream), new PdfWriter(outputStream));
            Document document = new Document(pdfDoc);
            
            // 创建印章图片
            Image sealImage = new Image(ImageDataFactory.create(sealImageBytes));
            
            // 设置印章大小（可以根据需要调整）
            float sealWidth = 80f;
            float sealHeight = 80f;
            sealImage.setWidth(sealWidth);
            sealImage.setHeight(sealHeight);
            
            // 计算印章位置 - 使用智能位置调整
            float[] adjustedPosition = calculateSealPosition(position, sealWidth, sealHeight);
            float x = adjustedPosition[0];
            float y = adjustedPosition[1];

            log.info("原始位置: ({}, {}), 调整后位置: ({}, {}), 关键字: '{}'",
                    position.getX(), position.getY(), x, y, position.getKeyword());
            
            // 设置印章位置
            sealImage.setFixedPosition(position.getPageNumber(), x, y);
            
            // 添加印章到文档
            document.add(sealImage);
            
            // 关闭文档
            document.close();
            pdfDoc.close();
            
            // 保存处理后的PDF到桌面
            byte[] processedPdfBytes = outputStream.toByteArray();
            String outputPath = System.getProperty("user.home") + "/Desktop/sealed_contract.pdf";
            FileOutputStream fileOutput = new FileOutputStream(outputPath);
            fileOutput.write(processedPdfBytes);
            fileOutput.close();
            
            log.info("成功添加印章，输出文件: {}", outputPath);
            
        } catch (Exception e) {
            log.error("添加印章时发生错误", e);
        }
    }

    /**
     * 智能计算印章位置
     * 根据关键字类型和位置进行精确调整
     */
    private float[] calculateSealPosition(PDFKeywordSearchUtil.KeywordPosition position, float sealWidth, float sealHeight) {
        float x = position.getX();
        float y = position.getY();
        String keyword = position.getKeyword();
        String matchedText = position.getMatchedText();

        log.info("开始计算印章位置 - 关键字: '{}', 匹配文本: '{}', 原始位置: ({}, {})",
                keyword, matchedText, x, y);

        // 根据关键字类型进行不同的位置调整
        if (keyword.contains("需方") && keyword.contains("章")) {
            // 需方（章）- 通常在文本右侧
            x += 10; // 向右偏移较少
            y -= sealHeight / 2; // 垂直居中对齐
            log.info("检测到需方章，调整位置");
        } else if (keyword.contains("甲方") && keyword.contains("章")) {
            // 甲方（章）- 通常在文本右侧
            x += 10; // 向右偏移较少
            y -= sealHeight / 2; // 垂直居中对齐
            log.info("检测到甲方章，调整位置");
        } else if (keyword.contains("甲方：") || keyword.contains("甲方:")) {
            // 甲方：公司名称 - 在公司名称后面
            x += matchedText.length() * 6; // 根据文本长度估算偏移
            y -= sealHeight / 2;
            log.info("检测到甲方公司名称，调整位置");
        } else if (matchedText.contains("（章）") || matchedText.contains("(章)")) {
            // 公司名称（章）- 在（章）位置
            int chapterIndex = Math.max(matchedText.indexOf("（章）"), matchedText.indexOf("(章)"));
            if (chapterIndex > 0) {
                x += chapterIndex * 6; // 估算到（章）位置
            }
            y -= sealHeight / 2;
            log.info("检测到公司名称章，调整位置");
        } else if (keyword.equals("最后文字位置")) {
            // 最后一行文字位置 - 在文字后面稍微偏移
            x += 20; // 向右偏移一点距离
            y -= sealHeight / 3; // 向上偏移，避免遮挡文字
            log.info("检测到最后文字位置，调整位置");
        } else if (keyword.equals("默认位置")) {
            // 固定默认位置
            x += 50; // 向右偏移
            y -= 40; // 向下偏移
            log.info("使用固定默认位置调整");
        } else {
            // 其他情况的默认调整
            x += 30; // 向右偏移
            y -= 20; // 向上偏移
            log.info("使用其他默认位置调整");
        }

        // 确保印章不会超出页面边界（假设A4页面宽度595，高度842）
        float originalX = x, originalY = y;
        x = Math.max(10, Math.min(x, 595 - sealWidth - 10));
        y = Math.max(10, Math.min(y, 842 - sealHeight - 10));

        if (originalX != x || originalY != y) {
            log.info("位置超出边界，从({}, {})调整到({}, {})", originalX, originalY, x, y);
        }

        return new float[]{x, y};
    }

    /**
     * 将输入流转换为字节数组
     */
    private byte[] readInputStreamToBytes(InputStream inputStream) throws Exception {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        return buffer.toByteArray();
    }

    /**
     * 主方法，可以直接运行测试
     */
    public static void main(String[] args) {
        PDFSealTest test = new PDFSealTest();
        test.testPdfSealFunction();
    }
}
