package io.terminus.erp.contract.domain.service;

import io.terminus.erp.contract.domain.util.PDFKeywordSearchUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

// iText imports
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.layout.property.TextAlignment;


/**
 * PDF位置调试工具
 * 用于精确调试印章位置
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Slf4j
public class PDFPositionDebugger {

    /**
     * 调试PDF位置 - 在找到的关键字位置画标记
     */
    public static void debugPositions() {
        String pdfFilePath = "/Users/<USER>/Downloads/dadada.pdf";
        String sealImagePath = "/Users/<USER>/Downloads/images.jpeg";
        try {
            log.info("开始调试PDF位置");
            log.info("PDF文件: {}", pdfFilePath);
            
            // 读取PDF文件
            FileInputStream pdfInputStream = new FileInputStream(pdfFilePath);
            byte[] pdfBytes = readInputStreamToBytes(pdfInputStream);
            pdfInputStream.close();
            
            // 定义搜索关键字
            List<String> keywords = Arrays.asList(
                "需方（章）",
                "需方(章)",
                "甲方（章）", 
                "甲方(章)"
            );
            
            String companyName = "滨州市北海魏桥固废处置有限公司aaa";
            
            // 创建输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            // 处理PDF
            InputStream pdfStream = new ByteArrayInputStream(pdfBytes);
            PdfDocument pdfDoc = new PdfDocument(new PdfReader(pdfStream), new PdfWriter(outputStream));
            Document document = new Document(pdfDoc);
            
            // 搜索所有关键字位置并标记
            InputStream searchStream = new ByteArrayInputStream(pdfBytes);
            List<PDFKeywordSearchUtil.KeywordPosition> allPositions = findAllKeywordPositions(searchStream, keywords, companyName);
            
            // 在每个找到的位置添加标记
            for (int i = 0; i < allPositions.size(); i++) {
                PDFKeywordSearchUtil.KeywordPosition position = allPositions.get(i);
                
                // 添加红色标记文本
                Paragraph marker = new Paragraph("●" + (i + 1) + ":" + position.getKeyword())
                    .setFontColor(ColorConstants.RED)
                    .setFontSize(8)
                    .setFixedPosition(position.getPageNumber(), position.getX(), position.getY(), 200);
                
                document.add(marker);
                
                log.info("标记 {}: 页码={}, 位置=({}, {}), 关键字='{}', 文本='{}'", 
                        i + 1, position.getPageNumber(), position.getX(), position.getY(), 
                        position.getKeyword(), position.getMatchedText());
            }
            
            // 关闭文档
            document.close();
            pdfDoc.close();
            
            // 保存调试文件
            byte[] debugPdfBytes = outputStream.toByteArray();
            String debugPath = System.getProperty("user.home") + "/Desktop/debug_positions.pdf";
            FileOutputStream fileOutput = new FileOutputStream(debugPath);
            fileOutput.write(debugPdfBytes);
            fileOutput.close();
            
            log.info("调试文件已保存: {}", debugPath);
            log.info("找到 {} 个关键字位置", allPositions.size());
            
        } catch (Exception e) {
            log.error("调试PDF位置时发生错误", e);
        }
    }

    /**
     * 测试不同偏移量的印章位置
     */
    public static void testSealPositions() {
        String pdfFilePath = "/Users/<USER>/Downloads/dadada.pdf";
        String sealImagePath = "/Users/<USER>/Downloads/images.jpeg";
        
        // 测试不同的偏移量
        int[][] offsets = {
            {0, 0},      // 原始位置
            {10, -10},   // 右上
            {20, -20},   // 更右上
            {30, -30},   // 再右上
            {-10, 10},   // 左下
            {50, -40}    // 默认偏移
        };
        
        try {
            // 读取文件
            FileInputStream pdfInputStream = new FileInputStream(pdfFilePath);
            byte[] pdfBytes = readInputStreamToBytes(pdfInputStream);
            pdfInputStream.close();
            
            FileInputStream sealInputStream = new FileInputStream(sealImagePath);
            byte[] sealImageBytes = readInputStreamToBytes(sealInputStream);
            sealInputStream.close();
            
            // 搜索关键字
            List<String> keywords = Arrays.asList("需方（章）", "甲方（章）");
            String companyName = "滨州市北海魏桥固废处置有限公司";
            
            InputStream searchStream = new ByteArrayInputStream(pdfBytes);
            PDFKeywordSearchUtil.KeywordPosition position = PDFKeywordSearchUtil.searchKeywordsFromLastPage(
                searchStream, keywords, companyName);
            
            if (position == null) {
                InputStream defaultStream = new ByteArrayInputStream(pdfBytes);
                position = PDFKeywordSearchUtil.getLastPageDefaultPosition(defaultStream);
            }
            
            if (position != null) {
                // 为每个偏移量生成一个测试文件
                for (int i = 0; i < offsets.length; i++) {
                    int offsetX = offsets[i][0];
                    int offsetY = offsets[i][1];
                    
                    InputStream pdfStream = new ByteArrayInputStream(pdfBytes);
                    createTestSealFile(pdfStream, sealImageBytes, position, offsetX, offsetY, i + 1);
                }
                
                log.info("已生成 {} 个测试文件，请查看桌面", offsets.length);
            }
            
        } catch (Exception e) {
            log.error("测试印章位置时发生错误", e);
        }
    }

    /**
     * 创建测试印章文件
     */
    private static void createTestSealFile(InputStream inputStream, byte[] sealImageBytes, 
                                         PDFKeywordSearchUtil.KeywordPosition position, 
                                         int offsetX, int offsetY, int testNumber) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputStream), new PdfWriter(outputStream));
            Document document = new Document(pdfDoc);
            
            // 创建印章图片
            Image sealImage = new Image(ImageDataFactory.create(sealImageBytes));
            sealImage.setWidth(60f);  // 稍小一点便于观察
            sealImage.setHeight(60f);
            
            // 计算位置
            float x = position.getX() + offsetX;
            float y = position.getY() + offsetY;
            
            // 设置印章位置
            sealImage.setFixedPosition(position.getPageNumber(), x, y);
            document.add(sealImage);
            
            // 添加位置标记
            Paragraph marker = new Paragraph("Test" + testNumber + ": (" + offsetX + "," + offsetY + ")")
                .setFontColor(ColorConstants.BLUE)
                .setFontSize(8)
                .setFixedPosition(position.getPageNumber(), x + 70, y, 100);
            document.add(marker);
            
            document.close();
            pdfDoc.close();
            
            // 保存文件
            byte[] pdfBytes = outputStream.toByteArray();
            String outputPath = System.getProperty("user.home") + "/Desktop/test_seal_" + testNumber + ".pdf";
            FileOutputStream fileOutput = new FileOutputStream(outputPath);
            fileOutput.write(pdfBytes);
            fileOutput.close();
            
            log.info("测试文件 {} 已保存: 偏移({}, {})", testNumber, offsetX, offsetY);
            
        } catch (Exception e) {
            log.error("创建测试文件时发生错误", e);
        }
    }

    /**
     * 查找所有关键字位置（用于调试）
     */
    private static List<PDFKeywordSearchUtil.KeywordPosition> findAllKeywordPositions(
            InputStream inputStream, List<String> keywords, String companyName) {
        // 这里需要实现查找所有位置的逻辑
        // 为了简化，先返回单个位置
        PDFKeywordSearchUtil.KeywordPosition position = PDFKeywordSearchUtil.searchKeywordsFromLastPage(
            inputStream, keywords, companyName);
        
        return position != null ? Arrays.asList(position) : Arrays.asList();
    }

    /**
     * 将输入流转换为字节数组
     */
    private static byte[] readInputStreamToBytes(InputStream inputStream) throws Exception {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        return buffer.toByteArray();
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        log.info("=== PDF位置调试工具 ===");
        
        // 1. 调试关键字位置
        log.info("1. 调试关键字位置...");
        debugPositions();
        
        // 2. 测试不同印章位置
        log.info("2. 测试不同印章位置...");
        testSealPositions();
        
        log.info("调试完成，请查看桌面上的文件");
    }
}
