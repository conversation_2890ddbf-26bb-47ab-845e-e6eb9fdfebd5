package io.terminus.erp.contract.domain.service;

import io.terminus.erp.contract.domain.util.PDFKeywordSearchUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * 测试最后文字位置查找功能
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@Slf4j
public class LastTextPositionTest {

    /**
     * 测试查找最后文字位置
     */
    public static void testLastTextPosition() {
        String pdfFilePath = "/Users/<USER>/Downloads/合同_HT-WL-20250716001 甲方_滨州市北海魏桥固废处置有限公司 乙方_淄博三迪经贸有限公司的签署文件.pdf";
        
        try {
            log.info("开始测试最后文字位置查找功能");
            log.info("PDF文件: {}", pdfFilePath);
            
            // 读取PDF文件
            FileInputStream pdfInputStream = new FileInputStream(pdfFilePath);
            byte[] pdfBytes = readInputStreamToBytes(pdfInputStream);
            pdfInputStream.close();
            
            // 1. 先尝试搜索关键字
            List<String> keywords = Arrays.asList(
                "需方（章）",
                "需方(章)",
                "甲方（章）", 
                "甲方(章)",
                "不存在的关键字" // 故意添加一个不存在的关键字来测试默认位置
            );
            
            String companyName = "滨州市北海魏桥固废处置有限公司";
            
            log.info("=== 测试关键字搜索 ===");
            InputStream searchStream = new ByteArrayInputStream(pdfBytes);
            PDFKeywordSearchUtil.KeywordPosition keywordPosition = PDFKeywordSearchUtil.searchKeywordsFromLastPage(
                searchStream, keywords, companyName);
            
            if (keywordPosition != null) {
                log.info("找到关键字: {}", keywordPosition);
            } else {
                log.info("未找到任何关键字");
            }
            
            // 2. 测试最后文字位置查找
            log.info("=== 测试最后文字位置查找 ===");
            InputStream defaultStream = new ByteArrayInputStream(pdfBytes);
            PDFKeywordSearchUtil.KeywordPosition lastTextPosition = PDFKeywordSearchUtil.getLastPageDefaultPosition(defaultStream);
            
            if (lastTextPosition != null) {
                log.info("找到最后文字位置: {}", lastTextPosition);
                log.info("位置详情: 页码={}, X={}, Y={}, 关键字='{}', 匹配文本='{}'", 
                        lastTextPosition.getPageNumber(), 
                        lastTextPosition.getX(), 
                        lastTextPosition.getY(), 
                        lastTextPosition.getKeyword(), 
                        lastTextPosition.getMatchedText());
            } else {
                log.error("未找到最后文字位置");
            }
            
            // 3. 测试只搜索不存在的关键字
            log.info("=== 测试搜索不存在的关键字 ===");
            List<String> nonExistentKeywords = Arrays.asList("不存在的关键字1", "不存在的关键字2");
            InputStream nonExistentStream = new ByteArrayInputStream(pdfBytes);
            PDFKeywordSearchUtil.KeywordPosition nonExistentResult = PDFKeywordSearchUtil.searchKeywordsFromLastPage(
                nonExistentStream, nonExistentKeywords, "不存在的公司");
            
            if (nonExistentResult == null) {
                log.info("正确：未找到不存在的关键字");
                
                // 应该使用最后文字位置
                InputStream fallbackStream = new ByteArrayInputStream(pdfBytes);
                PDFKeywordSearchUtil.KeywordPosition fallbackPosition = PDFKeywordSearchUtil.getLastPageDefaultPosition(fallbackStream);
                log.info("回退到最后文字位置: {}", fallbackPosition);
            } else {
                log.warn("意外：找到了不应该存在的关键字: {}", nonExistentResult);
            }
            
        } catch (Exception e) {
            log.error("测试最后文字位置时发生错误", e);
        }
    }

    /**
     * 测试不同类型的PDF文件
     */
    public static void testDifferentPdfTypes() {
        // 这里可以添加更多不同类型的PDF文件测试
        log.info("=== 测试不同类型的PDF文件 ===");
        
        // 可以添加更多测试文件
        String[] testFiles = {
            "/Users/<USER>/Downloads/合同_HT-WL-20250716001 甲方_滨州市北海魏桥固废处置有限公司 乙方_淄博三迪经贸有限公司的签署文件.pdf"
            // 可以添加更多测试文件路径
        };
        
        for (String filePath : testFiles) {
            try {
                log.info("测试文件: {}", filePath);
                
                FileInputStream pdfInputStream = new FileInputStream(filePath);
                byte[] pdfBytes = readInputStreamToBytes(pdfInputStream);
                pdfInputStream.close();
                
                InputStream stream = new ByteArrayInputStream(pdfBytes);
                PDFKeywordSearchUtil.KeywordPosition position = PDFKeywordSearchUtil.getLastPageDefaultPosition(stream);
                
                if (position != null) {
                    log.info("文件 {} 的最后文字位置: {}", filePath, position);
                } else {
                    log.warn("文件 {} 未找到最后文字位置", filePath);
                }
                
            } catch (Exception e) {
                log.error("测试文件 {} 时发生错误", filePath, e);
            }
        }
    }

    /**
     * 将输入流转换为字节数组
     */
    private static byte[] readInputStreamToBytes(InputStream inputStream) throws Exception {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        return buffer.toByteArray();
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        log.info("=== 最后文字位置测试工具 ===");
        
        // 1. 测试基本功能
        testLastTextPosition();
        
        // 2. 测试不同类型的PDF
        testDifferentPdfTypes();
        
        log.info("测试完成");
    }
}
