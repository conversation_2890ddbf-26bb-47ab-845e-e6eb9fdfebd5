<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>ep-parent</artifactId>
    <groupId>io.terminus.gaia</groupId>
    <version>1.0.0.ZJ01.PROD-SNAPSHOT</version>
  </parent>

  <groupId>io.terminus.gaia</groupId>
  <artifactId>ep-contract</artifactId>
  <packaging>pom</packaging>
  <version>1.0.0.ZJ01.PROD-SNAPSHOT</version>
  <modules>
    <module>gaia-contract-api</module>
    <module>gaia-contract-implement</module>
    <module>b2b-contract-api</module>
    <module>b2b-contract-implement</module>
    <module>ep-contract-runtime</module>
    <module>contract-common</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <logstash-logback-encoder.version>6.3</logstash-logback-encoder.version>
    <ooxml.schemas.version>1.4</ooxml.schemas.version>
    <easyexcel.version>3.1.5</easyexcel.version>
  </properties>

  <profiles>
    <profile>
      <id>local</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <trantor.metastore.url>
          http://localhost:8082/terminus/gaia
        </trantor.metastore.url>
      </properties>
    </profile>
    <profile>
      <id>dev</id>
      <properties>
        <trantor.metastore.url>
          http://metastore-management-f6a425b85f.project-2-test.svc.cluster.local:8080/terminus/gaia
        </trantor.metastore.url>
      </properties>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <trantor.metastore.url>
          https://ms.zcscm.net/terminus/gaia
        </trantor.metastore.url>
      </properties>
    </profile>
  </profiles>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.terminus.gaia</groupId>
        <artifactId>gaia-contract-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>io.terminus.gaia</groupId>
        <artifactId>gaia-contract-implement</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>io.terminus.gaia</groupId>
        <artifactId>b2b-contract-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>io.terminus.gaia</groupId>
        <artifactId>b2b-contract-implement</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- poi -->
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>ooxml-schemas</artifactId>
        <version>${ooxml.schemas.version}</version>
      </dependency>

      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool-all.version}</version>
      </dependency>

      <dependency>
        <groupId>io.github.mouzt</groupId>
        <artifactId>bizlog-sdk</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>${easyexcel.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <distributionManagement>
    <repository>
      <id>terminus</id>
      <name>terminus release repository</name>
      <url>https://repo.terminus.io/repository/releases/</url>
    </repository>
    <snapshotRepository>
      <id>terminus</id>
      <name>terminus snapshot repository</name>
      <url>https://repo.terminus.io/repository/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

</project>