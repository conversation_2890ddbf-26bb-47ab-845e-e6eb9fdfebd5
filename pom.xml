<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>erp-contract</artifactId>
    <version>1.0.0.WQ.UAT-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>erp-contract</name>
    <groupId>io.terminus.erp</groupId>

    <parent>
        <groupId>io.terminus.wq</groupId>
        <artifactId>erp-wq-parent</artifactId>
        <version>1.0.0.WQ.UAT-SNAPSHOT</version>
    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <ooxml.schemas.version>1.4</ooxml.schemas.version>
    </properties>

    <modules>
        <module>erp-contract-adapter</module>
        <module>erp-contract-app</module>
        <module>erp-contract-domain</module>
        <module>erp-contract-infrastructure</module>
        <module>erp-contract-spi</module>
<!--        <module>erp-contract-starter</module>-->
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-contract-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-contract-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-contract-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-contract-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-contract-spi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-md-spi</artifactId>
                <version>${erp.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-md-infrastructure</artifactId>
                <version>${erp.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.common</groupId>
                <artifactId>terminus-spring-boot-starter-rocketmq</artifactId>
                <version>${terminus.rocketmq.version}</version>
            </dependency>
            <!-- poi -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>ooxml-schemas</artifactId>
                <version>${ooxml.schemas.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus</groupId>
                <artifactId>third-party-common</artifactId>
                <version>${third.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus</groupId>
                <artifactId>third-party-sign-api</artifactId>
                <version>${third.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus</groupId>
                <artifactId>third-party-sign-esign-cloud</artifactId>
                <version>${third.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus</groupId>
                <artifactId>third-party-sign-qiyuesuo-private</artifactId>
                <version>${third.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.wq</groupId>
                <artifactId>wq-md-spi</artifactId>
                <version>${erp.wq.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-purchase-spi</artifactId>
                <version>${erp.version}</version>
                <scope>compile</scope>
            </dependency>
            <!-- itext7html转pdf  -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>html2pdf</artifactId>
                <version>3.0.2</version>
            </dependency>
            <!-- 中文字体支持 -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>font-asian</artifactId>
                <version>7.1.13</version>
            </dependency>

            <!-- 微信支付 -->
            <dependency>
                <groupId>com.github.wxpay</groupId>
                <artifactId>wxpay-sdk</artifactId>
                <version>0.0.3</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.trantor</groupId>
                <artifactId>trantor-org-api</artifactId>
                <version>${trantor.org.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>terminus-notice-sdk</artifactId>
                <version>${terminus.notice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.4.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.4.0</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-service-runtime-impl</artifactId>
                <version>${terminus.trantor2.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.trantor.workflow</groupId>
                <artifactId>workflow-sdk</artifactId>
                <version>2.5.42.24.0330.RELEASE</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>io.terminus</groupId>
                <artifactId>terminus-code-generation</artifactId>
                <version>1.0.9-RELEASE</version>
                <configuration>
                    <!-- 开发环境元数据相关信息，其他环境请自行替换 -->
                    <httpInfo>
                        <iamUrl>portal-iam-dev.nonprod.hqzc.com/</iamUrl>
                        <consoleUrl>console-iam-staging.app.terminus.io</consoleUrl>
                        <loginApiEndpoint>/iam/api/v1/user/login/account</loginApiEndpoint>
                        <metaApiEndpoint>/api/trantor/struct-node/related-by-alias</metaApiEndpoint>
                        <account>***********</account>
                        <password>wtc123456*</password>
                        <teamId>1</teamId>
                        <appId>16206</appId>
                    </httpInfo>
                    <!-- 基本信息配置 -->
                    <generateCodeInfo>
                        <!-- 待生成代码的表信息 -->
                        <modelInfoList>
                            <modelInfo>
                                <!-- 分组名： io.terminus.erp.contract.spi.model.tc -->
                                <domain>tp</domain>
                                <modelCodeList>
                                    <value>con_signatory_account_recharge</value>
                                    <value>con_ct_signatory_account_tr</value>
                                </modelCodeList>
                            </modelInfo>
                        </modelInfoList>
                        <!-- 是否允许覆盖 -->
                        <allowOverwrite>true</allowOverwrite>
                        <!-- 是否允许 生成瞬时模型的关联模型 默认为false -->
                        <generateLinkObj>false</generateLinkObj>
                        <!-- 增加初始化DTO中link many List -->
                        <initOneToManyLink>false</initOneToManyLink>
                        <author>robot</author>
                        <!-- 生成代码的包名 -->
                        <tablePackageName>io.terminus.erp.contract</tablePackageName>
                    </generateCodeInfo>
                    <groupInfoList>
                        <!-- 按照不同的模板生成不同的目录 进行分组 -->
                        <groupInfo>
                            <savePath>erp-contract-spi</savePath>
                            <templateInfoList>
                                <templateInfo>
                                    <scope>MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/PO.java.vm</value>
                                        <value>template/default/Converter.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                                <templateInfo>
                                    <scope>TRANSIENT_MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/DTO.java.vm</value>
<!--                                        <value>template/default/VO.java.vm</value>-->
<!--                                        <value>template/default/SO.java.vm</value>-->
<!--                                        <value>template/default/CreateREQ.java.vm</value>-->
<!--                                        <value>template/default/PageREQ.java.vm</value>-->
<!--                                        <value>template/default/UpdateREQ.java.vm</value>-->
                                    </templateUrlList>
                                </templateInfo>
                                <templateInfo>
                                    <scope>DICT</scope>
                                    <templateUrlList>
                                        <value>template/default/Dict.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                            </templateInfoList>
                        </groupInfo>
                        <groupInfo>
                            <savePath>erp-contract-infrastructure</savePath>
                            <templateInfoList>
                                <templateInfo>
                                    <scope>MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/Repo.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                            </templateInfoList>
                        </groupInfo>
                    </groupInfoList>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
