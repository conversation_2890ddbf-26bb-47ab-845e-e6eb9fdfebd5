package io.terminus.erp.contract.starter;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = {"io.terminus.erp.contract"})
@MapperScan(value = {"io.terminus.erp.md.infrastructure.repo", "io.terminus.erp.contract"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
