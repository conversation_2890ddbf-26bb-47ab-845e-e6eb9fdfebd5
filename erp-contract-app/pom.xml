<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>erp-contract</artifactId>
        <groupId>io.terminus.erp</groupId>
        <version>1.0.0.WQ.UAT-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>erp-contract-app</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.terminus.erp</groupId>
            <artifactId>erp-contract-domain</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.common</groupId>
                    <artifactId>terminus-common-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.common</groupId>
                    <artifactId>terminus-spring-boot-starter-mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
