package io.terminus.erp.contract.infrastructure.repo.tp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignRemindRecordTrPO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 签署提醒记录(ConCtSignRemindRecordTr)表数据库访问层
 *
 * <AUTHOR>
 * @since  2024-01-01 00:00:00
 */
@Repository
public interface ConCtSignRemindRecordTrRepo extends BaseRepository<ConCtSignRemindRecordTrPO> {

    /**
     * 根据签署任务ID和签署方ID查询最近的提醒记录
     * @param signTaskId 签署任务ID
     * @param signatoryId 签署方ID
     * @return 最近的提醒记录
     */
    default ConCtSignRemindRecordTrPO findLatestRemindRecord(Long signTaskId, Long signatoryId) {
        LambdaQueryWrapper<ConCtSignRemindRecordTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConCtSignRemindRecordTrPO::getSignTaskId, signTaskId);
        wrapper.eq(ConCtSignRemindRecordTrPO::getSignatoryId, signatoryId);
        wrapper.orderByDesc(ConCtSignRemindRecordTrPO::getSmsSendTime);
        wrapper.last("limit 1");
        return this.selectOne(wrapper);
    }

    /**
     * 根据签署任务ID查询所有提醒记录
     * @param signTaskId 签署任务ID
     * @return 提醒记录列表
     */
    default List<ConCtSignRemindRecordTrPO> findBySignTaskId(Long signTaskId) {
        LambdaQueryWrapper<ConCtSignRemindRecordTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConCtSignRemindRecordTrPO::getSignTaskId, signTaskId);
        wrapper.orderByDesc(ConCtSignRemindRecordTrPO::getSmsSendTime);
        return this.selectList(wrapper);
    }

    /**
     * 查询指定时间范围内的提醒记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 提醒记录列表
     */
    default List<ConCtSignRemindRecordTrPO> findBySmsSendTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<ConCtSignRemindRecordTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(ConCtSignRemindRecordTrPO::getSmsSendTime, startTime, endTime);
        wrapper.orderByDesc(ConCtSignRemindRecordTrPO::getSmsSendTime);
        return this.selectList(wrapper);
    }
} 