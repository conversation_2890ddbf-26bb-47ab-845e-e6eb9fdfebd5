package io.terminus.erp.contract.infrastructure.repo.tp;

import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.erp.contract.spi.model.tp.po.ConSignatoryAccountUsedPO;

/**
 * 签章账户使用明细表(ConSignatoryAccountUsed)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-11-08 13:49:01
 */
@Repository
public interface ConSignatoryAccountUsedRepo extends BaseRepository<ConSignatoryAccountUsedPO> {

}
