package io.terminus.erp.contract.infrastructure.repo.tp;

import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.erp.contract.spi.model.tp.po.ConCtEsignatureTrPO;

/**
 * 电子签名(ConCtEsignatureTr)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-10-15 17:05:43
 */
@Repository
public interface ConCtEsignatureTrRepo extends BaseRepository<ConCtEsignatureTrPO> {


    default void updateThirdCompanyId(Long id, long thirdCompanyId){
        ConCtEsignatureTrPO conCtEsignatureTrPO = new ConCtEsignatureTrPO();
        conCtEsignatureTrPO.setId(id);
        conCtEsignatureTrPO.setOrgId(String.valueOf(thirdCompanyId));
        updateById(conCtEsignatureTrPO);
    }

}
