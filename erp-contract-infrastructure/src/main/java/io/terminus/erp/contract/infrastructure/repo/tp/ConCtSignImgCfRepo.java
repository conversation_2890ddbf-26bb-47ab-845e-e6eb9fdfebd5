package io.terminus.erp.contract.infrastructure.repo.tp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignImgCfPO;

/**
 * (ConCtSignImgCf)表数据库访问层
 *
 * <AUTHOR> 
 * @since  2024-11-15 16:13:39
 */
@Repository
public interface ConCtSignImgCfRepo extends BaseRepository<ConCtSignImgCfPO> {

    default ConCtSignImgCfPO getSignImageUrl(long operator, long purOrgId) {
        return getSignImage(operator, purOrgId);
    }


    default ConCtSignImgCfPO getSignImage(long operator, long purOrgId) {
        // 查询配置表
        LambdaQueryWrapper<ConCtSignImgCfPO> conCtSignImgCfPOWrapper = new LambdaQueryWrapper<>();
        conCtSignImgCfPOWrapper.eq(ConCtSignImgCfPO::getOperator, operator);
        conCtSignImgCfPOWrapper.eq(ConCtSignImgCfPO::getOrgPurOrgCfId, purOrgId);
        // 有唯一索引保障
        return selectOne(conCtSignImgCfPOWrapper);
    }
}
