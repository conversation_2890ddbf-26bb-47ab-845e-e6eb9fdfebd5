package io.terminus.erp.contract.infrastructure.repo.role;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.erp.contract.spi.model.role.po.ConRoleItemTrPO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConRoleItemTrRepo extends BaseRepository<ConRoleItemTrPO> {

    default List<ConRoleItemTrPO> findByRoleIds(List<String> roleKeys) {
        LambdaQueryWrapper<ConRoleItemTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ConRoleItemTrPO::getRoleKey, roleKeys);
        return this.selectList(wrapper);
    }

}
