package io.terminus.erp.contract.infrastructure.repo.tp;

import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignchannalCfPO;

/**
 * 合同签署渠道类型(ConCtSignchannalCf)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-08-07 16:27:33
 */
@Repository
public interface ConCtSignchannalCfRepo extends BaseRepository<ConCtSignchannalCfPO> {

}
