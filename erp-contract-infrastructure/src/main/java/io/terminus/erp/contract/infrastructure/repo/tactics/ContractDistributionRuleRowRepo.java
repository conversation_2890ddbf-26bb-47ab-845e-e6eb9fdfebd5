package io.terminus.erp.contract.infrastructure.repo.tactics;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.api.model.BaseModel;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.erp.contract.spi.model.tactics.po.ContractDistributionRuleRowPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public interface ContractDistributionRuleRowRepo extends BaseRepository<ContractDistributionRuleRowPO> {

    default void deleteLineByHeaderId(Long headerId) {
        LambdaQueryWrapper<ContractDistributionRuleRowPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractDistributionRuleRowPO::getDistributionConfigurationHeader, headerId);
        List<ContractDistributionRuleRowPO> contractDistributionRuleRowPOS = selectList(wrapper);
        if (CollectionUtils.isEmpty(contractDistributionRuleRowPOS)) {
            return;
        }
        deleteBatchIds(contractDistributionRuleRowPOS.stream().map(BaseModel::getId).collect(Collectors.toList()));
    }

}
