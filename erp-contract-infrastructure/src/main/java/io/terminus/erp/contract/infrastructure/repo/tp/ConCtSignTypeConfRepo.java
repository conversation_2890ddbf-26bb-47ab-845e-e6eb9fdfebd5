package io.terminus.erp.contract.infrastructure.repo.tp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSignTypeConfPO;

/**
 * 签署类型配置(ConCtSignTypeConf)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-09-05 20:01:16
 */
@Repository
public interface ConCtSignTypeConfRepo extends BaseRepository<ConCtSignTypeConfPO> {

    default ConCtSignTypeConfPO selectByCode(String code) {
        LambdaQueryWrapper<ConCtSignTypeConfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConCtSignTypeConfPO::getCode, code);
        return this.selectOne(queryWrapper);
    }

}
