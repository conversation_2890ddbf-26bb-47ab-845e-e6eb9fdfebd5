package io.terminus.erp.contract.infrastructure.repo.tp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.erp.contract.spi.model.tp.po.ConCtSigntaskTrPO;

import java.util.List;

/**
 * 签署任务(ConCtSigntaskTr)表数据库访问层
 *
 * <AUTHOR>
 * @since  2023-10-18 15:04:34
 */
@Repository
public interface ConCtSigntaskTrRepo extends BaseRepository<ConCtSigntaskTrPO> {

    /**
     * 根据合同Id 查询合同关联的签署任务
     * @param ctConId 合同ID
     * @return 合同关联的签署任务
     */
    default List<ConCtSigntaskTrPO> findConCtSigntaskTrByCtConId(Long ctConId) {
        LambdaQueryWrapper<ConCtSigntaskTrPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConCtSigntaskTrPO::getSignBillId, ctConId);
        return this.selectList(wrapper);
    }

}
