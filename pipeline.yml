version: "1.1"
name: ""
stages:
  - stage:
      - git-checkout:
          alias: repo
          description: 代码仓库克隆
          params:
            depth: 1
  - stage:
      - custom-script:
          alias: deploy
          description: 运行自定义命令
          commands:
            - cat ~/.m2/settings.xml
            - sed -i "s/terminus/((maven.repository))/g" ~/.m2/settings.xml
            - sed -i "s/placeholder/((maven.repository))/g" ~/.m2/settings.xml
            - sed -i "s/{{BP_NEXUS_URL}}/http:\/\/((maven.url))/g" ~/.m2/settings.xml
            - sed -i "s/{{BP_NEXUS_USERNAME}}/((maven.user))/g" ~/.m2/settings.xml
            - sed -i "s/{{BP_NEXUS_PASSWORD}}/((maven.pwd))/g" ~/.m2/settings.xml
            - cat ~/.m2/settings.xml
            - cd ${repo}
            - mvn clean deploy -Dmaven.test.skip -Ddependency-check.skip=true -U
          resources:
            cpu: 1
            mem: 2048
