/.idea/
/erp-contract-adapter/erp-contract-adapter.iml
/erp-contract-app/erp-contract-app.iml
/erp-contract-domain/erp-contract-domain.iml
/erp-contract-infrastructure/erp-contract-infrastructure.iml
/erp-contract-spi/erp-contract-spi.iml
/erp-contract-starter/erp-contract-starter.iml
/erp-contract.iml
target/
*/src/*/java/META-INF
*/src/META-INF/
*/src/*/java/META-INF/
.classpath
.springBeans
.project
.DS_Store
.settings/
.idea/
out/
bin/
intellij/
build/
*.log
*.log.*
*.iml
*.ipr
*.iws
.gradle/
atlassian-ide-plugin.xml
!etc/eclipse/.checkstyle
.checkstyle
*.bak
application.properties
_book/
#application.yml
env/
frontend/node_modules/
frontend/dist/
src/main/java/io/terminus/parana/trade/starter/Test.java
tc-starter/tc-server-starter/src/main/java/io/terminus/parana/trade/starter/Test.java
rebel.xml
*.versionsBackup
/gaia-item-implement/src/main/resources/trantor/resources/*flow.json
*flow.json
.factorypath
.history
.vscode/
/*.env
/srm-runtime/src/main/resources/application-yf.yml
/deploy.sh

