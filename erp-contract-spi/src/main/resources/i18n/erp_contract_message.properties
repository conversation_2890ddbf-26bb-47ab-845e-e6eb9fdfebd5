con.ct.esignature.is.null=con.ct.esignature.is.null
con.ct.esignature.not.support=con.ct.esignature.not.support
con.ct.esignature.auth.invoke.failed=con.ct.esignature.auth.invoke.failed
con.ct.sign.task.param.is.null=con.ct.sign.task.param.is.null
con.ct.sign.task.contract.is.null=con.ct.sign.task.contract.is.null
con.ct.sign.task.signway.is.null=\u7B7E\u7F72\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A
con.ct.sign.task.signfile.is.null=\u7B7E\u7F72\u6587\u4EF6\u4E0D\u80FD\u4E3A\u7A7A
con.ct.sign.task.signatory.is.null=\u7B7E\u7F72\u65B9\u4E0D\u80FD\u4E3A\u7A7A
con.ct.sign.task.employee.is.null=\u4EE3\u529E\u4EBA\u4E0D\u80FD\u4E3A\u7A7A
con.ct.sign.task.user.is.null=\u7B7E\u7F72\u4EFB\u52A1\u7528\u6237\u4E0D\u80FD\u8FDD\u6297
con.ct.sign.task.is.exist=\u5B58\u5728\u7B7E\u7F72\u4EFB\u52A1
con.ct.sign.task.signatory.status.is.not.waiting=\u5F53\u524D\u72B6\u6001\u4E0D\u662F\u5F85\u7B7E\u7F72
con.ct.sign.task.signatory.update.fail=\u7B7E\u7F72\u65B9\u66F4\u65B0\u5931\u8D25
con.ct.sign.task.is.null=\u7B7E\u7F72\u4EFB\u52A1\u4E3A\u7A7A
con.ct.sign.task.status.is.not.signing=con.ct.sign.task.status.is.not.signing
con.ct.sign.task.signatory.status.is.not.waiting.or.rejected=\u7B7E\u7F72\u72B6\u6001\u4E0D\u662F\u5F85\u7B7E\u7F72\u6216\u8005\u62D2\u7EDD
con.ct.sign.task.esign.is.null=con.ct.sign.task.esign.is.null
con.ct.sign.task.esign.is.disabled=con.ct.sign.task.esign.is.disabled
con.ct.sign.task.signatory.status.is.not.signed=con.ct.sign.task.signatory.status.is.not.signed
con.ct.sign.task.upload.file.fail=\u7B7E\u7F72\u4E0A\u4F20\u6587\u4EF6\u5931\u8D25
con.ct.sign.task.create.fail=\u7B7E\u7F72\u4EFB\u52A1\u521B\u5EFA\u5931\u8D25
con.ct.sign.task.agent.is.null=con.ct.sign.task.agent.is.null
