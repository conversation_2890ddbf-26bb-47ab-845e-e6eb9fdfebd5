package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSignfileTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignfileTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 签署文件(ConCtSignfileTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@Mapper(componentModel = "spring")
public interface ConCtSignfileTrConverter {

    ConCtSignfileTrDTO po2Dto(ConCtSignfileTrPO req);

    List<ConCtSignfileTrDTO> po2DtoList(List<ConCtSignfileTrPO> poList);

    ConCtSignfileTrPO dto2Po(ConCtSignfileTrDTO req);

    List<ConCtSignfileTrPO> dto2PoList(List<ConCtSignfileTrDTO> dtoList);
}
