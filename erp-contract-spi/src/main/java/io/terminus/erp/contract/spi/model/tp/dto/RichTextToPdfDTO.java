package io.terminus.erp.contract.spi.model.tp.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @className: RichTextToPdfDTO
 * @author: charl
 * @date: 2023/11/3 15:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RichTextToPdfDTO extends BaseModel {

    @ApiModelProperty("富文本")
    private String richText;

    @MetaModelField
    @ApiModelProperty("关联合同")
    private Long ctHeadRef;

    @ApiModelProperty("PDF文件链接")
    private String pdfFileLink;

    @MetaModelField
    @ApiModelProperty("关联合同模版")
    private Long ctTpl;

}
