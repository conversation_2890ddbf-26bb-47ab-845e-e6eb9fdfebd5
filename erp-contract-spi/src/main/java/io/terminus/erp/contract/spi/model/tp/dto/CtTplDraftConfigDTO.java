package io.terminus.erp.contract.spi.model.tp.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (CtTplDraftConfig)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-07 19:39:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CtTplDraftConfigDTO extends BaseModel {
    private static final long serialVersionUID = -80246448944157032L;

    @ApiModelProperty("占位符")
    private String placeHolder;

    @ApiModelProperty("占位符名称")
    private String placeHolderName;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("值")
    private String value;

}
