package io.terminus.erp.contract.spi.model.tpl.po;


import java.util.ArrayList;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同模版占位符配置表(CtTemplatePlaceholderConfig)存储模型
 *
 * <AUTHOR>
 * @since  2023-10-24 16:17:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ct_template_placeholder_config")
public class CtTemplatePlaceholderConfigPO extends BaseModel {
    private static final long serialVersionUID = -31724093431460960L;

    @ApiModelProperty("位置类型")
    @TableField("`type`")
    private String type;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("占位符")
    @TableField("`key`")
    private String key;

    @ApiModelProperty("占位符对应值")
    @TableField("`value`")
    private String value;

    @ApiModelProperty("业务类型")
    @TableField("`biz_type`")
    private Long bizType;

    @ApiModelProperty("内容类型")
    @TableField("`content_type`")
    private String contentType;

    @ApiModelProperty("排序字段（从小到大）")
    @TableField("`sort`")
    private Integer sort;

    @ApiModelProperty("备注信息")
    @TableField("`remark`")
    private String remark;

}
