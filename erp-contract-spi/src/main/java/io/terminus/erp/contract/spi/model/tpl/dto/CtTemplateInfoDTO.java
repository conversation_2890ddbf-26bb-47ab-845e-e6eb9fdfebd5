package io.terminus.erp.contract.spi.model.tpl.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 合同模版数据表(CtTemplateInfo)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-18 15:24:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CtTemplateInfoDTO extends BaseModel {
    private static final long serialVersionUID = -26663084312167091L;

    @ApiModelProperty("合同模版编码")
    private String code;

    @ApiModelProperty("合同模版名称")
    private String name;

    @MetaModelField
    @ApiModelProperty("业务类型")
    private CtTplTypeCfDTO bizType;

    @ApiModelProperty("合同模版类型")
    private String templateType;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("合同模版富文本")
    private String templateInfo;

}
