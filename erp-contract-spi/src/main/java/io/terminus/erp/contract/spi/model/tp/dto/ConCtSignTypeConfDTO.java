package io.terminus.erp.contract.spi.model.tp.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署类型配置(ConCtSignTypeConf)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-05 20:01:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignTypeConfDTO extends BaseModel {
    private static final long serialVersionUID = 808717384777467458L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("甲方签署")
    private Boolean partyASign;

    @ApiModelProperty("乙方签署")
    private Boolean partyBSign;

    @ApiModelProperty("签署顺序：开启甲方先签，关闭乙方先签")
    private Boolean partyAFirst;

    @ApiModelProperty("是否校验电子签名：线下不需校验电子签名；线上需要校验电子签名")
    private Boolean verifyESign;

    @ApiModelProperty("关联渠道")
    private Long channel;

}
