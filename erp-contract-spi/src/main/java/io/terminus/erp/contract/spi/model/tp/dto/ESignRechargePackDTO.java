package io.terminus.erp.contract.spi.model.tp.dto;


import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 电子签章充值套餐(ESignRechargePack)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-06 16:07:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ESignRechargePackDTO extends BaseModel {
    private static final long serialVersionUID = 294908404117263757L;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("金额")
    private BigDecimal amt;

}
