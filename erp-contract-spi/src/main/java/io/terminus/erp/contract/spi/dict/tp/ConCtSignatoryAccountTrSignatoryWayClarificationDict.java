package io.terminus.erp.contract.spi.dict.tp;

/**
 * 认证签署说明(ConCtSignatoryAccountTrSignatoryWayClarification)字典
 *
 * <AUTHOR>
 * @since  2023-10-15 13:08:03
 */
@Deprecated
public interface ConCtSignatoryAccountTrSignatoryWayClarificationDict {

    /**
     * UKey签署首次使用需要缴纳300元，付款成功后会制作UKey邮寄给您
     */
    String UKEY = "Ukey";
    /**
     * 事件型签署通过预付费的方式缴费,每份合同收取1元
     */
    String NUMBER = "number";

}
