package io.terminus.erp.contract.spi.model.role.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "con_role_item_tr")
public class ConRoleItemTrPO extends BaseModel {

    @TableField("`role_name`")
    @ApiModelProperty("角色名称")
    private String roleName;

    @TableField("`role_key`")
    @ApiModelProperty("角色标识")
    private String roleKey;

    @TableField("con_org_role_tr_id")
    @ApiModelProperty("权限头ID")
    private Long conOrgRoleTrId;

}
