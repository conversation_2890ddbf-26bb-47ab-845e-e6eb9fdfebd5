package io.terminus.erp.contract.spi.model.tp.po;


import java.time.LocalDateTime;
    
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 签署方(ConCtSignatoryTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-11 09:48:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_signatory_tr")
public class ConCtSignatoryTrPO extends BaseModel {
    private static final long serialVersionUID = -29116423877929060L;

    @ApiModelProperty("关联签署任务")
    @TableField("`sign_task`")
    private Long signTask;

    @ApiModelProperty("签署方")
    @TableField("`signatory`")
    private Long signatory;

    @ApiModelProperty("签署顺序")
    @TableField("`sort_num`")
    private Integer sortNum;

    @ApiModelProperty("签署状态")
    @TableField("`signatory_status`")
    private String signatoryStatus;

    @ApiModelProperty("签署时间")
    @TableField("`sign_time`")
    private LocalDateTime signTime;

    @ApiModelProperty("签署页面url")
    @TableField("`sign_url`")
    private String signUrl;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty("关联电子签名")
    @TableField("`e_sign`")
    private Long eSign;

    @ApiModelProperty("代办人")
    @TableField("`agent`")
    private Long agent;

    @ApiModelProperty("签署方类型")
    @TableField("`signatory_type`")
    private String signatoryType;

    @ApiModelProperty("签署方式")
    @TableField("sign_type")
    private String signType;

    // 线上签署
    public final static String SIGN_TYPE_ONLINE = "SIGN_TYPE_ONLINE";
    // 线下签署
    public final static String SIGN_TYPE_OFFLINE = "SIGN_TYPE_OFFLINE";


    public void seteSign(Long eSign) {
        this.eSign = eSign;
    }

    public Long geteSign() {
        return eSign;
    }
}
