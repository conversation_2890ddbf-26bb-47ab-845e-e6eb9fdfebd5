package io.terminus.erp.contract.spi.model.tp.dto;


import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 签署任务(ConCtSigntaskTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-08 16:42:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSigntaskTrDTO extends BaseModel {
    private static final long serialVersionUID = -88342425377300585L;

    @ApiModelProperty("任务编码")
    private String code;

    @ApiModelProperty("任务标题")
    private String title;

    @ApiModelProperty("签署状态")
    private String signStatus;

    @MetaModelField
    @ApiModelProperty("签署方式")
    private Long signWay;

    @MetaModelField
    @ApiModelProperty("所属公司")
    private Long company;

    @ApiModelProperty("签署单据ID")
    private Long signBillId;

    @ApiModelProperty("签署单据编码")
    private String signBillCode;

    @MetaModelField
    @ApiModelProperty("发起人")
    private Long sponsor;

    @ApiModelProperty("签署完成时间")
    private LocalDateTime finishTime;

    @ApiModelProperty("三方签署任务ID")
    private String signFlowId;

    @ApiModelProperty("签署方")
    private List<ConCtSignatoryTrDTO> signatories;

    @ApiModelProperty("签署文件")
    private List<ConCtSignfileTrDTO> signFiles;

    @ApiModelProperty("签署记录")
    private List<ConCtSignrecordTrDTO> signRecords;

    @ApiModelProperty("签署单据类型")
    private String signBillType;

    @ApiModelProperty("采购组织")
    private Long orgRef;

    @ApiModelProperty("签约组织")
    private Long orgRuleId;

}
