package io.terminus.erp.contract.spi.model.tp.req;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同签署渠道类型(ConCtSignchannalCf)更新请求
 *
 * <AUTHOR>
 * @since  2023-08-07 16:27:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignchannalCfUpdateReq extends AbstractRequest {
    private static final long serialVersionUID = 657659630328625694L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("路由标识")
    private String routeKey;

}
