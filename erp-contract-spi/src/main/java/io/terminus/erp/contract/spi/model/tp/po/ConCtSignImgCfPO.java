package io.terminus.erp.contract.spi.model.tp.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ConCtSignImgCf)存储模型
 *
 * <AUTHOR> 
 * @since  2024-11-15 16:13:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_sign_img_cf")
public class ConCtSignImgCfPO extends BaseModel {
    private static final long serialVersionUID = 437830079721423631L;

    @ApiModelProperty("经办人员工")
    @TableField("`operator`")
    private Long operator;

    @ApiModelProperty("采购组织")
    @TableField("`org_pur_org_cf_id`")
    private Long orgPurOrgCfId;

    @ApiModelProperty("实际签署人")
    @TableField("`sign_employee`")
    private Long signEmployee;

    @ApiModelProperty("签名图片")
    @TableField("`sign_img_url`")
    private String signImgUrl;

}
