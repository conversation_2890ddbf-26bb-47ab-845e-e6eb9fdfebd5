package io.terminus.erp.contract.spi.model.tp.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同签署渠道类型(ConCtSignchannalCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-08-07 16:27:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_signchannal_cf")
public class ConCtSignchannalCfPO extends BaseModel {
    private static final long serialVersionUID = -86591241925507707L;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("路由标识")
    @TableField("`route_key`")
    private String routeKey;

}
