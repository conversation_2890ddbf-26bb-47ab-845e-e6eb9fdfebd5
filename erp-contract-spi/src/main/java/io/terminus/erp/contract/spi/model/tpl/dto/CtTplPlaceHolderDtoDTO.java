package io.terminus.erp.contract.spi.model.tpl.dto;


import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (CtTplPlaceHolderDto)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-17 19:55:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CtTplPlaceHolderDtoDTO extends BaseModel {
    private static final long serialVersionUID = 855152599509995759L;

    @ApiModelProperty("占位符类型")
    private String type;

    @ApiModelProperty("占位符配置")
    private List<CtTemplatePlaceholderConfigDTO> tplPrefixList;

}
