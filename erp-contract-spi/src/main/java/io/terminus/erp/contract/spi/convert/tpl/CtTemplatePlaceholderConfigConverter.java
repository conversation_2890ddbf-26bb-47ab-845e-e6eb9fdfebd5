package io.terminus.erp.contract.spi.convert.tpl;

import io.terminus.erp.contract.spi.model.tpl.po.CtTemplatePlaceholderConfigPO;
import io.terminus.erp.contract.spi.model.tpl.dto.CtTemplatePlaceholderConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * 合同模版占位符配置表(CtTemplatePlaceholderConfig)结构映射器
 *
 * <AUTHOR>
 * @since  2023-10-24 16:17:36
 */
@Mapper(componentModel = "spring")
public interface CtTemplatePlaceholderConfigConverter {

    @Mapping(target = "bizType.id", source = "bizType")
    CtTemplatePlaceholderConfigDTO po2Dto(CtTemplatePlaceholderConfigPO req);

    List<CtTemplatePlaceholderConfigDTO> po2DtoList(List<CtTemplatePlaceholderConfigPO> poList);

    @Mapping(target = "bizType", source = "bizType.id")
    CtTemplatePlaceholderConfigPO dto2Po(CtTemplatePlaceholderConfigDTO req);

    List<CtTemplatePlaceholderConfigPO> dto2PoList(List<CtTemplatePlaceholderConfigDTO> dtoList);
}
