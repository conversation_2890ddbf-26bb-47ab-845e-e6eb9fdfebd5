package io.terminus.erp.contract.spi.model.tp.req;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署文件(ConCtSignfileTr)创建请求
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignfileTrCreateReq extends AbstractRequest {
    private static final long serialVersionUID = -93306899620452530L;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("签署文件（只支持PDF）")
    private String signFile;

    @ApiModelProperty("已签署文件（只支持PDF）")
    private String signedFile;

    @ApiModelProperty("关联签署任务")
    private Long signTask;

    @ApiModelProperty("关联三方签署平台文件id")
    private String thirdFileId;

}
