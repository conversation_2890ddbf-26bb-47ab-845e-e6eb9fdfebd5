package io.terminus.erp.contract.spi.model.tp.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同签署类型配置(ConCtSigntypeCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-08-04 17:12:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_signtype_cf")
public class ConCtSigntypeCfPO extends BaseModel {
    private static final long serialVersionUID = -14063776655793678L;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("启用甲方签署")
    @TableField("`party_a_sign`")
    private Boolean partyASign;

    @ApiModelProperty("启用乙方签署")
    @TableField("`party_b_sign`")
    private Boolean partyBSign;

    @ApiModelProperty("签署顺序：开启甲方先签，关闭乙方先签")
    @TableField("`party_a_first`")
    private Boolean partyAFirst;

}
