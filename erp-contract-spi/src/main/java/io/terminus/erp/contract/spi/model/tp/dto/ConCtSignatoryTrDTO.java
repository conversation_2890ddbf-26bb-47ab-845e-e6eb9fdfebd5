package io.terminus.erp.contract.spi.model.tp.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import io.terminus.trantor2.common.user.User;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 签署方(ConCtSignatoryTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-09-11 09:48:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignatoryTrDTO extends BaseModel {
    private static final long serialVersionUID = -64031193035703764L;

    @ApiModelProperty("关联签署任务")
    private Long signTask;

    @ApiModelProperty("签署方")
    private GenComTypeCfDTO signatory;

    @ApiModelProperty("签署顺序")
    private Integer sortNum;

    @ApiModelProperty("签署状态")
    private String signatoryStatus;

    @ApiModelProperty("签署时间")
    private LocalDateTime signTime;

    @ApiModelProperty("签署记录")
    private List<ConCtSignrecordTrDTO> signRecords;

    @ApiModelProperty("签署页面url")
    private String signUrl;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @ApiModelProperty("关联电子签名")
    private Long eSign;

    @ApiModelProperty("代办人")
    private User agent;

    @ApiModelProperty("签署方类型")
    private String signatoryType;

    @ApiModelProperty("签署方式")
    private String signType;

    public void seteSign(Long eSign) {
        this.eSign = eSign;
    }

    public Long geteSign() {
        return eSign;
    }

}
