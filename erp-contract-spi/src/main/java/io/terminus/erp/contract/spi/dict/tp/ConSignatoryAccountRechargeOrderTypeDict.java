package io.terminus.erp.contract.spi.dict.tp;

/**
 * 订单类型(ConSignatoryAccountRechargeOrderType)字典
 *
 * <AUTHOR>
 * @since  2023-11-07 13:32:30
 */
public interface ConSignatoryAccountRechargeOrderTypeDict {

    /**
     * 首次费用
     */
    String INITIAL_COSTS = "Initial_costs";
    /**
     * 年费续费
     */
    String ANNUAL_FEE_RENEWAL = "Annual_fee_renewal";
    /**
     * 预付费
     */
    String PREPAY = "prepay";

}
