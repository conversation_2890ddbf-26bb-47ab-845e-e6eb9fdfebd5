package io.terminus.erp.contract.spi.model.tactics.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 合同共享池配单展示规则明细行
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "contract_distribution_rule_row_table")
public class ContractDistributionRuleRowPO extends BaseModel {

    @ApiModelProperty("采购组织")
    @TableField("purchasing_organization")
    private Long purchasingOrganization;

    @ApiModelProperty("业务类型")
    @TableField(value = "biz_type", typeHandler = JacksonTypeHandler.class)
    private List<String> bizType;

    @ApiModelProperty("描述信息")
    @TableField("`desc`")
    private String desc;

    @ApiModelProperty("关联投标")
    @TableField("distribution_configuration_header")
    private Long distributionConfigurationHeader;

}
