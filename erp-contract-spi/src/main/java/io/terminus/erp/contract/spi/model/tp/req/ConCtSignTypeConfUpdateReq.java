package io.terminus.erp.contract.spi.model.tp.req;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署类型配置(ConCtSignTypeConf)更新请求
 *
 * <AUTHOR>
 * @since  2023-08-11 10:47:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignTypeConfUpdateReq extends AbstractRequest {
    private static final long serialVersionUID = -94706436424723830L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("甲方签署")
    private Boolean partyASign;

    @ApiModelProperty("乙方签署")
    private Boolean partyBSign;

    @ApiModelProperty("签署顺序：开启甲方先签，关闭乙方先签")
    private Boolean partyAFirst;

    @ApiModelProperty("是否校验电子签名：线下不需校验电子签名；线上需要校验电子签名")
    private Boolean verifyESign;

}
