package io.terminus.erp.contract.spi.model.tp.po;


import java.math.BigDecimal;
import java.util.ArrayList;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电子签章充值套餐(ESignRechargePack)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-06 16:07:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "e_sign_recharge_pack")
public class ESignRechargePackPO extends BaseModel {
    private static final long serialVersionUID = -93479734390000147L;

    @ApiModelProperty("类型")
    @TableField("`type`")
    private String type;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("金额")
    @TableField("`amt`")
    private BigDecimal amt;

}
