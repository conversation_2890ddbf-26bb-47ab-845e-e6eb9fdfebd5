package io.terminus.erp.contract.spi.model.tp.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署提醒记录(ConCtSignRemindRecordTr)存储模型
 *
 * <AUTHOR>
 * @since  2024-01-01 00:00:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_sign_remind_record_tr")
public class ConCtSignRemindRecordTrPO extends BaseModel {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("合同ID")
    @TableField("`contract_id`")
    private Long contractId;

    @ApiModelProperty("签署任务ID")
    @TableField("`sign_task_id`")
    private Long signTaskId;

    @ApiModelProperty("签署方ID")
    @TableField("`signatory_id`")
    private Long signatoryId;

    @ApiModelProperty("短信发送时间")
    @TableField("`sms_send_time`")
    private LocalDateTime smsSendTime;

    @ApiModelProperty("手机号码")
    @TableField("`mobile`")
    private String mobile;

    @ApiModelProperty("提醒类型：1-每2天提醒，2-每天提醒")
    @TableField("`remind_type`")
    private String remindType;

    @ApiModelProperty("甲方签署时间")
    @TableField("`party_a_sign_time`")
    private LocalDateTime partyASignTime;

    @ApiModelProperty("距离甲方签署天数")
    @TableField("`days_since_party_a_sign`")
    private BigDecimal daysSincePartyASign;
} 