package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSignatoryAccountTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignatoryAccountTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (ConCtSignatoryAccountTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-08 14:46:07
 */
@Mapper(componentModel = "spring")
public interface ConCtSignatoryAccountTrConverter {

    ConCtSignatoryAccountTrDTO po2Dto(ConCtSignatoryAccountTrPO req);

    List<ConCtSignatoryAccountTrDTO> po2DtoList(List<ConCtSignatoryAccountTrPO> poList);

    ConCtSignatoryAccountTrPO dto2Po(ConCtSignatoryAccountTrDTO req);

    List<ConCtSignatoryAccountTrPO> dto2PoList(List<ConCtSignatoryAccountTrDTO> dtoList);
}
