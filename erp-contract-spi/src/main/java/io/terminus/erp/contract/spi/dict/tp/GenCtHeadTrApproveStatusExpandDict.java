package io.terminus.erp.contract.spi.dict.tp;
import io.terminus.erp.md.spi.dict.ct.GenCtHeadTrApproveStatusDict;

/**
 * 审批状态(GenCtHeadTrApproveStatus)字典
 *
 * <AUTHOR>
 * @since  2023-12-27 16:51:30
 */
public interface GenCtHeadTrApproveStatusExpandDict extends GenCtHeadTrApproveStatusDict {

    /**
     * 申请审批中
     */
    String EXT_WQ_ADD_INPROCESS = "EXT_WQ_ADD_INPROCESS";
    /**
     * 申请审批通过
     */
    String EXT_WQ_ADD_APPROVED = "EXT_WQ_ADD_APPROVED";
    /**
     * 申请审批拒绝
     */
    String EXT_WQ_ADD_REJECTED = "EXT_WQ_ADD_REJECTED";
    /**
     * 编制审批中
     */
    String EXT_WQ_DRAFT_INPROCESS = "EXT_WQ_DRAFT_INPROCESS";
    /**
     * 编制审批通过
     */
    String EXT_WQ_DRAFT_APPROVED = "EXT_WQ_DRAFT_APPROVED";
    /**
     * 编制审批拒绝
     */
    String EXT_WQ_DRAFT_REJECTED = "EXT_WQ_DRAFT_REJECTED";

}
