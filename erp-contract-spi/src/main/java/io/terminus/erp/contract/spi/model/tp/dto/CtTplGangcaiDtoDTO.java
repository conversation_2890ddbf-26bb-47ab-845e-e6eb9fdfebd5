package io.terminus.erp.contract.spi.model.tp.dto;


import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (CtTplGangcaiDto)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-07 19:53:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CtTplGangcaiDtoDTO extends BaseModel {
    private static final long serialVersionUID = -41351822512822855L;

    @ApiModelProperty("占位符")
    private String placeHolder;

    @ApiModelProperty("占位符名称")
    private String placeHolderName;

    @ApiModelProperty("编制模版信息")
    private List<CtTplDraftConfigDTO> draftConfig;

}
