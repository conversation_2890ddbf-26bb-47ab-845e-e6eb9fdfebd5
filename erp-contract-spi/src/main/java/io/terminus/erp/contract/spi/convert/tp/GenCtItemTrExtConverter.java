package io.terminus.erp.contract.spi.convert.tp;


import java.math.BigDecimal;
import java.util.ArrayList;

import io.terminus.erp.md.spi.model.dto.ct.CtItemTrDTO;
import io.terminus.erp.md.spi.model.po.ct.CtHeadTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtItemTrExtDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.Objects;
import org.apache.commons.collections.MapUtils;

/**
 * 协议合同行表(GenCtItemTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-12-27 16:52:29
 */
@Mapper(componentModel = "spring")
public interface GenCtItemTrExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    CtItemTrDTO convert(GenCtItemTrExtDTO dto);

    default GenCtItemTrExtDTO convert(CtHeadTrPO po) {
        GenCtItemTrExtDTO res = new GenCtItemTrExtDTO();
        this.convert(res, po);
        return res;
    }

    List<CtItemTrDTO> convertToDtoList(List<GenCtItemTrExtDTO> req);

    List<GenCtItemTrExtDTO> convertToExtDtoList(List<CtHeadTrPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget GenCtItemTrExtDTO dto, CtHeadTrPO po);

    default Map<String, Object> convertToExtraMap(GenCtItemTrExtDTO dto){
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget GenCtItemTrExtDTO dto, CtHeadTrPO po) {
        this.buildFields(po.getExtra(),dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(GenCtItemTrExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (Objects.nonNull(dto.getExtWqDeliveryData())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqDeliveryData, dto.getExtWqDeliveryData());
        }

        if (Objects.nonNull(dto.getExtWqPriceTerm())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqPriceTerm, dto.getExtWqPriceTerm());
        }

        if (Objects.nonNull(dto.getExtWqCtTotalQty())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqCtTotalQty, dto.getExtWqCtTotalQty());
        }

        if (Objects.nonNull(dto.getExtWqCtTotalAmt())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqCtTotalAmt, dto.getExtWqCtTotalAmt());
        }

        if (Objects.nonNull(dto.getExtWqCtRemark())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqCtRemark, dto.getExtWqCtRemark());
        }

        if (Objects.nonNull(dto.getExtWqOrderItemRef())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqOrderItemRef, dto.getExtWqOrderItemRef());
        }

        if (Objects.nonNull(dto.getExtWqSourcingMetarial())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqSourcingMetarial, dto.getExtWqSourcingMetarial());
        }

        if (Objects.nonNull(dto.getExtWqWarranty())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqWarranty, dto.getExtWqWarranty());
        }

        if (Objects.nonNull(dto.getExtWqAtt())) {
            extra.put(GenCtItemTrExtDTO.Fields.extWqAtt, dto.getExtWqAtt());
        }

        return extra;
    }

    default void buildFields(Map<String, Object> extra, GenCtItemTrExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqDeliveryData))) {
            dto.setExtWqDeliveryData((String) extra.get(GenCtItemTrExtDTO.Fields.extWqDeliveryData));
        }

        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqPriceTerm))) {
            dto.setExtWqPriceTerm((Long) extra.get(GenCtItemTrExtDTO.Fields.extWqPriceTerm));
        }

        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqCtTotalQty))) {
            dto.setExtWqCtTotalQty((BigDecimal) extra.get(GenCtItemTrExtDTO.Fields.extWqCtTotalQty));
        }

        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqCtTotalAmt))) {
            dto.setExtWqCtTotalAmt((BigDecimal) extra.get(GenCtItemTrExtDTO.Fields.extWqCtTotalAmt));
        }

        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqCtRemark))) {
            dto.setExtWqCtRemark((String) extra.get(GenCtItemTrExtDTO.Fields.extWqCtRemark));
        }

        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqOrderItemRef))) {
            dto.setExtWqOrderItemRef((Long) extra.get(GenCtItemTrExtDTO.Fields.extWqOrderItemRef));
        }

        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqSourcingMetarial))) {
            dto.setExtWqSourcingMetarial((Long) extra.get(GenCtItemTrExtDTO.Fields.extWqSourcingMetarial));
        }

        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqWarranty))) {
            dto.setExtWqWarranty((Long) extra.get(GenCtItemTrExtDTO.Fields.extWqWarranty));
        }

        if (Objects.nonNull(extra.get(GenCtItemTrExtDTO.Fields.extWqAtt))) {
            dto.setExtWqAtt((String) extra.get(GenCtItemTrExtDTO.Fields.extWqAtt));
        }

    }
}
