package io.terminus.erp.contract.spi.model.tp.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.erp.md.spi.model.dto.ct.CtHeadTrDTO;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDateTime;
import java.util.List;
/**
 * 协议合同表(GenCtHeadTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-12-27 16:51:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class GenCtHeadTrExtDTO extends CtHeadTrDTO {
    private static final long serialVersionUID = -27471456719914739L;

    @MetaModelField
    @ApiModelProperty("付款协议（魏桥）")
    private Long extWqPayTerm;

    @MetaModelField
    @ApiModelProperty("合同模版（魏桥）")
    private Long extWqCtTpl;

    @ApiModelProperty("合同编制文本（魏桥）")
    private String extWqCtContent;

    @MetaModelField
    @ApiModelProperty("合同编制人（魏桥）")
    private Long extWqCtDrafter;

    @ApiModelProperty("合同来源（魏桥）")
    private String extWqCtSource;

    @MetaModelField
    @ApiModelProperty("合同发起人（魏桥）")
    private Long extWqCtInitiator;

    @ApiModelProperty("电子合同ID（魏桥）")
    private String extWqEsignId;

    @ApiModelProperty("合同付款进度（魏桥）")
    private String extWqCtPayProgresss;

    @ApiModelProperty("合同编制完成时间（魏桥）")
    private LocalDateTime extWqCtDrafteTime;

    @ApiModelProperty("合同来源单据id（魏桥）")
    private Long extWqSourceBillId;

    @ApiModelProperty("合同来源单据名称（魏桥）")
    private String extWqSourceBillCode;

    @ApiModelProperty("合同签约地点（魏桥）")
    private String extWqSignLocation;

    @MetaModelField
    @ApiModelProperty("关联父合同（魏桥）")
    private Long extWqParentContract;

    @ApiModelProperty("关联合同订单（魏桥）")
    private List<GenCtOrderLinkTrDTO> extWqCtOrderRef;

    @MetaModelField
    @ApiModelProperty("合同管理员")
    private Long extWqCtManager;

    @ApiModelProperty("是否多甲方")
    private Boolean extWqCtPartner;

    @ApiModelProperty("合同类型（魏桥）")
    private String extWqCtType;

    @ApiModelProperty("合同编辑驳回原因（魏桥）")
    private String extWqCtEdit;

    @ApiModelProperty("编制合同备注")
    private String extWqDraftRemark;

    @ApiModelProperty("编制合同文件")
    private String extWqDraftFile;

    @ApiModelProperty("合同提交时间（魏桥）")
    private LocalDateTime extWqSubmitTime;

    @ApiModelProperty("编制提取时间（魏桥）")
    private LocalDateTime extWqDraftTime;

    @ApiModelProperty("档案号")
    private String extWqArchiveNo;

    @ApiModelProperty("是否原件")
    private Boolean extWqOriginal;

    @ApiModelProperty("是否已归档")
    private Boolean extWqArchived;

    @ApiModelProperty("权限标识符")
    private String identifier;

}
