package io.terminus.erp.contract.spi.model.tp.req;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ConCtSignImgCf)分页查询请求
 *
 * <AUTHOR> 
 * @since  2024-11-15 16:13:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignImgCfPageReq extends AbstractPageRequest {
    private static final long serialVersionUID = 487506844189963526L;

    @ApiModelProperty("经办人员工")
    private Long operator;

    @ApiModelProperty("采购组织")
    private Long orgPurOrgCfId;

    @ApiModelProperty("实际签署人")
    private Long signEmployee;

    @ApiModelProperty("签名图片")
    private String signImgUrl;

}
