package io.terminus.erp.contract.spi.model.tactics.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class ExtCtHeadTrDTO extends BaseModel {

    @ApiModelProperty("合同编号")
    private String ctCode;

    @ApiModelProperty("合同名称")
    private String ctName;

    @ApiModelProperty("合同类型")
    private String ctTypeName;

    @ApiModelProperty("合同状态")
    private String ctStatus;

    @ApiModelProperty("签约人")
    private String signatory;

    @ApiModelProperty("合同管理员")
    private String extWqCtManager;

    @ApiModelProperty("合同签约甲方")
    private String prtnA;

    @ApiModelProperty("合同签约乙方")
    private String prtnB;

    @ApiModelProperty("含税签约金额")
    @TableField("`ct_tax_amt`")
    private BigDecimal ctTaxAmt;

    @ApiModelProperty("未税签约金额")
    @TableField("`ct_amt`")
    private BigDecimal ctAmt;

    @ApiModelProperty("是否为补充合同")
    private Boolean supplementContract;

    @ApiModelProperty("适用采购组织")
    private String purOrgCodeApplicable;

    @ApiModelProperty("签约组织")
    private String orgName;
}
