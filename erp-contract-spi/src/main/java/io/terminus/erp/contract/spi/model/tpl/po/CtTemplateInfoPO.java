package io.terminus.erp.contract.spi.model.tpl.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同模版数据表(CtTemplateInfo)存储模型
 *
 * <AUTHOR>
 * @since  2023-10-18 15:24:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ct_template_info")
public class CtTemplateInfoPO extends BaseModel {
    private static final long serialVersionUID = -67283694123064523L;

    @ApiModelProperty("合同模版编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("合同模版名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("业务类型")
    @TableField("`biz_type`")
    private Long bizType;

    @ApiModelProperty("合同模版类型")
    @TableField("`template_type`")
    private String templateType;

    @ApiModelProperty("状态")
    @TableField("`state`")
    private String state;

    @ApiModelProperty("描述")
    @TableField("`desc`")
    private String desc;

    @ApiModelProperty("合同模版富文本")
    @TableField("`template_info`")
    private String templateInfo;

}
