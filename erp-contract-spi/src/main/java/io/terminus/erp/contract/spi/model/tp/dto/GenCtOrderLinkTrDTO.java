package io.terminus.erp.contract.spi.model.tp.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.erp.md.spi.model.po.ct.CtHeadTrPO;
import io.terminus.erp.purchase.spi.model.po.dto.PurPoHeadTrDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (GenCtOrderLinkTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-18 15:21:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GenCtOrderLinkTrDTO extends BaseModel {
    private static final long serialVersionUID = -74389260958850880L;

    @MetaModelField
    @ApiModelProperty("关联合同表")
    private CtHeadTrPO docRef;

    @MetaModelField
    @ApiModelProperty("关联订单表")
    private PurPoHeadTrDTO orderRef;

}
