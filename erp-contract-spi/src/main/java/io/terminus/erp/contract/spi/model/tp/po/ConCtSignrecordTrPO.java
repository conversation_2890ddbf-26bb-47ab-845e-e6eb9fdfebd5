package io.terminus.erp.contract.spi.model.tp.po;


import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署记录(ConCtSignrecordTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_signrecord_tr")
public class ConCtSignrecordTrPO extends BaseModel {
    private static final long serialVersionUID = 508036724461340947L;

    @ApiModelProperty("关联签署任务")
    @TableField("`sign_task`")
    private Long signTask;

    @ApiModelProperty("签署方")
    @TableField("`signatory`")
    private Long signatory;

    @ApiModelProperty("签署时间")
    @TableField("`sign_time`")
    private LocalDateTime signTime;

    @ApiModelProperty("签署员工")
    @TableField("`sing_emp`")
    private Long singEmp;

    @ApiModelProperty("签署人")
    @TableField("`sing_user`")
    private Long singUser;

    @ApiModelProperty("待签署文件")
    @TableField("`sign_file`")
    private Long signFile;

    @ApiModelProperty("电子签名")
    @TableField("`esignature`")
    private Long esignature;

    @ApiModelProperty("已签署文件")
    @TableField("`signed_file`")
    private String signedFile;

}
