package io.terminus.erp.contract.spi.model.tactics.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.BaseModel;
import io.terminus.erp.contract.spi.model.tactics.po.ContractDistributionConfigurationHeaderPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = true)
public class ContractDistributionConfigurationHeaderDTO extends BaseModel {

    @ApiModelProperty("行政组织")
    private Long administrativeOrganization;

    @ApiModelProperty("描述信息")
    private String desc;

    @ApiModelProperty("行信息")
    private List<ContractDistributionRuleRowDTO> distributionRuleRow;

    /**
     * 检查数据
     */
    public void checkData() {
        Set<Long> purchasingOrganizationSet = new HashSet<>();
        if (CollectionUtils.isEmpty(distributionRuleRow)) {
            throw new BusinessException("合同派单展示规则行数据不能为空");
        }

        for (ContractDistributionRuleRowDTO contractDistributionRuleRowDTO : distributionRuleRow) {
            if (purchasingOrganizationSet.contains(contractDistributionRuleRowDTO.getPurchasingOrganization())) {
                throw new BusinessException("采购组织重复");
            }
            purchasingOrganizationSet.add(contractDistributionRuleRowDTO.getPurchasingOrganization());
        }
    }

}
