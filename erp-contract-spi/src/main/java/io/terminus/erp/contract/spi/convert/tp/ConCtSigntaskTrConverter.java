package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSigntaskTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSigntaskTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * 签署任务(ConCtSigntaskTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-08 16:42:16
 */
@Mapper(componentModel = "spring")
public interface ConCtSigntaskTrConverter {

    ConCtSigntaskTrDTO po2Dto(ConCtSigntaskTrPO req);

    List<ConCtSigntaskTrDTO> po2DtoList(List<ConCtSigntaskTrPO> poList);

    ConCtSigntaskTrPO dto2Po(ConCtSigntaskTrDTO req);

    List<ConCtSigntaskTrPO> dto2PoList(List<ConCtSigntaskTrDTO> dtoList);
}
