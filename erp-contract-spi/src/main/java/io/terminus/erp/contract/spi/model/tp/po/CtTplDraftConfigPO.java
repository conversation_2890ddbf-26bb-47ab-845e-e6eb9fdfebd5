package io.terminus.erp.contract.spi.model.tp.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (CtTplDraftConfig)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-07 19:39:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ct_tpl_draft_config")
public class CtTplDraftConfigPO extends BaseModel {
    private static final long serialVersionUID = -40106579978026118L;

    @ApiModelProperty("占位符")
    @TableField("`place_holder`")
    private String placeHolder;

    @ApiModelProperty("占位符名称")
    @TableField("`place_holder_name`")
    private String placeHolderName;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("值")
    @TableField("`value`")
    private String value;

}
