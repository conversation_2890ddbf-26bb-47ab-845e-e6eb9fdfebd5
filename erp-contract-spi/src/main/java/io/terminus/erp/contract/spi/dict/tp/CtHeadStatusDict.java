package io.terminus.erp.contract.spi.dict.tp;

/**
 * 合同状态
 *
 * @className: CtHeadStatusDict
 * @author: charl
 * @date: 2023/8/10 11:44
 */
public interface CtHeadStatusDict {

    /**
     * 已创建
     */
    String DRAFT = "DRAFT";
    /**
     * 已提交
     */
    String SUBMITTED = "SUBMITTED";

    /**
     * 已提交待编制
     */
    String SUB_DRAFT = "SUB_DRAFT";

    /**
     * 已失效
     */
    String EXPIRED = "EXPIRED";

    /**
     * 已归档
     */
    String ARCHIVED = "ARCHIVED";

    /**
     * 签署中
     */
    String SIGNING = "SIGNING";

    /**
     * 已签署
     */
    String SIGNED = "SIGNED";

    /**
     * 已拒绝
     */
    String REJECTED = "REJECTED";

    /**
     * 编制中
     */
    String DRAFTING = "DRAFTING";

    /**
     * 签署确认
     */
    String SIGN_CONFORM = "SIGN_CONFORM";

    /**
     * 待签署
     */
    String AWAITING = "AWAITING";

    /**
     * 已生效
     */
    String ENABLED = "ENABLED";

    /**
     * 已作废
     */
    String CANCELLED = "CANCELLED";


}
