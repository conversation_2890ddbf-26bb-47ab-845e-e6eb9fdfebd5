package io.terminus.erp.contract.spi.model.tp.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同签署渠道类型(ConCtSignchannalCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-08-07 16:27:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignchannalCfDTO extends BaseModel {
    private static final long serialVersionUID = 869191661600545825L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("路由标识")
    private String routeKey;

}
