package io.terminus.erp.contract.spi.model.tp.so;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同和签署方式入参(ConContractSignway)搜索模型
 *
 * <AUTHOR>
 * @since  2023-08-08 15:02:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConContractSignwaySO extends BaseModel {
    private static final long serialVersionUID = 121034649386667139L;

    @ApiModelProperty("合同id")
    private String contract;

    @ApiModelProperty("签署方式id")
    private String signWay;

}
