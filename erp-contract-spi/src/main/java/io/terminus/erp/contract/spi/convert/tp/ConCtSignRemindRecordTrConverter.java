package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSignRemindRecordTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignRemindRecordTrDTO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * 签署提醒记录(ConCtSignRemindRecordTr)结构映射器
 *
 * <AUTHOR>
 * @since  2024-01-01 00:00:00
 */
@Mapper(componentModel = "spring")
public interface ConCtSignRemindRecordTrConverter {

    ConCtSignRemindRecordTrDTO po2Dto(ConCtSignRemindRecordTrPO req);

    List<ConCtSignRemindRecordTrDTO> po2DtoList(List<ConCtSignRemindRecordTrPO> poList);

    ConCtSignRemindRecordTrPO dto2Po(ConCtSignRemindRecordTrDTO req);

    List<ConCtSignRemindRecordTrPO> dto2PoList(List<ConCtSignRemindRecordTrDTO> dtoList);
} 