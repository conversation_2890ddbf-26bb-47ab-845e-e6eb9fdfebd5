package io.terminus.erp.contract.spi.convert.tpl;

import io.terminus.erp.contract.spi.model.tpl.po.CtTplTypeCfPO;
import io.terminus.erp.contract.spi.model.tpl.dto.CtTplTypeCfDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * 合同模版业务类型配置表(CtTplTypeCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-10-17 19:39:55
 */
@Mapper(componentModel = "spring")
public interface CtTplTypeCfConverter {

    CtTplTypeCfDTO po2Dto(CtTplTypeCfPO req);

    List<CtTplTypeCfDTO> po2DtoList(List<CtTplTypeCfPO> poList);

    CtTplTypeCfPO dto2Po(CtTplTypeCfDTO req);

    List<CtTplTypeCfPO> dto2PoList(List<CtTplTypeCfDTO> dtoList);
}
