package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConSignatoryAccountUsedPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConSignatoryAccountUsedDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * 签章账户使用明细表(ConSignatoryAccountUsed)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-08 13:49:01
 */
@Mapper(componentModel = "spring")
public interface ConSignatoryAccountUsedConverter {

    ConSignatoryAccountUsedDTO po2Dto(ConSignatoryAccountUsedPO req);

    List<ConSignatoryAccountUsedDTO> po2DtoList(List<ConSignatoryAccountUsedPO> poList);

    ConSignatoryAccountUsedPO dto2Po(ConSignatoryAccountUsedDTO req);

    List<ConSignatoryAccountUsedPO> dto2PoList(List<ConSignatoryAccountUsedDTO> dtoList);
}
