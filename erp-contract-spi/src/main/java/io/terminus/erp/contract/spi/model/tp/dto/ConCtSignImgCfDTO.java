package io.terminus.erp.contract.spi.model.tp.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ConCtSignImgCf)传输模型
 *
 * <AUTHOR> 
 * @since  2024-11-15 16:13:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignImgCfDTO extends BaseModel {
    private static final long serialVersionUID = -40067119171376432L;

    @MetaModelField
    @ApiModelProperty("经办人员工")
    private Long operator;

    @MetaModelField
    @ApiModelProperty("采购组织")
    private Long orgPurOrgCfId;

    @MetaModelField
    @ApiModelProperty("实际签署人")
    private Long signEmployee;

    @ApiModelProperty("签名图片")
    private String signImgUrl;

}
