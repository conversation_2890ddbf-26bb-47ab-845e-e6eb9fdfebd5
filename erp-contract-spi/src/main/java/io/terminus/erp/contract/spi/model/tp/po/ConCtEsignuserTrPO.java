package io.terminus.erp.contract.spi.model.tp.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电子签名-授权用户关联表(ConCtEsignuserTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_esignuser_tr")
public class ConCtEsignuserTrPO extends BaseModel {
    private static final long serialVersionUID = 567019785680631500L;

    @ApiModelProperty("电子签名")
    @TableField("`e_sign`")
    private Long eSign;

    @ApiModelProperty("授权用户")
    @TableField("`authorized_user`")
    private Long authorizedUser;

}
