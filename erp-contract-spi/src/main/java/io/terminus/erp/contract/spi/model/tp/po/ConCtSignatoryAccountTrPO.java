package io.terminus.erp.contract.spi.model.tp.po;


import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.util.ArrayList;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ConCtSignatoryAccountTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-08 14:46:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_signatory_account_tr")
public class ConCtSignatoryAccountTrPO extends BaseModel {
    private static final long serialVersionUID = -27416697334643165L;

    @ApiModelProperty("账户状态")
    @TableField("`signatory_account_status`")
    private String signatoryAccountStatus;

    @ApiModelProperty("账户余额（元）")
    @TableField("`signatory_account_amount`")
    private BigDecimal signatoryAccountAmount;

    @ApiModelProperty("账户已使用金额（元）")
    @TableField("`signatory_account_amount_used`")
    private BigDecimal signatoryAccountAmountUsed;

    @ApiModelProperty("账户上次充值时间")
    @TableField("`signatory_amount_time_news`")
    private LocalTime signatoryAmountTimeNews;

    @ApiModelProperty("余额生效日期")
    @TableField("`signatory_account_effect_time`")
    private LocalDateTime signatoryAccountEffectTime;

    @ApiModelProperty("余额失效日期")
    @TableField("`signatory_account_lose_time`")
    private LocalDateTime signatoryAccountLoseTime;

    @ApiModelProperty("关联公司")
    @TableField("`rel_company`")
    private Long relCompany;

    @ApiModelProperty("账户编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("关联电子签名")
    @TableField("`rel_esign`")
    private Long relEsign;

    @ApiModelProperty("账户类型")
    @TableField("`type`")
    private String type;

}
