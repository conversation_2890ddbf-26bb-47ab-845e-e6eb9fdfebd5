package io.terminus.erp.contract.spi.model.tp.so;


import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignrecordTrDTO;
import java.time.LocalDateTime;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署方(ConCtSignatoryTr)搜索模型
 *
 * <AUTHOR>
 * @since  2023-08-11 10:54:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignatoryTrSO extends BaseModel {
    private static final long serialVersionUID = -30640311132710173L;

    @ApiModelProperty("关联签署任务")
    private Long signTask;

    @ApiModelProperty("签署方")
    private Long signatory;

    @ApiModelProperty("签署顺序")
    private Integer sortNum;

    @ApiModelProperty("签署状态")
    private String signatoryStatus;

    @ApiModelProperty("签署时间")
    private LocalDateTime signTime;

    @ApiModelProperty("签署记录")
    private List<ConCtSignrecordTrDTO> signRecords;

    @ApiModelProperty("签署页面url")
    private String signUrl;

    @ApiModelProperty("关联电子签名")
    private Long eSign;

}
