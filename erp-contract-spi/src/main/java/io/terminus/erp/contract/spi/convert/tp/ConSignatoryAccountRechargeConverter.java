package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConSignatoryAccountRechargePO;
import io.terminus.erp.contract.spi.model.tp.dto.ConSignatoryAccountRechargeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (ConSignatoryAccountRecharge)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-07 13:32:30
 */
@Mapper(componentModel = "spring")
public interface ConSignatoryAccountRechargeConverter {

    ConSignatoryAccountRechargeDTO po2Dto(ConSignatoryAccountRechargePO req);

    List<ConSignatoryAccountRechargeDTO> po2DtoList(List<ConSignatoryAccountRechargePO> poList);

    ConSignatoryAccountRechargePO dto2Po(ConSignatoryAccountRechargeDTO req);

    List<ConSignatoryAccountRechargePO> dto2PoList(List<ConSignatoryAccountRechargeDTO> dtoList);
}
