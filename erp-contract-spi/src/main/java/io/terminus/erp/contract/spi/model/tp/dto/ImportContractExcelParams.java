package io.terminus.erp.contract.spi.model.tp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ImportContractExcelParams {

    @ExcelProperty("物料编码")
    private String matCode;

    @ExcelProperty("税率(%)")
    private String tax;

    @ExcelProperty("含税单价(元)")
    private BigDecimal amt;

    @ExcelProperty("数量")
    private BigDecimal qty;

    @ExcelProperty("交货期(天)")
    private BigDecimal dateDelivery;

    @ExcelProperty("质保期(月)")
    private BigDecimal guaranteePeriod;

    @ExcelProperty("备注")
    private String remark;

}
