package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tactics.dto.ContractDistributionConfigurationHeaderDTO;
import io.terminus.erp.contract.spi.model.tactics.dto.ContractDistributionRuleRowDTO;
import io.terminus.erp.contract.spi.model.tactics.po.ContractDistributionConfigurationHeaderPO;
import io.terminus.erp.contract.spi.model.tactics.po.ContractDistributionRuleRowPO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ContractDistributionConfigurationHeaderConverter {

    ContractDistributionConfigurationHeaderPO convert(ContractDistributionConfigurationHeaderDTO contractDistributionConfigurationHeaderDTO);

    ContractDistributionConfigurationHeaderDTO convert(ContractDistributionConfigurationHeaderPO contractDistributionConfigurationHeaderPO);

    ContractDistributionRuleRowDTO convert(ContractDistributionRuleRowPO contractDistributionRuleRowPO);

    ContractDistributionRuleRowPO convert(ContractDistributionRuleRowDTO contractDistributionRuleRowDTO);
}
