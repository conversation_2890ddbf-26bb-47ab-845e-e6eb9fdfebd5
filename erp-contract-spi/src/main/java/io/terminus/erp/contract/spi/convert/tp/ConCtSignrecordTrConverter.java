package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSignrecordTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignrecordTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 签署记录(ConCtSignrecordTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@Mapper(componentModel = "spring")
public interface ConCtSignrecordTrConverter {

    ConCtSignrecordTrDTO po2Dto(ConCtSignrecordTrPO req);

    List<ConCtSignrecordTrDTO> po2DtoList(List<ConCtSignrecordTrPO> poList);

    ConCtSignrecordTrPO dto2Po(ConCtSignrecordTrDTO req);

    List<ConCtSignrecordTrPO> dto2PoList(List<ConCtSignrecordTrDTO> dtoList);
}
