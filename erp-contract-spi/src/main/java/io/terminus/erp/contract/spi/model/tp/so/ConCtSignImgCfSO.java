package io.terminus.erp.contract.spi.model.tp.so;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ConCtSignImgCf)搜索模型
 *
 * <AUTHOR> 
 * @since  2024-11-15 16:13:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignImgCfSO extends BaseModel {
    private static final long serialVersionUID = 871095717501825516L;

    @ApiModelProperty("经办人员工")
    private Long operator;

    @ApiModelProperty("采购组织")
    private Long orgPurOrgCfId;

    @ApiModelProperty("实际签署人")
    private Long signEmployee;

    @ApiModelProperty("签名图片")
    private String signImgUrl;

}
