package io.terminus.erp.contract.spi.model.role.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import io.terminus.wq.md.spi.model.org.dto.OrgAdmOrgCfExtDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * console引用服务：con_org_role_tr
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConOrgRoleTrDTO extends BaseModel {

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("描述信息")
    private String desc;

    @ApiModelProperty("权限行ID")
    private List<ConRoleItemTrDTO> roleLine;

}
