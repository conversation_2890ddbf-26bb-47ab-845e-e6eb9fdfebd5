package io.terminus.erp.contract.spi.model.tp.req;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电子签名-授权用户关联表(ConCtEsignuserTr)创建请求
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtEsignuserTrCreateReq extends AbstractRequest {
    private static final long serialVersionUID = -31572684056881843L;

    @ApiModelProperty("电子签名")
    private Long eSign;

    @ApiModelProperty("授权用户")
    private Long authorizedUser;

}
