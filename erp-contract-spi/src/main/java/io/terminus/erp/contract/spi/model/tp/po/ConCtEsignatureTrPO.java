package io.terminus.erp.contract.spi.model.tp.po;


import java.time.LocalDateTime;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电子签名(ConCtEsignatureTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-10-15 17:05:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_esignature_tr")
public class ConCtEsignatureTrPO extends BaseModel {
    private static final long serialVersionUID = -24532003056138519L;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("电子签名状态")
    @TableField("`esignature_status`")
    private String esignatureStatus;

    @ApiModelProperty("签名主体")
    @TableField("`entity`")
    private Long entity;

    @ApiModelProperty("企业名称")
    @TableField("`company_name`")
    private String companyName;

    @ApiModelProperty("统一社会信用代码")
    @TableField("`register_social_credit_code`")
    private String registerSocialCreditCode;

    @ApiModelProperty("营业执照扫描件")
    @TableField("`business_license_scan_attachment`")
    private String businessLicenseScanAttachment;

    @ApiModelProperty("银行名称")
    @TableField("`bank_name`")
    private String bankName;

    @ApiModelProperty("银行账号")
    @TableField("`bank_account`")
    private String bankAccount;

    @ApiModelProperty("法人姓名")
    @TableField("`legal_person_mame`")
    private String legalPersonMame;

    @ApiModelProperty("法人证件号")
    @TableField("`legal_person_license_number`")
    private String legalPersonLicenseNumber;

    @ApiModelProperty("法人联系方式")
    @TableField("`legal_person_contact_details`")
    private String legalPersonContactDetails;

    @ApiModelProperty("法人证件")
    @TableField("`legal_person_license_attachment`")
    private String legalPersonLicenseAttachment;

    @ApiModelProperty("代理人姓名")
    @TableField("`agent_name`")
    private String agentName;

    @ApiModelProperty("代理人证件号")
    @TableField("`agent_license_number`")
    private String agentLicenseNumber;

    @ApiModelProperty("代理人联系方式")
    @TableField("`agent_contact_details`")
    private String agentContactDetails;

    @ApiModelProperty("代理人证件附件")
    @TableField("`agent_license`")
    private String agentLicense;

    @ApiModelProperty("证书机构")
    @TableField("`certificate_authority`")
    private Long certificateAuthority;

    @ApiModelProperty("证书密钥文件")
    @TableField("`certificate_attachment`")
    private String certificateAttachment;

    @ApiModelProperty("申请时间")
    @TableField("`apply_date`")
    private LocalDateTime applyDate;

    @ApiModelProperty("印章文件（描述签名的印章文件）")
    @TableField("`seal_attachment`")
    private String sealAttachment;

    @ApiModelProperty("认证流程id")
    @TableField("`auth_flow_id`")
    private String authFlowId;

    @ApiModelProperty("机构认证授权链接")
    @TableField("`auth_url`")
    private String authUrl;

    @ApiModelProperty("机构认证认证后的ID")
    @TableField("`org_id`")
    private String orgId;

    @ApiModelProperty("机构认证认证后的psnId")
    @TableField("`psn_id`")
    private String psnId;

    @ApiModelProperty("备注")
    @TableField("`remark`")
    private String remark;

    @ApiModelProperty("企业类型")
    @TableField("`enterprice_type`")
    private String enterpriceType;

    @ApiModelProperty("邮件接收人")
    @TableField("`mail_receiver`")
    private String mailReceiver;

    @ApiModelProperty("邮件接收人电话")
    @TableField("`mail_receiver_contract`")
    private String mailReceiverContract;

    @ApiModelProperty("邮件接收地址")
    @TableField("`mail_receive_addr`")
    private String mailReceiveAddr;

    @ApiModelProperty("认证打款金额")
    @TableField("`auth_pay_amt`")
    private BigDecimal authPayAmt;

    @ApiModelProperty("认证日期")
    @TableField("`auth_time`")
    private LocalDateTime authTime;

    @ApiModelProperty("签署方式")
    @TableField("`sign_way`")
    private String signWay;

    @ApiModelProperty("签署说明")
    @TableField("`sign_desc`")
    private String signDesc;

    @ApiModelProperty("组织认证承诺书")
    @TableField("`auth_promise_file`")
    private String authPromiseFile;

    @ApiModelProperty("关联签署账户")
    @TableField("`rel_sign_account`")
    private Long relSignAccount;

    @ApiModelProperty("联行号")
    @TableField("`bank_link_code`")
    private String bankLinkCode;

    @ApiModelProperty("关联大款银行")
    @TableField("`bank_ref`")
    private String bankRef;

}
