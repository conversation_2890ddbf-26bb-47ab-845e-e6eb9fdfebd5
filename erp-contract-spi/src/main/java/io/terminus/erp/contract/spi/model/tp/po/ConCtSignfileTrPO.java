package io.terminus.erp.contract.spi.model.tp.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署文件(ConCtSignfileTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_signfile_tr")
public class ConCtSignfileTrPO extends BaseModel {
    private static final long serialVersionUID = 725800175129199908L;

    @ApiModelProperty("文件名")
    @TableField("`file_name`")
    private String fileName;

    /**
     * 原始文件
     */
    @ApiModelProperty("签署文件（只支持PDF）")
    @TableField("`sign_file`")
    private String signFile;

    /**
     * 采购商签署文件
     */
    @ApiModelProperty("已签署文件（只支持PDF）")
    @TableField("`signed_file`")
    private String signedFile;

    /**
     * 供应商签署文件
     */
    @ApiModelProperty("供应商签署文件")
    @TableField("`signing_file`")
    private String signingFile;


    @ApiModelProperty("关联签署任务")
    @TableField("`sign_task`")
    private Long signTask;

    @ApiModelProperty("关联三方签署平台文件id")
    @TableField("`third_file_id`")
    private String thirdFileId;

    @ApiModelProperty("盖章定位方式")
    @TableField("`stamp_positioning_method`")
    private String stampPositioningMethod;

}
