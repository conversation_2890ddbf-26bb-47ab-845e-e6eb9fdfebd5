package io.terminus.erp.contract.spi.model.tp.po;


import java.time.LocalDateTime;
import java.util.ArrayList;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署任务(ConCtSigntaskTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-08 16:42:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_signtask_tr")
public class ConCtSigntaskTrPO extends BaseModel {
    private static final long serialVersionUID = -85537908478700555L;

    @ApiModelProperty("任务编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("任务标题")
    @TableField("`title`")
    private String title;

    @ApiModelProperty("签署状态")
    @TableField("`sign_status`")
    private String signStatus;

    @ApiModelProperty("签署方式")
    @TableField("`sign_way`")
    private Long signWay;

    @ApiModelProperty("所属公司")
    @TableField("`company`")
    private Long company;

    @ApiModelProperty("签署单据ID")
    @TableField("`sign_bill_id`")
    private Long signBillId;

    @ApiModelProperty("签署单据编码")
    @TableField("`sign_bill_code`")
    private String signBillCode;

    @ApiModelProperty("发起人")
    @TableField("`sponsor`")
    private Long sponsor;

    @ApiModelProperty("签署完成时间")
    @TableField("`finish_time`")
    private LocalDateTime finishTime;

    @ApiModelProperty("三方签署任务ID")
    @TableField("`sign_flow_id`")
    private String signFlowId;

    @ApiModelProperty("签署单据类型")
    @TableField("`sign_bill_type`")
    private String signBillType;

    @ApiModelProperty("采购组织")
    @TableField("`org_ref`")
    private Long orgRef;

    @ApiModelProperty("签约组织")
    @TableField("`org_rule_id`")
    private Long orgRuleId;

}
