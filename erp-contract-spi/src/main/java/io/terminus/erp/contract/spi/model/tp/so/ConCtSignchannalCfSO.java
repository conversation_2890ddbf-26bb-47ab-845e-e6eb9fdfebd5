package io.terminus.erp.contract.spi.model.tp.so;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同签署渠道类型(ConCtSignchannalCf)搜索模型
 *
 * <AUTHOR>
 * @since  2023-08-07 16:27:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignchannalCfSO extends BaseModel {
    private static final long serialVersionUID = 904014776488047552L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("路由标识")
    private String routeKey;

}
