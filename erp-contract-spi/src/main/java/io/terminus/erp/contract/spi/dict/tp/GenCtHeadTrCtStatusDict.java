package io.terminus.erp.contract.spi.dict.tp;

/**
 * 合同状态(GenCtHeadTrCtStatus)字典
 *
 * <AUTHOR>
 * @since  2023-10-19 09:51:16
 */
@Deprecated
public interface GenCtHeadTrCtStatusDict {

    /**
     * 已创建
     */
    String DRAFT = "DRAFT";
    /**
     * 已提交
     */
    String SUBMITTED = "SUBMITTED";

    /**
     * 已提交待编制
     */
    String SUB_DRAFT = "SUB_DRAFT";

    /**
     * 已失效
     */
    String EXPIRED = "EXPIRED";
    /**
     * 已归档
     */
    String ARCHIVED = "ARCHIVED";
    /**
     * 签署中
     */
    String SIGNING = "SIGNING";
    /**
     * 已签署
     */
    String SIGNED = "SIGNED";
    /**
     * 已拒绝
     */
    String REJECTED = "REJECTED";
    /**
     * 编制中
     */
    String DRAFTING = "DRAFTING";
    /**
     * 待签署
     */
    String AWAITING = "AWAITING";

    /**
     * 已生效
     */
    String ENABLED = "ENABLED";

}
