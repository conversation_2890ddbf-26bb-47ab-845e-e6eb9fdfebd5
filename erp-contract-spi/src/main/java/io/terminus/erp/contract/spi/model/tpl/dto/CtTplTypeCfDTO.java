package io.terminus.erp.contract.spi.model.tpl.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 合同模版业务类型配置表(CtTplTypeCf)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-17 19:39:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CtTplTypeCfDTO extends BaseModel {
    private static final long serialVersionUID = 203001003225271960L;

    @ApiModelProperty("类型编码")
    private String code;

    @ApiModelProperty("类型名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

}
