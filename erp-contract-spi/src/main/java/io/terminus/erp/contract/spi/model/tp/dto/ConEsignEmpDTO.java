package io.terminus.erp.contract.spi.model.tp.dto;


import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 电子签章授权用户(ConEsignEmp)传输模型
 *
 * <AUTHOR>
 * @since  2023-08-03 19:02:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConEsignEmpDTO extends BaseModel {
    private static final long serialVersionUID = 640300856641091208L;

    @ApiModelProperty("电子签章")
    private String esignature;

    @ApiModelProperty("授权用户")
    private List<OrgEmployeeMdDTO> authorizedUsers;

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class OrgEmployeeMdDTO {

        private Long  id;
    }

}
