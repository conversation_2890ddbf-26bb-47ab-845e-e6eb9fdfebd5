package io.terminus.erp.contract.spi.model.tp.dto;


import java.util.ArrayList;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.erp.md.spi.model.dto.ct.GenCtPartnerLinkTrDTO;
import lombok.experimental.FieldNameConstants;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 合同相关方关联表(GenCtPartnerLinkTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-12-27 17:30:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class GenCtPartnerLinkTrExtDTO extends GenCtPartnerLinkTrDTO {
    private static final long serialVersionUID = 947414695657363683L;

    @MetaModelField
    @ApiModelProperty("相关企业")
    private Long extWqRelCompany;

}
