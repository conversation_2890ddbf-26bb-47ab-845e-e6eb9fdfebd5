package io.terminus.erp.contract.spi.model.tp.dto;

import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署提醒记录(ConCtSignRemindRecordTr)传输模型
 *
 * <AUTHOR>
 * @since  2024-01-01 00:00:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignRemindRecordTrDTO extends BaseModel {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("合同ID")
    private Long contractId;

    @ApiModelProperty("签署任务ID")
    private Long signTaskId;

    @ApiModelProperty("签署方ID")
    private Long signatoryId;

    @ApiModelProperty("短信发送时间")
    private LocalDateTime smsSendTime;

    @ApiModelProperty("短信内容")
    private String smsContent;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("提醒类型：1-每2天提醒，2-每天提醒")
    private Integer remindType;

    @ApiModelProperty("甲方签署时间")
    private LocalDateTime partyASignTime;

    @ApiModelProperty("距离甲方签署天数")
    private Integer daysSincePartyASign;
} 