package io.terminus.erp.contract.spi.model.tp.dto;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 签章账户使用明细表(ConSignatoryAccountUsed)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-08 13:49:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConSignatoryAccountUsedDTO extends BaseModel {
    private static final long serialVersionUID = -92211356704918433L;

    @MetaModelField
    @ApiModelProperty("关联签名账户")
    private Long relSignAccount;

    @ApiModelProperty("关联单据id")
    private Long relBillId;

    @ApiModelProperty("扣款金额（元）")
    private BigDecimal deductionAmt;

    @ApiModelProperty("扣款时间")
    private LocalDateTime deductionTime;

    @ApiModelProperty("关联单据类型")
    private String relBillType;

    @ApiModelProperty("签署方式")
    private String signType;

}
