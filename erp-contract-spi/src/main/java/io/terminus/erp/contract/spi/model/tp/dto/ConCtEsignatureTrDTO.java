package io.terminus.erp.contract.spi.model.tp.dto;


import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import java.time.LocalDateTime;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 电子签名(ConCtEsignatureTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-15 17:05:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtEsignatureTrDTO extends BaseModel {
    private static final long serialVersionUID = 948646768652370276L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("电子签名状态")
    private String esignatureStatus;

    @MetaModelField
    @ApiModelProperty("签名主体")
    private GenComTypeCfDTO entity;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("统一社会信用代码")
    private String registerSocialCreditCode;

    @ApiModelProperty("营业执照扫描件")
    private String businessLicenseScanAttachment;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账号")
    private String bankAccount;

    @ApiModelProperty("法人姓名")
    private String legalPersonMame;

    @ApiModelProperty("法人证件号")
    private String legalPersonLicenseNumber;

    @ApiModelProperty("法人联系方式")
    private String legalPersonContactDetails;

    @ApiModelProperty("法人证件")
    private String legalPersonLicenseAttachment;

    @ApiModelProperty("代理人姓名")
    private String agentName;

    @ApiModelProperty("代理人证件号")
    private String agentLicenseNumber;

    @ApiModelProperty("代理人联系方式")
    private String agentContactDetails;

    @ApiModelProperty("代理人证件附件")
    private String agentLicense;

    @MetaModelField
    @ApiModelProperty("证书机构")
    private ConCtSignchannalCfDTO certificateAuthority;

    @ApiModelProperty("证书密钥文件")
    private String certificateAttachment;

    @ApiModelProperty("申请时间")
    private LocalDateTime applyDate;

    @ApiModelProperty("印章文件（描述签名的印章文件）")
    private String sealAttachment;

    @ApiModelProperty("认证流程id")
    private String authFlowId;

    @ApiModelProperty("机构认证授权链接")
    private String authUrl;

    @ApiModelProperty("机构认证认证后的ID")
    private String orgId;

    @ApiModelProperty("机构认证认证后的psnId")
    private String psnId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("企业类型")
    private String enterpriceType;

    @ApiModelProperty("邮件接收人")
    private String mailReceiver;

    @ApiModelProperty("邮件接收人电话")
    private String mailReceiverContract;

    @ApiModelProperty("邮件接收地址")
    private String mailReceiveAddr;

    @ApiModelProperty("认证打款金额")
    private BigDecimal authPayAmt;

    @ApiModelProperty("认证日期")
    private LocalDateTime authTime;

    @ApiModelProperty("签署方式")
    private String signWay;

    @ApiModelProperty("签署说明")
    private String signDesc;

    @ApiModelProperty("组织认证承诺书")
    private String authPromiseFile;

    @MetaModelField
    @ApiModelProperty("关联签署账户")
    private ConCtSignatoryAccountTrDTO relSignAccount;

    @ApiModelProperty("联行号")
    private String bankLinkCode;

    @ApiModelProperty("关联打款银行")
    private ConCtSignBankInfoCfDTO bankRef;


}
