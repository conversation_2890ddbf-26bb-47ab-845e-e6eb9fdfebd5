package io.terminus.erp.contract.spi.model.tp.so;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同签署类型配置(ConCtSigntypeCf)搜索模型
 *
 * <AUTHOR>
 * @since  2023-08-04 17:12:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSigntypeCfSO extends BaseModel {
    private static final long serialVersionUID = -61508126985583414L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("启用甲方签署")
    private Boolean partyASign;

    @ApiModelProperty("启用乙方签署")
    private Boolean partyBSign;

    @ApiModelProperty("签署顺序：开启甲方先签，关闭乙方先签")
    private Boolean partyAFirst;

}
