package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSignTypeConfPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignTypeConfDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * 签署类型配置(ConCtSignTypeConf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-05 20:01:16
 */
@Mapper(componentModel = "spring")
public interface ConCtSignTypeConfConverter {

    ConCtSignTypeConfDTO po2Dto(ConCtSignTypeConfPO req);

    List<ConCtSignTypeConfDTO> po2DtoList(List<ConCtSignTypeConfPO> poList);

    ConCtSignTypeConfPO dto2Po(ConCtSignTypeConfDTO req);

    List<ConCtSignTypeConfPO> dto2PoList(List<ConCtSignTypeConfDTO> dtoList);
}
