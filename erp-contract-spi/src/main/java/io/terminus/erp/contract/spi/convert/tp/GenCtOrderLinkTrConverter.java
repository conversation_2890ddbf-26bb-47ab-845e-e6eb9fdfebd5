package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.GenCtOrderLinkTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtOrderLinkTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (GenCtOrderLinkTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-10-18 15:21:30
 */
@Mapper(componentModel = "spring")
public interface GenCtOrderLinkTrConverter {

    @Mapping(target = "docRef.id", source = "docRef")
    @Mapping(target = "orderRef.id", source = "orderRef")
    GenCtOrderLinkTrDTO po2Dto(GenCtOrderLinkTrPO req);

    List<GenCtOrderLinkTrDTO> po2DtoList(List<GenCtOrderLinkTrPO> poList);

    @Mapping(target = "docRef", source = "docRef.id")
    @Mapping(target = "orderRef", source = "orderRef.id")
    GenCtOrderLinkTrPO dto2Po(GenCtOrderLinkTrDTO req);

    List<GenCtOrderLinkTrPO> dto2PoList(List<GenCtOrderLinkTrDTO> dtoList);
}
