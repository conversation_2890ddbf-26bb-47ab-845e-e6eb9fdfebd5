package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.CtTplDraftConfigPO;
import io.terminus.erp.contract.spi.model.tp.dto.CtTplDraftConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (CtTplDraftConfig)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-07 19:39:44
 */
@Mapper(componentModel = "spring")
public interface CtTplDraftConfigConverter {

    CtTplDraftConfigDTO po2Dto(CtTplDraftConfigPO req);

    List<CtTplDraftConfigDTO> po2DtoList(List<CtTplDraftConfigPO> poList);

    CtTplDraftConfigPO dto2Po(CtTplDraftConfigDTO req);

    List<CtTplDraftConfigPO> dto2PoList(List<CtTplDraftConfigDTO> dtoList);
}
