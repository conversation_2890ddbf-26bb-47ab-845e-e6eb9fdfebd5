package io.terminus.erp.contract.spi.model.tactics.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ContractDistributionRuleRowDTO extends BaseModel {

    @ApiModelProperty("采购组织")
    private Long purchasingOrganization;

    @ApiModelProperty("业务类型")
    private List<String> bizType;

    @ApiModelProperty("描述信息")
    private String desc;

    @ApiModelProperty("关联投标")
    private Long distributionConfigurationHeader;

}
