package io.terminus.erp.contract.spi.model.tp.so;


import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电子签名(ConCtEsignatureTr)搜索模型
 *
 * <AUTHOR>
 * @since  2023-08-07 16:27:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtEsignatureTrSO extends BaseModel {
    private static final long serialVersionUID = -19421605518846381L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("电子签名状态")
    private String esignatureStatus;

    @ApiModelProperty("签名主体")
    private Long entity;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("统一社会信用代码")
    private String registerSocialCreditCode;

    @ApiModelProperty("营业执照扫描件")
    private String businessLicenseScanAttachment;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账号")
    private String bankAccount;

    @ApiModelProperty("法人姓名")
    private String legalPersonMame;

    @ApiModelProperty("法人证件号")
    private String legalPersonLicenseNumber;

    @ApiModelProperty("法人联系方式")
    private String legalPersonContactDetails;

    @ApiModelProperty("法人证件")
    private String legalPersonLicenseAttachment;

    @ApiModelProperty("代理人姓名")
    private String agentName;

    @ApiModelProperty("代理人证件号")
    private String agentLicenseNumber;

    @ApiModelProperty("代理人联系方式")
    private String agentContactDetails;

    @ApiModelProperty("代理人证件附件")
    private String agentLicense;

    @ApiModelProperty("证书机构")
    private Long certificateAuthority;

    @ApiModelProperty("证书密钥文件")
    private String certificateAttachment;

    @ApiModelProperty("申请时间")
    private LocalDateTime applyDate;

    @ApiModelProperty("印章文件（描述签名的印章文件）")
    private String sealAttachment;

    @ApiModelProperty("认证流程id")
    private String authFlowId;

    @ApiModelProperty("机构认证授权链接")
    private String authUrl;

    @ApiModelProperty("机构认证认证后的ID")
    private String orgId;

    @ApiModelProperty("机构认证认证后的psnId")
    private String psnId;

    @ApiModelProperty("备注")
    private String remark;

}
