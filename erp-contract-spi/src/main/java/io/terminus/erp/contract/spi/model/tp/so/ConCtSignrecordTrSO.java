package io.terminus.erp.contract.spi.model.tp.so;


import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署记录(ConCtSignrecordTr)搜索模型
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignrecordTrSO extends BaseModel {
    private static final long serialVersionUID = -46521139121283982L;

    @ApiModelProperty("关联签署任务")
    private Long signTask;

    @ApiModelProperty("签署方")
    private Long signatory;

    @ApiModelProperty("签署时间")
    private LocalDateTime signTime;

    @ApiModelProperty("签署员工")
    private Long singEmp;

    @ApiModelProperty("签署人")
    private Long singUser;

    @ApiModelProperty("待签署文件")
    private Long signFile;

    @ApiModelProperty("电子签名")
    private Long esignature;

    @ApiModelProperty("已签署文件")
    private String signedFile;

}
