package io.terminus.erp.contract.spi.dict.tp;

/**
 * 合同类型（魏桥）(GenCtHeadTrExtWqCtType)字典
 *
 * <AUTHOR>
 * @since  2023-12-27 16:51:30
 */
public interface GenCtHeadTrExtWqCtTypeExpandDict {

    /**
     * 框架合同
     */
    String CTYPE0001 = "CTYPE0001";
    /**
     * 补充合同
     */
    String CTYPE0003 = "CTYPE0003";
    /**
     * 订单合同
     */
    String CTYPE0002 = "CTYPE0002";
    /**
     * 自制合同
     */
    String CTYPE0004 = "CTYPE0004";

}
