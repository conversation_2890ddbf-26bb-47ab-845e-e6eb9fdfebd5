package io.terminus.erp.contract.spi.model.tp.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.erp.md.spi.model.dto.base.GenComTypeCfDTO;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 电子签名(ConCtEsignatureTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-15 17:05:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignBankInfoCfDTO extends BaseModel {
    private static final long serialVersionUID = 948646768652370276L;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("联行号")
    private String bankNo;

}
