package io.terminus.erp.contract.spi.dict.tpl;

/**
 * 位置类型(CtTemplatePlaceholderConfigType)字典
 *
 * <AUTHOR>
 * @since  2023-10-24 16:17:36
 */
public interface CtTemplatePlaceholderConfigTypeDict {

    /**
     * 基础配置
     */
    String BASE_CONFIG = "BASE_CONFIG";
    /**
     * 页头配置
     */
    String HEADER_CONFIG = "HEADER_CONFIG";
    /**
     * 签订项目配置
     */
    String PROJECT_CONFIG = "PROJECT_CONFIG";
    /**
     * 页尾配置
     */
    String FOOTER_CONFIG = "FOOTER_CONFIG";
    /**
     * 钢材标准配置
     */
    String STEEL_CONFIG = "STEEL_CONFIG";
    /**
     * 明细表单列配置
     */
    String TABLE_COLUMN_CONFIG = "TABLE_COLUMN_CONFIG";
    /**
     * 明细备注
     */
    String TABLE_ROW_CONFIG = "TABLE_ROW_CONFIG";

}
