package io.terminus.erp.contract.spi.model.tp.req;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同和签署方式入参(ConContractSignway)分页查询请求
 *
 * <AUTHOR>
 * @since  2023-08-08 15:02:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConContractSignwayPageReq extends AbstractPageRequest {
    private static final long serialVersionUID = -63812891320170370L;

    @ApiModelProperty("合同id")
    private String contract;

    @ApiModelProperty("签署方式id")
    private String signWay;

}
