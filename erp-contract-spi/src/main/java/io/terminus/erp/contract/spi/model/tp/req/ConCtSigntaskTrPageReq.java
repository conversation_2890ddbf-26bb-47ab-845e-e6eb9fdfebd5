package io.terminus.erp.contract.spi.model.tp.req;


import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignatoryTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignfileTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignrecordTrDTO;
import java.time.LocalDateTime;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署任务(ConCtSigntaskTr)分页查询请求
 *
 * <AUTHOR>
 * @since  2023-08-07 16:27:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSigntaskTrPageReq extends AbstractPageRequest {
    private static final long serialVersionUID = -72235446309251722L;

    @ApiModelProperty("任务编码")
    private String code;

    @ApiModelProperty("任务标题")
    private String title;

    @ApiModelProperty("签署状态")
    private String signStatus;

    @ApiModelProperty("签署方式")
    private Long signWay;

    @ApiModelProperty("所属公司")
    private Long company;

    @ApiModelProperty("签署单据ID")
    private Long signBillId;

    @ApiModelProperty("签署单据编码")
    private String signBillCode;

    @ApiModelProperty("发起人")
    private Long sponsor;

    @ApiModelProperty("签署完成时间")
    private LocalDateTime finishTime;

    @ApiModelProperty("三方签署任务ID")
    private String signFlowId;

    @ApiModelProperty("签署方")
    private List<ConCtSignatoryTrDTO> signatories;

    @ApiModelProperty("签署文件")
    private List<ConCtSignfileTrDTO> signFiles;

    @ApiModelProperty("签署记录")
    private List<ConCtSignrecordTrDTO> signRecords;

    @ApiModelProperty("签署单据类型")
    private String signBillType;

    @ApiModelProperty("关联合同")
    private Long relatedContract;

}
