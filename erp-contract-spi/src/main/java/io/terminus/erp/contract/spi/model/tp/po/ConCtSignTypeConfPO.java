package io.terminus.erp.contract.spi.model.tp.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署类型配置(ConCtSignTypeConf)存储模型
 *
 * <AUTHOR>
 * @since  2023-09-05 20:01:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_ct_sign_type_conf")
public class ConCtSignTypeConfPO extends BaseModel {
    private static final long serialVersionUID = 343417086239075326L;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("甲方签署")
    @TableField("`party_a_sign`")
    private Boolean partyASign;

    @ApiModelProperty("乙方签署")
    @TableField("`party_b_sign`")
    private Boolean partyBSign;

    @ApiModelProperty("签署顺序：开启甲方先签，关闭乙方先签")
    @TableField("`party_a_first`")
    private Boolean partyAFirst;

    @ApiModelProperty("是否校验电子签名：线下不需校验电子签名；线上需要校验电子签名")
    @TableField("`verify_e_sign`")
    private Boolean verifyESign;

    @ApiModelProperty("关联渠道")
    @TableField("`channel`")
    private Long channel;

}
