package io.terminus.erp.contract.spi.model.tp.dto;


import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ConCtSignatoryAccountTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-08 14:46:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignatoryAccountTrDTO extends BaseModel {
    private static final long serialVersionUID = -39231249807266644L;

    @ApiModelProperty("账户状态")
    private String signatoryAccountStatus;

    @ApiModelProperty("账户余额（元）")
    private BigDecimal signatoryAccountAmount;

    @ApiModelProperty("账户已使用金额（元）")
    private BigDecimal signatoryAccountAmountUsed;

    @ApiModelProperty("账户上次充值时间")
    private LocalTime signatoryAmountTimeNews;

    @ApiModelProperty("余额生效日期")
    private LocalDateTime signatoryAccountEffectTime;

    @ApiModelProperty("余额失效日期")
    private LocalDateTime signatoryAccountLoseTime;

    @MetaModelField
    @ApiModelProperty("关联公司")
    private Long relCompany;

    @ApiModelProperty("账户编码")
    private String code;

    @ApiModelProperty("账户已使用流水")
    private List<ConSignatoryAccountUsedDTO> accountUsedFlow;

    @ApiModelProperty("账户充值流水")
    private List<ConSignatoryAccountRechargeDTO> accountRechargeFlow;

    @MetaModelField
    @ApiModelProperty("关联电子签名")
    private Long relEsign;

    @ApiModelProperty("账户类型")
    private String type;

}
