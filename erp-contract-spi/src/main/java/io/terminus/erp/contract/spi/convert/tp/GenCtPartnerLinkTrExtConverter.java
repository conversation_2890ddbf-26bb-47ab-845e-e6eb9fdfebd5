package io.terminus.erp.contract.spi.convert.tp;


import java.util.ArrayList;

import io.terminus.erp.md.spi.model.dto.ct.GenCtPartnerLinkTrDTO;
import io.terminus.erp.md.spi.model.po.ct.GenCtPartnerLinkTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtPartnerLinkTrExtDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.Objects;
import org.apache.commons.collections.MapUtils;

/**
 * 合同相关方关联表(GenCtPartnerLinkTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-12-27 17:30:53
 */
@Mapper(componentModel = "spring")
public interface GenCtPartnerLinkTrExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    GenCtPartnerLinkTrDTO convert(GenCtPartnerLinkTrExtDTO dto);

    default GenCtPartnerLinkTrExtDTO convert(GenCtPartnerLinkTrPO po) {
        GenCtPartnerLinkTrExtDTO res = new GenCtPartnerLinkTrExtDTO();
        this.convert(res, po);
        return res;
    }

    List<GenCtPartnerLinkTrDTO> convertToDtoList(List<GenCtPartnerLinkTrExtDTO> req);

    List<GenCtPartnerLinkTrExtDTO> convertToExtDtoList(List<GenCtPartnerLinkTrPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget GenCtPartnerLinkTrExtDTO dto, GenCtPartnerLinkTrPO po);

    default Map<String, Object> convertToExtraMap(GenCtPartnerLinkTrExtDTO dto){
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget GenCtPartnerLinkTrExtDTO dto, GenCtPartnerLinkTrPO po) {
        this.buildFields(po.getExtra(),dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(GenCtPartnerLinkTrExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (Objects.nonNull(dto.getExtWqRelCompany())) {
            extra.put(GenCtPartnerLinkTrExtDTO.Fields.extWqRelCompany, dto.getExtWqRelCompany());
        }

        return extra;
    }

    default void buildFields(Map<String, Object> extra, GenCtPartnerLinkTrExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        if (Objects.nonNull(extra.get(GenCtPartnerLinkTrExtDTO.Fields.extWqRelCompany))) {
            dto.setExtWqRelCompany((Long) extra.get(GenCtPartnerLinkTrExtDTO.Fields.extWqRelCompany));
        }

    }
}
