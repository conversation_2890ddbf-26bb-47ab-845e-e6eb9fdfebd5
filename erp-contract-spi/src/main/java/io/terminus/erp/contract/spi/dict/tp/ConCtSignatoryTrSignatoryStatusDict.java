package io.terminus.erp.contract.spi.dict.tp;

/**
 * 签署状态(ConCtSignatoryTrSignatoryStatus)字典
 *
 * <AUTHOR>
 * @since  2023-09-11 09:48:21
 */
public interface ConCtSignatoryTrSignatoryStatusDict {

    /**
     * 待展示
     */
    String NOT_DISPLAY = "NOT_DISPLAY";
    /**
     * 待签署
     */
    String WAITING = "WAITING";
    /**
     * 签署中
     */
    String SIGNING = "SIGNING";
    /**
     * 已签署
     */
    String SIGNED = "SIGNED";
    /**
     * 已拒绝
     */
    String REJECTED = "REJECTED";
    /**
     * 已关闭
     */
    String CLOSED = "CLOSED";

}
