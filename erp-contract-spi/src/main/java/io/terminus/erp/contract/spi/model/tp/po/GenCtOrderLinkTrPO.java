package io.terminus.erp.contract.spi.model.tp.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (GenCtOrderLinkTr)存储模型
 *
 * <AUTHOR>
 * @since  2023-10-18 15:21:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "gen_ct_order_link_tr")
public class GenCtOrderLinkTrPO extends BaseModel {
    private static final long serialVersionUID = -44816390732233950L;

    @ApiModelProperty("关联合同表")
    @TableField("`doc_ref`")
    private Long docRef;

    @ApiModelProperty("关联订单表")
    @TableField("`order_ref`")
    private Long orderRef;

}
