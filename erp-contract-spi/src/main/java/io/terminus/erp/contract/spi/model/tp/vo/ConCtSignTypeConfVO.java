package io.terminus.erp.contract.spi.model.tp.vo;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签署类型配置(ConCtSignTypeConf)视图模型
 *
 * <AUTHOR>
 * @since  2023-08-11 10:47:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtSignTypeConfVO extends BaseModel {
    private static final long serialVersionUID = -83713861882201252L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("甲方签署")
    private Boolean partyASign;

    @ApiModelProperty("乙方签署")
    private Boolean partyBSign;

    @ApiModelProperty("签署顺序：开启甲方先签，关闭乙方先签")
    private Boolean partyAFirst;

    @ApiModelProperty("是否校验电子签名：线下不需校验电子签名；线上需要校验电子签名")
    private Boolean verifyESign;

}
