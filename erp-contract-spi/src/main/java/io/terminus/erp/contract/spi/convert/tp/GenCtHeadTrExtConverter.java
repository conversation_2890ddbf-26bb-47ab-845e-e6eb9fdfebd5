package io.terminus.erp.contract.spi.convert.tp;


import io.terminus.erp.contract.spi.model.tp.dto.GenCtHeadTrExtDTO;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtHeadTrExtDTO.Fields;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtOrderLinkTrDTO;
import io.terminus.erp.md.spi.model.dto.ct.CtHeadTrDTO;
import io.terminus.erp.md.spi.model.po.ct.CtHeadTrPO;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections.MapUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 * 协议合同表(GenCtHeadTr)结构映射器
 *
 * <AUTHOR>
 * @since 2023-12-27 16:51:30
 */
@Mapper(componentModel = "spring")
public interface GenCtHeadTrExtConverter {

    @Mapping(target = "extra", expression = "java(convertToExtraMap(dto))")
    CtHeadTrDTO convert(GenCtHeadTrExtDTO dto);

    default GenCtHeadTrExtDTO convert(CtHeadTrPO po) {
        GenCtHeadTrExtDTO res = new GenCtHeadTrExtDTO();
        this.convert(res, po);
        return res;
    }

    List<CtHeadTrDTO> convertToDtoList(List<GenCtHeadTrExtDTO> req);

    List<GenCtHeadTrExtDTO> convertToExtDtoList(List<CtHeadTrPO> req);


    @Mapping(target = "extra", expression = "java(convertDtoExtFields(dto,po))")
    void convert(@MappingTarget GenCtHeadTrExtDTO dto, CtHeadTrPO po);

    default Map<String, Object> convertToExtraMap(GenCtHeadTrExtDTO dto) {
        return this.buildExtraMap(dto);
    }

    default Map<String, Object> convertDtoExtFields(@MappingTarget GenCtHeadTrExtDTO dto, CtHeadTrPO po) {
        this.buildFields(po.getExtra(), dto);
        return po.getExtra();
    }


    default Map<String, Object> buildExtraMap(GenCtHeadTrExtDTO dto) {
        Map<String, Object> extra = dto.getExtra();
        if (MapUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        if (Objects.nonNull(dto.getExtWqPayTerm())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqPayTerm, dto.getExtWqPayTerm());
        }

        if (Objects.nonNull(dto.getExtWqCtTpl())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtTpl, dto.getExtWqCtTpl());
        }

        if (Objects.nonNull(dto.getExtWqCtContent())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtContent, dto.getExtWqCtContent());
        }

        if (Objects.nonNull(dto.getExtWqCtDrafter())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtDrafter, dto.getExtWqCtDrafter());
        }

        if (Objects.nonNull(dto.getExtWqCtSource())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtSource, dto.getExtWqCtSource());
        }

        if (Objects.nonNull(dto.getExtWqCtInitiator())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtInitiator, dto.getExtWqCtInitiator());
        }

        if (Objects.nonNull(dto.getExtWqEsignId())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqEsignId, dto.getExtWqEsignId());
        }

        if (Objects.nonNull(dto.getExtWqCtPayProgresss())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtPayProgresss, dto.getExtWqCtPayProgresss());
        }

        if (Objects.nonNull(dto.getExtWqCtDrafteTime())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtDrafteTime, dto.getExtWqCtDrafteTime());
        }

        if (Objects.nonNull(dto.getExtWqSourceBillId())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqSourceBillId, dto.getExtWqSourceBillId());
        }

        if (Objects.nonNull(dto.getExtWqSourceBillCode())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqSourceBillCode, dto.getExtWqSourceBillCode());
        }

        if (Objects.nonNull(dto.getExtWqSignLocation())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqSignLocation, dto.getExtWqSignLocation());
        }

        if (Objects.nonNull(dto.getExtWqParentContract())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqParentContract, dto.getExtWqParentContract());
        }

//        if (Objects.nonNull(dto.getExtWqCtOrderRef())) {
//            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtOrderRef, dto.getExtWqCtOrderRef());
//        }

        if (Objects.nonNull(dto.getExtWqCtManager())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtManager, dto.getExtWqCtManager());
        }

        if (Objects.nonNull(dto.getExtWqCtPartner())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtPartner, dto.getExtWqCtPartner());
        }

        if (Objects.nonNull(dto.getExtWqCtType())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtType, dto.getExtWqCtType());
        }

        if (Objects.nonNull(dto.getExtWqCtEdit())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqCtEdit, dto.getExtWqCtEdit());
        }

        if (Objects.nonNull(dto.getExtWqDraftRemark())) {
            extra.put(GenCtHeadTrExtDTO.Fields.extWqDraftRemark, dto.getExtWqDraftRemark());
        }

        if (Objects.nonNull(dto.getExtWqDraftFile())) {
            extra.put(Fields.extWqDraftFile, dto.getExtWqDraftFile());
        }

        if (Objects.nonNull(dto.getExtWqDraftTime())) {
            extra.put(Fields.extWqDraftTime, dto.getExtWqDraftTime());
        }

        if (Objects.nonNull(dto.getExtWqSubmitTime())) {
            extra.put(Fields.extWqSubmitTime, dto.getExtWqSubmitTime());
        }

        return extra;
    }

    default void buildFields(Map<String, Object> extra, GenCtHeadTrExtDTO dto) {
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqPayTerm))) {
            dto.setExtWqPayTerm((Long) extra.get(GenCtHeadTrExtDTO.Fields.extWqPayTerm));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtTpl))) {
            dto.setExtWqCtTpl((Long) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtTpl));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtContent))) {
            dto.setExtWqCtContent((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtContent));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtDrafter))) {
            dto.setExtWqCtDrafter((Long) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtDrafter));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtSource))) {
            dto.setExtWqCtSource((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtSource));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtInitiator))) {
            dto.setExtWqCtInitiator((Long) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtInitiator));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqEsignId))) {
            dto.setExtWqEsignId((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqEsignId));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtPayProgresss))) {
            dto.setExtWqCtPayProgresss((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtPayProgresss));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtDrafteTime))) {
            dto.setExtWqCtDrafteTime((LocalDateTime) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtDrafteTime));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqSourceBillId))) {
            dto.setExtWqSourceBillId((Long) extra.get(GenCtHeadTrExtDTO.Fields.extWqSourceBillId));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqSourceBillCode))) {
            dto.setExtWqSourceBillCode((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqSourceBillCode));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqSignLocation))) {
            dto.setExtWqSignLocation((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqSignLocation));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqParentContract))) {
            dto.setExtWqParentContract((Long) extra.get(GenCtHeadTrExtDTO.Fields.extWqParentContract));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtOrderRef))) {
            dto.setExtWqCtOrderRef((List<GenCtOrderLinkTrDTO>) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtOrderRef));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtManager))) {
            dto.setExtWqCtManager((Long) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtManager));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtPartner))) {
            dto.setExtWqCtPartner((Boolean) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtPartner));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtType))) {
            dto.setExtWqCtType((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtType));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqCtEdit))) {
            dto.setExtWqCtEdit((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqCtEdit));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqDraftRemark))) {
            dto.setExtWqDraftRemark((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqDraftRemark));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqDraftFile))) {
            dto.setExtWqDraftFile((String) extra.get(GenCtHeadTrExtDTO.Fields.extWqDraftFile));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqSubmitTime))) {
            dto.setExtWqSubmitTime((LocalDateTime) extra.get(GenCtHeadTrExtDTO.Fields.extWqSubmitTime));
        }

        if (Objects.nonNull(extra.get(GenCtHeadTrExtDTO.Fields.extWqDraftTime))) {
            dto.setExtWqDraftTime((LocalDateTime) extra.get(GenCtHeadTrExtDTO.Fields.extWqDraftTime));
        }

    }
}
