package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSignatoryTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignatoryTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;
import org.mapstruct.Mappings;

/**
 * 签署方(ConCtSignatoryTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-09-11 09:48:21
 */
@Mapper(componentModel = "spring")
public interface ConCtSignatoryTrConverter {

    @Mappings({
        @Mapping(source = "signatory",target = "signatory.id"),
        @Mapping(source = "agent",target = "agent.id")
    })
    ConCtSignatoryTrDTO po2Dto(ConCtSignatoryTrPO req);

    @Mappings({
        @Mapping(source = "signatory",target = "signatory.id"),
        @Mapping(source = "agent",target = "agent.id")
    })
    List<ConCtSignatoryTrDTO> po2DtoList(List<ConCtSignatoryTrPO> poList);

    @Mappings({
        @Mapping(source = "signatory.id",target = "signatory"),
        @Mapping(source = "agent.id",target = "agent")
    })
    ConCtSignatoryTrPO dto2Po(ConCtSignatoryTrDTO req);

    @Mappings({
        @Mapping(source = "signatory.id",target = "signatory"),
        @Mapping(source = "agent.id",target = "agent")
    })
    List<ConCtSignatoryTrPO> dto2PoList(List<ConCtSignatoryTrDTO> dtoList);
}
