package io.terminus.erp.contract.spi.msg;

/**
 * 电子签名消息
 *
 * @className: CtSignMsg
 * @author: charl
 * @date: 2023/8/2 17:59
 */
public interface CtSignMsg {

    String CON_CT_ESIGNATURE_IS_NULL = "con.ct.esignature.is.null";
    String CON_CT_ESIGNATURE_NOT_SUPPORT = "con.ct.esignature.not.support";
    String CON_CT_ESIGNATURE_AUTH_INVOKE_FAILED = "con.ct.esignature.auth.invoke.failed";

    String CON_CT_SIGN_TASK_PARAM_IS_NULL = "con.ct.sign.task.param.is.null";

    String CON_CT_SIGN_TASK_CONTRACT_IS_NULL = "con.ct.sign.task.contract.is.null";

    String CON_CT_SIGN_TASK_SIGNWAY_IS_NULL = "con.ct.sign.task.signway.is.null";

    String CON_CT_SIGN_TASK_SIGNFILE_IS_NULL = "con.ct.sign.task.signfile.is.null";

    String  CON_CT_SIGN_TASK_SIGNATORY_IS_NULL = "con.ct.sign.task.signatory.is.null";

    String CON_CT_SIGN_TASK_EMPLOYEE_IS_NULL = "con.ct.sign.task.employee.is.null";

    String CON_CT_SIGN_TASK_USER_IS_NULL = "con.ct.sign.task.user.is.null";

    String CON_CT_SIGN_TASK_IS_EXIST = "con.ct.sign.task.is.exist";

    String CON_CT_SIGN_TASK_SIGNATORY_STATUS_IS_NOT_WAITING = "con.ct.sign.task.signatory.status.is.not.waiting";

    String CON_CT_SIGN_TASK_SIGNATORY_UPDATE_FAIL = "con.ct.sign.task.signatory.update.fail";

    String CON_CT_SIGN_TASK_IS_NULL = "con.ct.sign.task.is.null";

    String CON_CT_SIGN_TASK_STATUS_IS_NOT_SIGNING = "con.ct.sign.task.status.is.not.signing";

    String CON_CT_SIGN_TASK_SIGNATORY_STATUS_IS_NOT_WAITING_OR_REJECTED = "con.ct.sign.task.signatory.status.is.not.waiting.or.rejected";

    String CON_CT_SIGN_TASK_ESIGN_IS_NULL = "con.ct.sign.task.esign.is.null";

    String CON_CT_SIGN_TASK_ESIGN_IS_DISABLED = "con.ct.sign.task.esign.is.disabled";

    String CON_CT_SIGN_TASK_SIGNATORY_STATUS_IS_NOT_SIGNED = "con.ct.sign.task.signatory.status.is.not.signed";

    String CON_CT_SIGN_TASK_UPLOAD_FILE_FAIL =  "con.ct.sign.task.upload.file.fail";

    String CON_CT_SIGN_TASK_CREATE_FAIL = "con.ct.sign.task.create.fail";

    String CON_CT_SIGN_TASK_FILE_UPLOAD_FAIL = "con.ct.sign.task.file.upload.fail";

    String  CON_CT_SIGN_TASK_AGENT_IS_NULL = "con.ct.sign.task.agent.is.null";


    String  CON_CT_SIGN_UPDATE_SIGNWAY_FAILED = "con.ct.sign.update.signway.failed";
}
