package io.terminus.erp.contract.spi.convert.tpl;

import io.terminus.erp.contract.spi.model.tpl.po.CtTemplateInfoPO;
import io.terminus.erp.contract.spi.model.tpl.dto.CtTemplateInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * 合同模版数据表(CtTemplateInfo)结构映射器
 *
 * <AUTHOR>
 * @since  2023-10-18 15:24:38
 */
@Mapper(componentModel = "spring")
public interface CtTemplateInfoConverter {

    @Mapping(target = "bizType.id", source = "bizType")
    CtTemplateInfoDTO po2Dto(CtTemplateInfoPO req);

    List<CtTemplateInfoDTO> po2DtoList(List<CtTemplateInfoPO> poList);

    @Mapping(target = "bizType", source = "bizType.id")
    CtTemplateInfoPO dto2Po(CtTemplateInfoDTO req);

    List<CtTemplateInfoPO> dto2PoList(List<CtTemplateInfoDTO> dtoList);
}
