package io.terminus.erp.contract.spi.model.tp.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电子签名-授权用户关联表(ConCtEsignuserTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConCtEsignuserTrDTO extends BaseModel {
    private static final long serialVersionUID = 691066493816283019L;

    @ApiModelProperty("电子签名")
    private Long eSign;

    @ApiModelProperty("授权用户")
    private Long authorizedUser;

}
