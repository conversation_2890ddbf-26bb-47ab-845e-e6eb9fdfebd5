package io.terminus.erp.contract.spi.model.tp.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ContractDownloadDTO extends BaseModel {

    @ApiModelProperty(value = "文件链接")
    private String url;

    @ApiModelProperty("附件文件")
    private String file;

}
