package io.terminus.erp.contract.spi.model.tpl.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同模版业务类型配置表(CtTplTypeCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-10-17 19:39:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ct_tpl_type_cf")
public class CtTplTypeCfPO extends BaseModel {
    private static final long serialVersionUID = -91184951889756235L;

    @ApiModelProperty("类型编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("类型名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

}
