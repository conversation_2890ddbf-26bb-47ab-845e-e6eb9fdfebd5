package io.terminus.erp.contract.spi.model.tp.req;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同和签署方式入参(ConContractSignway)创建请求
 *
 * <AUTHOR>
 * @since  2023-08-08 15:02:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConContractSignwayCreateReq extends AbstractRequest {
    private static final long serialVersionUID = 164816627575666643L;

    @ApiModelProperty("合同id")
    private String contract;

    @ApiModelProperty("签署方式id")
    private String signWay;

}
