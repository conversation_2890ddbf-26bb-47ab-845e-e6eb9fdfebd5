package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtEsignuserTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtEsignuserTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 电子签名-授权用户关联表(ConCtEsignuserTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-08-03 11:17:18
 */
@Mapper(componentModel = "spring")
public interface ConCtEsignuserTrConverter {

    ConCtEsignuserTrDTO po2Dto(ConCtEsignuserTrPO req);

    List<ConCtEsignuserTrDTO> po2DtoList(List<ConCtEsignuserTrPO> poList);

    ConCtEsignuserTrPO dto2Po(ConCtEsignuserTrDTO req);

    List<ConCtEsignuserTrPO> dto2PoList(List<ConCtEsignuserTrDTO> dtoList);
}
