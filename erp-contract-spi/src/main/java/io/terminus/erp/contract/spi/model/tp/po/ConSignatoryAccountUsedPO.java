package io.terminus.erp.contract.spi.model.tp.po;


import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签章账户使用明细表(ConSignatoryAccountUsed)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-08 13:49:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_signatory_account_used")
public class ConSignatoryAccountUsedPO extends BaseModel {
    private static final long serialVersionUID = -55374131491370264L;

    @ApiModelProperty("关联签名账户")
    @TableField("`rel_sign_account`")
    private Long relSignAccount;

    @ApiModelProperty("关联单据id")
    @TableField("`rel_bill_id`")
    private Long relBillId;

    @ApiModelProperty("扣款金额（元）")
    @TableField("`deduction_amt`")
    private BigDecimal deductionAmt;

    @ApiModelProperty("扣款时间")
    @TableField("`deduction_time`")
    private LocalDateTime deductionTime;

    @ApiModelProperty("关联单据类型")
    @TableField("`rel_bill_type`")
    private String relBillType;

    @ApiModelProperty("签署方式")
    @TableField("`sign_type`")
    private String signType;

}
