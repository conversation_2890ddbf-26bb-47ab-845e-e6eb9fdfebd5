package io.terminus.erp.contract.spi.model.role.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.wq.md.spi.model.org.dto.OrgAdmOrgCfExtDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "con_org_role_tr")
public class ConOrgRoleTrPO extends BaseModel {

    @TableField("`org_code`")
    @ApiModelProperty("组织编码")
    private String orgCode;

    @TableField("`org_name`")
    @ApiModelProperty("组织名称")
    private String orgName;

    @TableField("`desc`")
    @ApiModelProperty("描述信息")
    private String desc;
}
