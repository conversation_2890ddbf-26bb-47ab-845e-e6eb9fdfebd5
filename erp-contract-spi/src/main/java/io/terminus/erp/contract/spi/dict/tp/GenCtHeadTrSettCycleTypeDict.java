package io.terminus.erp.contract.spi.dict.tp;

/**
 * 结算类型(GenCtHeadTrSettCycleType)字典
 *
 * <AUTHOR>
 * @since  2023-10-19 09:51:16
 */
public interface GenCtHeadTrSettCycleTypeDict {

    /**
     * daily
     */
    String D = "D";
    /**
     * weekly
     */
    String W = "W";
    /**
     * biweekly
     */
    String BW = "BW";
    /**
     * monthly
     */
    String M = "M";
    /**
     * bimonthly
     */
    String BM = "BM";
    /**
     * quarterly
     */
    String Q = "Q";
    /**
     * half yearly
     */
    String HY = "HY";
    /**
     * yearly
     */
    String Y = "Y";

}
