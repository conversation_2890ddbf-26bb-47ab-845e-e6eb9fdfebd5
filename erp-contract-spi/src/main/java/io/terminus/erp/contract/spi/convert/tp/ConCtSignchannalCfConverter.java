package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSignchannalCfPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignchannalCfDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 合同签署渠道类型(ConCtSignchannalCf)结构映射器
 *
 * <AUTHOR>
 * @since  2023-08-07 16:27:33
 */
@Mapper(componentModel = "spring")
public interface ConCtSignchannalCfConverter {

    ConCtSignchannalCfDTO po2Dto(ConCtSignchannalCfPO req);

    List<ConCtSignchannalCfDTO> po2DtoList(List<ConCtSignchannalCfPO> poList);

    ConCtSignchannalCfPO dto2Po(ConCtSignchannalCfDTO req);

    List<ConCtSignchannalCfPO> dto2PoList(List<ConCtSignchannalCfDTO> dtoList);
}
