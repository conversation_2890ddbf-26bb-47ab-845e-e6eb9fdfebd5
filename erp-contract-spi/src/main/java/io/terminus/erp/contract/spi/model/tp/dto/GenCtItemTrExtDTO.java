package io.terminus.erp.contract.spi.model.tp.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.erp.md.spi.model.dto.ct.CtItemTrDTO;
import io.terminus.trantor2.doc.annotation.ExtraMetaModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
/**
 * 协议合同行表(GenCtItemTr)传输模型
 *
 * <AUTHOR>
 * @since  2023-12-27 16:52:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@ExtraMetaModel
public class GenCtItemTrExtDTO extends CtItemTrDTO {
    private static final long serialVersionUID = -77924628812363233L;

    @ApiModelProperty("交货期（魏桥）")
    private String extWqDeliveryData;

    @MetaModelField
    @ApiModelProperty("价格目录")
    private Long extWqPriceTerm;

    @ApiModelProperty("数量")
    private BigDecimal extWqCtTotalQty = BigDecimal.ZERO;

    @ApiModelProperty("总价（含税）")
    private BigDecimal extWqCtTotalAmt = BigDecimal.ZERO;

    @ApiModelProperty("备注")
    private String extWqCtRemark;

    @MetaModelField
    @ApiModelProperty("关联采购订单行")
    private Long extWqOrderItemRef;

    @MetaModelField
    @ApiModelProperty("寻源清单项")
    private Long extWqSourcingMetarial;

    @ApiModelProperty("质保期")
    private Long extWqWarranty;

    @ApiModelProperty("附件")
    private String extWqAtt;

}
