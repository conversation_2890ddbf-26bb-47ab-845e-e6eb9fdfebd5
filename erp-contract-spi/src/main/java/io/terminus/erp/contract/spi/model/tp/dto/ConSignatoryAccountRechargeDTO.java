package io.terminus.erp.contract.spi.model.tp.dto;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * (ConSignatoryAccountRecharge)传输模型
 *
 * <AUTHOR>
 * @since  2023-11-07 13:32:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConSignatoryAccountRechargeDTO extends BaseModel {
    private static final long serialVersionUID = -12527351317456887L;

    @ApiModelProperty("订单状态")
    private String orderStatus;

    @ApiModelProperty("订单支付结果")
    private String orderResult;

    @ApiModelProperty("订单类型")
    private String orderType;

    @ApiModelProperty("订单金额（元）")
    private BigDecimal orderSum;

    @ApiModelProperty("微信订单号")
    private String wxOrderCode;

    @ApiModelProperty("支付时间")
    private LocalDateTime orderTime;

    @ApiModelProperty("报销凭证")
    private String reimbursement;

    @ApiModelProperty("纳税人识别号")
    private String taxpayerIdentificationNumber;

    @ApiModelProperty("开票地址")
    private String invoicingAddress;

    @ApiModelProperty("开票电话")
    private Long invoicingNumber;

    @ApiModelProperty("开户行")
    private String invoicingBank;

    @ApiModelProperty("银行账号")
    private Long invoicingBankAccount;

    @MetaModelField
    @ApiModelProperty("关联签署账户")
    private Long relSignAccount;

    @ApiModelProperty("编码")
    private String orderCode;

    @ApiModelProperty("预支付url")
    private String prepayUrl;

    @ApiModelProperty("回调url")
    private String notifyUrl;

    @MetaModelField
    @ApiModelProperty("充值金额关联")
    private Long rechargePackRef;

    @ApiModelProperty("二维码支付链接")
    private String payLink;

    @ApiModelProperty("支付结束时间")
    private LocalDateTime timeEnd;

}
