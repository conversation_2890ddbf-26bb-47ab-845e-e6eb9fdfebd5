package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtEsignatureTrPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtEsignatureTrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * 电子签名(ConCtEsignatureTr)结构映射器
 *
 * <AUTHOR>
 * @since  2023-10-15 17:05:43
 */
@Mapper(componentModel = "spring")
public interface ConCtEsignatureTrConverter {

    @Mapping(target = "entity.id", source = "entity")
    @Mapping(target = "certificateAuthority.id", source = "certificateAuthority")
    @Mapping(target = "relSignAccount.id", source = "relSignAccount")
    @Mapping(target = "bankRef.id", source = "bankRef")
    ConCtEsignatureTrDTO po2Dto(ConCtEsignatureTrPO req);

    List<ConCtEsignatureTrDTO> po2DtoList(List<ConCtEsignatureTrPO> poList);

    @Mapping(target = "entity", source = "entity.id")
    @Mapping(target = "certificateAuthority", source = "certificateAuthority.id")
    @Mapping(target = "relSignAccount", source = "relSignAccount.id")
    @Mapping(target = "bankRef", source = "bankRef.id")
    ConCtEsignatureTrPO dto2Po(ConCtEsignatureTrDTO req);

    List<ConCtEsignatureTrPO> dto2PoList(List<ConCtEsignatureTrDTO> dtoList);
}
