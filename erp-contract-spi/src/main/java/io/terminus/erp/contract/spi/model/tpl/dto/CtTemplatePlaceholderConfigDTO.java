package io.terminus.erp.contract.spi.model.tpl.dto;


import java.util.ArrayList;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.terminus.trantor2.doc.annotation.MetaModelField;
/**
 * 合同模版占位符配置表(CtTemplatePlaceholderConfig)传输模型
 *
 * <AUTHOR>
 * @since  2023-10-24 16:17:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CtTemplatePlaceholderConfigDTO extends BaseModel {
    private static final long serialVersionUID = 365150307653931966L;

    @ApiModelProperty("位置类型")
    private String type;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("占位符")
    private String key;

    @ApiModelProperty("占位符对应值")
    private String value;

    @MetaModelField
    @ApiModelProperty("业务类型")
    private CtTplTypeCfDTO bizType;

    @ApiModelProperty("内容类型")
    private String contentType;

    @ApiModelProperty("排序字段（从小到大）")
    private Integer sort;

    @ApiModelProperty("备注信息")
    private String remark;

}
