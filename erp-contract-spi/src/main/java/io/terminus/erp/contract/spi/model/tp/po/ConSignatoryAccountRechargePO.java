package io.terminus.erp.contract.spi.model.tp.po;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (ConSignatoryAccountRecharge)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-07 13:32:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "con_signatory_account_recharge")
public class ConSignatoryAccountRechargePO extends BaseModel {
    private static final long serialVersionUID = -32397025062564847L;

    @ApiModelProperty("订单状态")
    @TableField("`order_status`")
    private String orderStatus;

    @ApiModelProperty("订单支付结果")
    @TableField("`order_result`")
    private String orderResult;

    @ApiModelProperty("订单类型")
    @TableField("`order_type`")
    private String orderType;

    @ApiModelProperty("订单金额（元）")
    @TableField("`order_sum`")
    private BigDecimal orderSum;

    @ApiModelProperty("微信订单号")
    @TableField("`wx_order_code`")
    private String wxOrderCode;

    @ApiModelProperty("支付时间")
    @TableField("`order_time`")
    private LocalDateTime orderTime;

    @ApiModelProperty("报销凭证")
    @TableField("`reimbursement`")
    private String reimbursement;

    @ApiModelProperty("纳税人识别号")
    @TableField("`taxpayer_identification_number`")
    private String taxpayerIdentificationNumber;

    @ApiModelProperty("开票地址")
    @TableField("`invoicing_address`")
    private String invoicingAddress;

    @ApiModelProperty("开票电话")
    @TableField("`invoicing_number`")
    private Long invoicingNumber;

    @ApiModelProperty("开户行")
    @TableField("`invoicing_bank`")
    private String invoicingBank;

    @ApiModelProperty("银行账号")
    @TableField("`invoicing_bank_account`")
    private Long invoicingBankAccount;

    @ApiModelProperty("关联签署账户")
    @TableField("`rel_sign_account`")
    private Long relSignAccount;

    @ApiModelProperty("编码")
    @TableField("`order_code`")
    private String orderCode;

    @ApiModelProperty("预支付url")
    @TableField("`prepay_url`")
    private String prepayUrl;

    @ApiModelProperty("回调url")
    @TableField("`notify_url`")
    private String notifyUrl;

    @ApiModelProperty("充值金额关联")
    @TableField("`recharge_pack_ref`")
    private Long rechargePackRef;

    @ApiModelProperty("二维码支付链接")
    @TableField("`pay_link`")
    private String payLink;

    @ApiModelProperty("支付结束时间")
    @TableField("`time_end`")
    private LocalDateTime timeEnd;

}
