package io.terminus.erp.contract.spi.model.tp.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * 传输模型：追加签署图片上下文对象
 *
 * 借助服务编排，通过合同的需求公司字段，查询到采购组织，然后通过此对象传递到合同】域
 *
 * <AUTHOR>
 * @since  2024-11-15 15:02:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConAppendSingImgContextDTO extends BaseModel {
    private static final long serialVersionUID = 806414552084757103L;

    @ApiModelProperty("合同id")
    private Long contractId;

    @ApiModelProperty("采购组织ID")
    private Long orgPurOrgCfId;

    @ApiModelProperty("需求公司")
    private Long invOrgId;

}
