package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ESignRechargePackPO;
import io.terminus.erp.contract.spi.model.tp.dto.ESignRechargePackDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * 电子签章充值套餐(ESignRechargePack)结构映射器
 *
 * <AUTHOR>
 * @since  2023-11-06 16:07:23
 */
@Mapper(componentModel = "spring")
public interface ESignRechargePackConverter {

    ESignRechargePackDTO po2Dto(ESignRechargePackPO req);

    List<ESignRechargePackDTO> po2DtoList(List<ESignRechargePackPO> poList);

    ESignRechargePackPO dto2Po(ESignRechargePackDTO req);

    List<ESignRechargePackPO> dto2PoList(List<ESignRechargePackDTO> dtoList);
}
