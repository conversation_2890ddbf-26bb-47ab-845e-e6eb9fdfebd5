package io.terminus.erp.contract.spi.convert.tp;

import io.terminus.erp.contract.spi.model.tp.po.ConCtSignImgCfPO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignImgCfDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import java.util.List;

/**
 * (ConCtSignImgCf)结构映射器
 *
 * <AUTHOR> 
 * @since  2024-11-15 16:13:39
 */
@Mapper(componentModel = "spring")
public interface ConCtSignImgCfConverter {

    ConCtSignImgCfDTO po2Dto(ConCtSignImgCfPO req);

    List<ConCtSignImgCfDTO> po2DtoList(List<ConCtSignImgCfPO> poList);

    ConCtSignImgCfPO dto2Po(ConCtSignImgCfDTO req);

    List<ConCtSignImgCfPO> dto2PoList(List<ConCtSignImgCfDTO> dtoList);
}
