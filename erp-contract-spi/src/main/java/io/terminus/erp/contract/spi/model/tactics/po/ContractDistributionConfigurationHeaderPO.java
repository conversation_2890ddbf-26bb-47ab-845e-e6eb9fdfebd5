package io.terminus.erp.contract.spi.model.tactics.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同共享池派单展示规则头标
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "contract_distribution_configuration_header_table")
public class ContractDistributionConfigurationHeaderPO extends BaseModel {

    @ApiModelProperty("行政组织")
    @TableField("administrative_organization")
    private Long administrativeOrganization;

    @ApiModelProperty("描述信息")
    @TableField("`desc`")
    private String desc;

}
