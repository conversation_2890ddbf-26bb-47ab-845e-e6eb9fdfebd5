package io.terminus.erp.contract.sign.callback;

import io.swagger.annotations.Api;
import io.terminus.erp.contract.domain.service.WxPayCallBackService;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: WxCallBackController
 * @author: charl
 * @date: 2023/11/7 09:36
 */
@Api(tags = "协议合同")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("api/service/erp-contract/controller/ct/")
public class WxCallBackController {

    private final WxPayCallBackService wxCallBackService;

    @RequestMapping("/wxPayCallBack")
    @ResponseBody
    public String wxPayCallBack(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return wxCallBackService.wxPayCallBack(request);
    }

}
