package io.terminus.erp.contract.sign;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.erp.contract.domain.service.CtBaseService;
import io.terminus.erp.contract.domain.service.CtOfflineSignService;
import io.terminus.erp.contract.domain.service.CtSignTaskService;
import io.terminus.erp.contract.domain.service.QysSignService;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignatoryTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSigntaskTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtHeadTrExtDTO;
import io.terminus.erp.contract.spi.model.tp.dto.RichTextToPdfDTO;
import io.terminus.erp.md.spi.model.dto.ct.CtHeadTrDTO;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: CtSignTaskApiAction
 * @author: charl
 * @date: 2023/7/31 16:46
 */
@Api(tags = "签署任务")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/service/erp-contract/action/ct-signtask/")
public class CtSignTaskApiAction {

    private final CtBaseService ctBaseService;
    private final CtSignTaskService ctSignTaskService;
    private final CtOfflineSignService ctOfflineSignService;
    private final QysSignService qysSignService;


    /**
     * 保存签署任务
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("保存签署任务")
    @Action(name = "保存签署任务", value = "CON_ERP_SAVE_SIGNTASK")
    @PostMapping("/save")
    public Response<ConCtSigntaskTrDTO> save(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO save = ctSignTaskService.save(request);
        return Response.ok(save);
    }


    /**
     * 批量保存签署方信息
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("批量保存签署方信息")
    @Action(name = "批量保存签署方信息", value = "CON_BATCH_SAVE_SIGNATORIES")
    @PostMapping("bashSaveSinatories")
    public Response<ConCtSigntaskTrDTO> batchSaveSignatories(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctSignTaskService.batchSaveSignatories(request);
        return Response.ok(conCtSigntaskTrDTO);
    }


    /**
     * 批量保存签署文件
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("批量保存签署文件")
    @Action(name = "批量保存签署文件", value = "CON_BATCH_SAVE_SIGNFILE")
    @PostMapping("bashSaveSignFile")
    public Response<ConCtSigntaskTrDTO> batchSaveSignFile(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctSignTaskService.batchSaveSignFile(request);
        return Response.ok(conCtSigntaskTrDTO);
    }

    /**
     * 签署方拒绝
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("签署方拒绝")
    @Action(name = "签署方拒绝", value = "CON_REJECT_SIGNATORY")
    @PostMapping("rejectSignatory")
    public Response<ConCtSignatoryTrDTO> rejectSignatory(@RequestBody ConCtSignatoryTrDTO request) {
        ConCtSignatoryTrDTO conCtSignatoryTrDTO = ctSignTaskService.rejectSignatory(request);
        return Response.ok(conCtSignatoryTrDTO);
    }

    /**
     * 关闭签署任务
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("关闭签署任务")
    @Action(name = "关闭签署任务", value = "CON_CLOSE_SIGN_TASK")
    @PostMapping("closeSignTask")
    public Response<ConCtSigntaskTrDTO> closeSignTask(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO conCtSignatoryTrDTO = ctSignTaskService.closeSignTask(request);
        return Response.ok(conCtSignatoryTrDTO);
    }

    /**
     * 关闭签署任务
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("根据合同关闭签署任务")
    @Action(name = "根据合同关闭签署任务", value = "CON_CLOSE_SIGN_TASK_BY_CONTRACT")
    @PostMapping("closeSignTaskByContract")
    public Response<CtHeadTrDTO> closeSignTaskByContract(@RequestBody CtHeadTrDTO request) {
        CtHeadTrDTO ctHeadTrDTO = ctSignTaskService.closeSignTaskByContract(request);
        return Response.ok(ctHeadTrDTO);
    }

    /**
     * 签署（线下签署）
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("签署（线下签署）")
    @Action(name = "签署（线下签署）", value = "CON_SIGNING_SIGN_TASK")
    @PostMapping("signingSignTask")
    public Response<ConCtSignatoryTrDTO> signingSignTask(@RequestBody ConCtSignatoryTrDTO request) {
        ConCtSignatoryTrDTO conCtSignatoryTrDTO = ctSignTaskService.signingSignTask(request);
        return Response.ok(conCtSignatoryTrDTO);
    }

    /**
     * 根据签署任务签署（线下签署）
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("根据签署任务签署（线下签署）")
    @Action(name = "根据签署任务（线下签署）", value = "CON_SIGNING_SIGN_TASK_BY_TASK")
    @PostMapping("signingSignTaskByTask")
    public Response<ConCtSigntaskTrDTO> signingSignTaskByTask(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctSignTaskService.signingSignTaskByTask(request);
        return Response.ok(conCtSigntaskTrDTO);
    }

    /**
     * 电子签章签署
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("电子签章签署")
    @Action(name = "电子签章签署", value = "CON_E_SIGNING_SIGN")
    @PostMapping("eSigningSign")
    public Response<ConCtSignatoryTrDTO> eSigningSign(@RequestBody ConCtSignatoryTrDTO request) {
        ConCtSignatoryTrDTO conCtSignatoryTrDTO = ctSignTaskService.eSigningSign(request);
        return Response.ok(conCtSignatoryTrDTO);
    }

    /**
     * 校验单据是否存在进行中的签署任务
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("校验单据是否存在进行中的签署任务")
    @Action(name = "校验单据是否存在进行中的签署任务", value = "CON_CHECK_SIGN_TASK_IN_PROGRESS")
    @PostMapping("checkSignTaskInProgress")
    public Response<ConCtSigntaskTrDTO> checkSignTaskInProgress(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctSignTaskService.checkSignTaskInProgress(request);
        return Response.ok(conCtSigntaskTrDTO);
    }


    /**
     * 获取签署任务信息：签署文件和签署方(平台端)
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("获取签署任务信息：签署文件和签署方")
    @Action(name = "获取签署任务信息：签署文件和签署方", value = "CON_QUERY_SIGN_TASK_INFO")
    @PostMapping("querySignTaskInfo")
    public Response<ConCtSigntaskTrDTO> querySignTaskInfo(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctSignTaskService.querySignTaskInfo(request);
        return Response.ok(conCtSigntaskTrDTO);
    }

    /**
     * 获取签署任务信息：签署文件和签署方(供应商端)
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("获取签署任务信息：签署文件和签署方(供应商端)")
    @Action(name = "获取签署任务信息：签署文件和签署方(供应商端)", value = "CON_QUERY_SUPPLIER_SIGN_TASK_INFO")
    @PostMapping("querySupplierSignTaskInfo")
    public Response<ConCtSigntaskTrDTO> querySupplierSignTaskInfo(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctSignTaskService.querySupplierSignTaskInfo(request);
        return Response.ok(conCtSigntaskTrDTO);
    }


    /**
     * 基于合同自动创建签署任务
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("基于合同自动创建签署任务")
    @Action(name = "基于合同自动创建签署任务", value = "CON_ADD_SIGN_TASK_BY_CONTRACT")
    @PostMapping("createSignTaskByContract")
    public Response<ConCtSigntaskTrDTO> createSignTaskByContract(@RequestBody CtHeadTrDTO request) {
        // 审批流第一步
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctSignTaskService.createSignTaskByContract(request.getId());
        return Response.ok(conCtSigntaskTrDTO);
    }

    /**
     * 纸版提交流程：
     * 基于合同自动创建线下签署任务
     * @param request action入参
     * @return void
     */
    @Deprecated
    @ApiOperation("基于合同自动创建线下签署任务")
    @Action(name = "基于合同自动创建线下签署任务", value = "CON_OFFLINE_SIGN_TASK_BY_CONTRACT")
    @PostMapping("createOfflineSignTaskByContract")
    public Response<ConCtSigntaskTrDTO> createOfflineSignTaskByContract(@RequestBody CtHeadTrDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctOfflineSignService.createOfflineSignTaskByContract(request);
        // 线下签,流转到内部公司
        return Response.ok(conCtSigntaskTrDTO);
    }



    /**
     *
     * 甲方自动签署流程
     * @param request action入参
     * @return void
     */
    @ApiOperation("采购方自动签署流程")
    @Action(name = "采购方自动签署流程", value = "CON_PURCHASER_AUTO_SIGN_TASK")
    @PostMapping("purchaserAutoSignTask")
    public Response<ConCtSigntaskTrDTO> purchaserAutoSignTask(@RequestBody ConCtSigntaskTrDTO request) {
        //编制合同 提交审批
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = qysSignService.launchOnlineSign(request);
        return Response.ok(conCtSigntaskTrDTO);
    }

    /**
     * 供应商端：获取契约锁的签署链接；
     * 因为契约锁的签署链接有有效期，供应商端每次点击线上签署都重新获取链接
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("供应商签署")
    @Action(name = "供应商签署", value = "CON_SUPPLIER_SIGN")
    @PostMapping("supplierSign")
    public Response<ConCtSignatoryTrDTO> supplierSign(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSignatoryTrDTO conCtSignatoryTrDTO = qysSignService.getSupplierESignUrl(request);
        return Response.ok(conCtSignatoryTrDTO);
    }

    /**
     * 根据富文本生成PDF
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("富文本生成PDF")
    @Action(name = "富文本生成PDF", value = "CON_RICH_TEXT_TO_PDF")
    @PostMapping("richTextToPdf")
    public Response<RichTextToPdfDTO> richTextToPdf(@RequestBody RichTextToPdfDTO request) {
        RichTextToPdfDTO richTextToPdfDTO = ctBaseService.richTextToPdf(request);
        return Response.ok(richTextToPdfDTO);
    }

    @ApiOperation("合同审批提交参数转换")
    @Action(name = "合同审批提交参数转换", value = "CON_GENERATE_PDF_BY_CT")
    @PostMapping("generatePdfByCt")
    public Response<GenCtHeadTrExtDTO> generatePdfByCt(@RequestBody GenCtHeadTrExtDTO request) {
        GenCtHeadTrExtDTO headTrExtDTO = qysSignService.generatePdfByCt(request);
        return Response.ok(headTrExtDTO);
    }

}
