package io.terminus.erp.contract.api;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.Paging;
import io.terminus.erp.contract.domain.service.CtTemplateService;
import io.terminus.erp.contract.spi.model.tp.dto.CtTplDraftConfigDTO;
import io.terminus.erp.contract.spi.model.tp.dto.CtTplGangcaiDtoDTO;
import io.terminus.erp.contract.spi.model.tpl.dto.CtTemplatePlaceholderConfigDTO;
import io.terminus.erp.contract.spi.model.tpl.dto.CtTplPlaceHolderDtoDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: CtTemplateErpApiAction
 * @author: charl
 * @date: 2023/9/14 13:57
 */
@Api(tags = "合同模版")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/service/erp-contract/template/action/ct/")
public class CtTemplateErpApiAction {

    private final CtTemplateService ctTemplateService;

    /**
     * 查询合同模版业务类型
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("查询合同模版占位符")
    @Action(name = "查询合同模版占位符", value = "CON_TPL_QUERY_PREFIX")
    @PostMapping("/queryTplPrefix")
    public Paging<CtTplPlaceHolderDtoDTO> queryTplPrefix(@RequestBody CtTemplatePlaceholderConfigDTO request) {
        return ctTemplateService.queryTplPrefix(request);
    }



    /**
     * 查询编制合同信息
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("查询编制合同信息")
    @Action(name = "查询编制合同信息", value = "CON_TPL_QUERY_DRAFT_PREFIX")
    @PostMapping("/queryDraftPrefix")
    public Paging<CtTplGangcaiDtoDTO> queryDraftPrefix(@RequestBody CtTplDraftConfigDTO request) {
        return ctTemplateService.queryDraftPrefix(request);
    }

}
