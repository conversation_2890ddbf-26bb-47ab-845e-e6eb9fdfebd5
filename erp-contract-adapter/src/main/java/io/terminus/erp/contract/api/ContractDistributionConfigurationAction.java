package io.terminus.erp.contract.api;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.erp.contract.domain.service.ContractDistributionConfigurationHeaderService;
import io.terminus.erp.contract.domain.service.ExtRoleService;
import io.terminus.erp.contract.spi.model.role.dto.ConOrgRoleTrDTO;
import io.terminus.erp.contract.spi.model.role.dto.RoleDTO;
import io.terminus.erp.contract.spi.model.tactics.dto.ContractDistributionConfigurationHeaderDTO;
import io.terminus.erp.contract.spi.model.tactics.dto.ExtCtHeadTrDTO;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "合同派单展示规则")
@RequestMapping("api/service/erp-contract/action/configuration/")
public class ContractDistributionConfigurationAction {

    private final ExtRoleService extRoleService;

    private final ContractDistributionConfigurationHeaderService contractDistributionConfigurationHeaderService;

    @ApiOperation("合同派单展示规则保存")
    @Action(name = "合同派单展示规则保存", value = "contract_configuration_save_update")
    @PostMapping("/contract_configuration_save_update")
    public Response<Void> saveOrUpdate(@RequestBody ContractDistributionConfigurationHeaderDTO contractDistributionConfigurationHeaderDTO) {
        log.info("ContractDistributionConfigurationAction#saveCompletedCheck start params {}", JSON.toJSONString(contractDistributionConfigurationHeaderDTO));
        contractDistributionConfigurationHeaderService.saveOrUpdate(contractDistributionConfigurationHeaderDTO);
        return Response.ok();
    }

    @ApiOperation("合同派单展示规则删除")
    @Action(name = "合同派单展示规则删除", value = "contract_configuration_delete")
    @PostMapping("/contract_configuration_delete")
    public Response<Void> delete(@RequestBody ContractDistributionConfigurationHeaderDTO contractDistributionConfigurationHeaderDTO) {
        log.info("ContractDistributionConfigurationAction#saveCompletedCheck start params {}", JSON.toJSONString(contractDistributionConfigurationHeaderDTO));
        contractDistributionConfigurationHeaderService.delete(contractDistributionConfigurationHeaderDTO);
        return Response.ok();
    }

    @ApiOperation("合同共享池分页查询")
    @Action(name = "合同共享池分页查询", value = "contract_share_page")
    @PostMapping("/contract_share_page")
    public Response<Paging<ExtCtHeadTrDTO>> contractPaging(@RequestBody Pageable pageable) {
        log.info("ContractDistributionConfigurationAction#contractPaging start params {}", JSON.toJSONString(pageable));
        return Response.ok(contractDistributionConfigurationHeaderService.contractPaging(pageable));
    }

    @ApiOperation("查询关联权限采购组织")
    @Action(name = "查询关联权限采购组织", value = "find_org_rule_action")
    @PostMapping("/find_org_rule_action")
    public Response<ConOrgRoleTrDTO> fndOrgRuleAction(@RequestBody Pageable pageable) {
        log.info("ContractDistributionConfigurationAction#fndOrgRuleAction start params {}", JSONUtil.toJsonStr(pageable));
        return Response.ok(contractDistributionConfigurationHeaderService.findRuleOrgIds(pageable));
    }


    /**
     * console引用服务key：contract_role_save_service
     * @param conOrgRoleTrDTO 请求参数
     * @return res
     */
    @ApiOperation("合同列表权限保存服务")
    @Action(name = "合同列表权限保存服务", value = "contract_role_save")
    @PostMapping("/contract_role_save")
    public Response<Void> contractSave(@RequestBody ConOrgRoleTrDTO conOrgRoleTrDTO) {
        log.info("ContractDistributionConfigurationAction.contractSave start params {}", JSON.toJSONString(conOrgRoleTrDTO));
        extRoleService.contractSave(conOrgRoleTrDTO);
        return Response.ok();
    }

    /**
     * console引用服务：contract_role_delete_service
     * @param conOrgRoleTrDTO 请求参数
     * @return res
     */
    @ApiOperation("合同列表权限删除服务")
    @Action(name = "合同列表权限删除服务", value = "contract_role_delete")
    @PostMapping("/contract_role_delete")
    public Response<Void> contractDelete(@RequestBody ConOrgRoleTrDTO conOrgRoleTrDTO) {
        log.info("ContractDistributionConfigurationAction.contractDelete start params {}", JSON.toJSONString(conOrgRoleTrDTO));
        extRoleService.contractDelete(conOrgRoleTrDTO);
        return Response.ok();
    }

}
