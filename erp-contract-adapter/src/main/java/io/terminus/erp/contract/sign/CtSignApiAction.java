package io.terminus.erp.contract.sign;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.erp.contract.domain.service.CtSignAccountService;
import io.terminus.erp.contract.domain.service.CtEsignatureService;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtEsignatureTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConEsignEmpDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ContractDownloadDTO;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtHeadTrExtDTO;
import io.terminus.trantor2.common.dto.Paging;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: CtSignApiAction
 * @author: charl
 * @date: 2023/7/31 16:46
 */
@Api(tags = "电子签名")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/service/erp-contract/action/ct-sign/")
public class CtSignApiAction {

    private final CtEsignatureService ctSignService;

    private final CtSignAccountService ctSignAccountService;


    /**
     * 保存电子签章
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("保存电子签章")
    @Action(name = "保存电子签章", value = "CON_SAVE_ESIGN")
    @PostMapping("/save")
    public Response<ConCtEsignatureTrDTO> save(@RequestBody ConCtEsignatureTrDTO request) {
        Long save = ctSignService.save(request);
        request.setId(save);
        return Response.ok(request);
    }


    /**
     * 电子签章组织认证
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("电子签章组织认证")
    @Action(name = "电子签章组织认证", value = "CON_ERP_SIGN_AUTH")
    @PostMapping("/signAuth")
    public Response<ConCtEsignatureTrDTO> signAuth(@RequestBody ConCtEsignatureTrDTO request) {
        ConCtEsignatureTrDTO conCtEsignatureTrDTO = ctSignService.signAuth(request);
        return Response.ok(conCtEsignatureTrDTO);
    }


    /**
     * 授权用户
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("电子签章授权用户")
    @Action(name = "电子签章授权用户", value = "CON_ERP_GRANT_USER_AUTH")
    @PostMapping("/grandErpUserAuth")
    public Response<Void> grandErpUserAuth(@RequestBody ConEsignEmpDTO request) {
        ctSignService.grandUserAuth(request);
        return Response.ok();
    }

    /**
     * 电子签章已授权用户列表
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("电子签章已授权用户列表")
    @Action(name = "电子签章已授权用户列表", value = "CON_ERP_AUTHED_USER_LIST")
    @PostMapping("/authedUserList")
    public Response<Paging<Long>> authedUserList(@RequestBody ConEsignEmpDTO request) {
        Paging<Long> longs = ctSignService.authedUserList(request);
        return Response.ok(longs);
    }

    @ApiOperation("下载契约锁认证资料")
    @Action(name = "下载契约锁认证资料", value = "download_certification_information_action")
    @PostMapping(value = "download_certification_information_action")
    public Response<ContractDownloadDTO>  downloadCertificationAction(ContractDownloadDTO contractDownloadDTO) {
        return Response.ok(ctSignAccountService.downloadCertificationAction());
    }

    @ApiOperation("导入合同明细行")
    @Action(name = "导入合同明细行", value = "import_contract_line")
    @PostMapping(value = "import_contract_line")
    public Response<GenCtHeadTrExtDTO> importContractLine(ContractDownloadDTO contractDownloadDTO) {
        return Response.ok(ctSignAccountService.importContractLine(contractDownloadDTO));
    }


}
