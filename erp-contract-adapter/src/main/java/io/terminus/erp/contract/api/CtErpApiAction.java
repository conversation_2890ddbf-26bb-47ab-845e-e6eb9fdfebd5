package io.terminus.erp.contract.api;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdRequest;
import io.terminus.erp.contract.domain.service.CtErpService;
import io.terminus.erp.contract.spi.model.tp.dto.ConAppendSingImgContextDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConContractSignwayDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSigntaskTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.GenCtHeadTrExtDTO;
import io.terminus.erp.md.spi.model.dto.ct.CtHeadTrDTO;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 协议合同
 *
 * <AUTHOR>
 */
@Api(tags = "协议合同")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/service/erp-contract/action/ct/")
public class CtErpApiAction {

    private final CtErpService ctErpService;

    /**
     * 保存完整性检查
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("保存协议合同完整性检查")
    @Action(name = "保存协议合同完整性检查", value = "CON_SAVE_CHECK_ACTION")
    @PostMapping("/saveCompletedCheck")
    public Response<Void> saveCompletedCheck(@RequestBody CtHeadTrDTO request) {
        ctErpService.saveCompletedCheck(request);
        return Response.ok();
    }

    /**
     * 保存
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("保存协议合同")
    @Action(name = "保存协议合同", value = "CON_SAVE_ACTION")
    @PostMapping("/save")
    public Response<CtHeadTrDTO> save(@RequestBody GenCtHeadTrExtDTO request) {
        ctErpService.save(request);
        return Response.ok(request);
    }

    /**
     * 归档合同
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("归档合同")
    @Action(name = "归档合同", value = "CON_ARCHIVE_ACTION")
    @PostMapping("/archive")
    public Response<CtHeadTrDTO> archive(@RequestBody GenCtHeadTrExtDTO request) {
        ctErpService.archive(request);
        return Response.ok(request);
    }

    /**
     * 变更
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("变更协议合同")
    @Action(name = "变更协议合同", value = "CON_CHANGE_ACTION")
    @PostMapping("/change")
    public Response<CtHeadTrDTO> change(@RequestBody GenCtHeadTrExtDTO request) {
        ctErpService.change(request);
        return Response.ok(request);
    }

    /**
     * 提交完整性检查
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("提交协议合同完整性检查")
    @Action(name = "提交协议合同完整性检查", value = "CON_SUBMIT_CHECK_ACTION")
    @PostMapping("/submitCompletedCheck")
    public Response<Void> submitCompletedCheck(@RequestBody CtHeadTrDTO request) {
        ctErpService.submitCompletedCheck(request);
        return Response.ok();
    }

    /**
     * 根据Id查询协议合同
     *
     * @param request action入参
     * @return CtHeadTrDTOCtHeadTrDTO
     */
    @ApiOperation("根据Id查询协议合同")
    @Action(name = "根据Id查询协议合同", value = "CON_QUERY_BY_ID_ACTION")
    @PostMapping("/queryCtById")
    public Response<CtHeadTrDTO> queryCtById(@RequestBody IdRequest request) {
        if (request == null || request.getId() == null) {
            return Response.ok();
        }
        CtHeadTrDTO ctHeadTrDTO = ctErpService.queryFullCtById(request.getId());
        return Response.ok(ctHeadTrDTO);
    }

    /**
     * 推送合同完成MQ消息
     *
     * @param request action入参
     * @return CtHeadTrDTO
     */
    @ApiOperation("推送合同失效MQ消息")
    @Action(name = "推送合同失效MQ消息", value = "CON_PUSH_CT_INVALID_MSG_ACTION")
    @PostMapping("/pushCtInvalidMsg")
    public Response<CtHeadTrDTO> pushCtInvalidMsg(@RequestBody CtHeadTrDTO request) {
        if (request == null || request.getId() == null) {
            return Response.ok();
        }
        CtHeadTrDTO ctHeadTrDTO = ctErpService.pushCtInvalidMsg(request);
        return Response.ok(ctHeadTrDTO);
    }

    /**
     * 获取合同和签署信息
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("获取合同和签署信息")
    @Action(name = "获取合同和签署信息", value = "CON_QUERY_CON_TASK_INFO")
    @PostMapping("queryConTaskInfo")
    public Response<ConCtSigntaskTrDTO> queryConTaskInfo(@RequestBody ConContractSignwayDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctErpService.queryConTaskInfo(request);
        return Response.ok(conCtSigntaskTrDTO);
    }

    /**
     * 更新合同签署人和签署时间
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("更新合同签署人和签署时间")
    @Action(name = "更新合同签署人和签署时间", value = "CON_UPDATE_SIGNATORY_AND_SIGNTIME")
    @PostMapping("updateSignatoryAndSignTime")
    public Response<ConCtSigntaskTrDTO> updateSignatoryAndSignTime(@RequestBody ConCtSigntaskTrDTO request) {
        ConCtSigntaskTrDTO conCtSigntaskTrDTO = ctErpService.updateSignatoryAndSignTime(request);
        return Response.ok(conCtSigntaskTrDTO);
    }

    /**
     * 补充合同关联初始附件
     *
     * @param request action入参
     * @return GenCtAttachmentLinkTrDTO
     */
    @ApiOperation("补充合同关联初始附件")
    @Action(name = "补充合同关联初始附件", value = "CT_FILL_CT_ATTACHMENT_LINK_ACTION")
    @PostMapping("/fillCtAttachmentLink")
    public Response<Map<String,Object>> fillCtAttachmentLink(@RequestBody Map<String,Object> request){
        ctErpService.fillCtAttachmentLink(request);
        return Response.ok(request);
    }

    /**
     * 生成PDF临时链接
     * @param request action入参
     * @return void
     */
    @ApiOperation("生成PDF临时链接")
    @Action(name = "生成PDF临时链接", value = "GENERATE_TEMPORARY_LINKS")
    @PostMapping("/GENERATE_TEMPORARY_LINKS")
    public Response<GenCtHeadTrExtDTO> generateTemporaryLinks(@RequestBody GenCtHeadTrExtDTO request) {
        return Response.ok(ctErpService.generateTemporaryLinks(request));
    }

    /**
     * 合同审批通过后追加审批人签名到编制的合同中：
     * 1. 识别审批流中审批任务包含关键词：【】的审批节点
     * 2. 把节点名和审批人的签名图片追加到合同文件中
     * 3. 修改合同附件文件
     * @param request action入参
     * @return void
     */
    @ApiOperation("")
    @Action(name = "在合同文件中追加审批人的签名图片", value = "CON_APPEND_SIGN_IMG")
    @PostMapping("createSignTaskByContract")
    public Response<CtHeadTrDTO> appendSignImage(@RequestBody ConAppendSingImgContextDTO request) {
        // 审批流《编制合同审批》审批通过回调，第一步
        ctErpService.appendSignImage(request);
        return Response.ok();
    }


    /**
     * 删除签署完的文件
     * @param request action入参
     * @return void
     */
    @ApiOperation("删除合同签署完成的文件")
    @Action(name = "删除合同签署完成的文件", value = "CON_DELETE_THE_SIGNED_FILE")
    @PostMapping("/deleteTheSignedFile")
    public Response<GenCtHeadTrExtDTO> deleteTheSignedFile(@RequestBody GenCtHeadTrExtDTO request) {
        ctErpService.deleteTheSignedFile(request.getId());
        return Response.ok(request);
    }

    /**
     * 删除编制生成的合同文件:
     *
     * 在签署过程中，撤回合同，需要删除掉之前编制生成的文件。否则会导致重复签名
     * @param request action入参
     * @return void
     */
    @ApiOperation("删除合同签署完成的文件")
    @Action(name = "删除合同签署完成的文件", value = "CON_DELETE_THE_DRAFTED_FILE")
    @PostMapping("/deleteTheDraftedFile")
    public Response<GenCtHeadTrExtDTO> deleteTheDraftedFile(@RequestBody GenCtHeadTrExtDTO request) {
        ctErpService.deleteTheDraftedFile(request.getId());
        return Response.ok(request);
    }

    /**
     * 合同状态更新为 编制中
     * 合同审核状态更新为 初始状态 null
     * 校验合同当前是否存在正在进行的签署任务；如果签署任务不是已关闭或者是已签署 那么就提示需要先关闭签署任务
     * @param request res
     * @return res
     */
    @ApiOperation("重新编制状态更改")
    @Action(name = "重新编制状态更改", value = "con_again_submit_status_change_action")
    @PostMapping("/con_again_submit_status_change_action")
    public Response<GenCtHeadTrExtDTO> conAgainSubmitStatusChangeAction(@RequestBody GenCtHeadTrExtDTO request) {
        ctErpService.conAgainSubmitStatusChangeAction(request);
        return Response.ok(request);
    }

    /**
     * console引用服务 con_sign_affirm_service
     * @param request 请求参数
     * @return res
     */
    @ApiOperation("合同签署确认")
    @Action(name = "合同签署确认", value = "con_sign_affirm_action")
    @PostMapping("/con_sign_affirm_action")
    public Response<GenCtHeadTrExtDTO> conSignAffirmAction(@RequestBody GenCtHeadTrExtDTO request) {
        ctErpService.conSignAffirmAction(request);
        return Response.ok(request);
    }

    @ApiOperation("更新签收时间")
    @Action(name = "更新签收时间", value = "UPDATE_SIGN_FOR_DATE_ACTION")
    @PostMapping("/UPDATE_SIGN_FOR_DATE_ACTION")
    public Response<Void> updateSignForDateAction(@RequestBody CtHeadTrDTO request) {
        ctErpService.updateSignForDateAction(request);
        return Response.ok();
    }
}
