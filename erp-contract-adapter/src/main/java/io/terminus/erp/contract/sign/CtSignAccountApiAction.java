package io.terminus.erp.contract.sign;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.erp.contract.domain.service.CtSignAccountService;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtEsignatureTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtSignatoryAccountTrDTO;
import io.terminus.erp.contract.spi.model.tp.dto.ConSignatoryAccountRechargeDTO;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 电子签章账户服务
 *
 * @className: CtSignAccountApiAction
 * @author: charl
 * @date: 2023/11/5 14:26
 */
@Api(tags = "电子签章账户服务")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/service/erp-contract/action/ct-sign-account/")
public class CtSignAccountApiAction {

    private final CtSignAccountService ctSignAccountService;

    /**
     * 创建电子签章账户
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("创建电子签章账户")
    @Action(name = "创建电子签章账户", value = "CON_CREATE_PAYMENT_ACCOUNT")
    @PostMapping("/createPaymentAccount")
    public Response<ConCtSignatoryAccountTrDTO> createPaymentAccount(@RequestBody ConCtEsignatureTrDTO request) {
        return Response.ok(ctSignAccountService.createPaymentAccount(request));
    }

    /**
     * 创建支付单
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("创建支付单")
    @Action(name = "创建支付单", value = "CON_CREATE_PAYMENT_ORDER")
    @PostMapping("/createPaymentOrder")
    public Response<ConSignatoryAccountRechargeDTO> createPaymentOrder(@RequestBody ConSignatoryAccountRechargeDTO request) {
        return Response.ok(ctSignAccountService.createPaymentOrder(request));
    }

    /**
     * 生成支付二维码
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("生成支付二维码")
    @Action(name = "生成支付二维码", value = "CON_CREATE_PAYMENT_QR_CODE")
    @PostMapping("/createPaymentQRCode")
    public Response<ConSignatoryAccountRechargeDTO> createPaymentQRCode(@RequestBody ConSignatoryAccountRechargeDTO request) {
        return Response.ok(ctSignAccountService.createPaymentQRCode(request));
    }


    /**
     * 查询支付单详情
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("查询支付单详情")
    @Action(name = "查询支付单详情", value = "CON_QUERY_PAYMENT_DETAIL")
    @PostMapping("/queryPaymentDetail")
    public Response<ConSignatoryAccountRechargeDTO> queryPaymentDetail(@RequestBody ConSignatoryAccountRechargeDTO request) {
        return Response.ok(ctSignAccountService.queryPaymentDetail(request));
    }


}
