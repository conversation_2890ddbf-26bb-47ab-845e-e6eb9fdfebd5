package io.terminus.erp.contract.sign.callback;

import com.google.common.base.Throwables;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.erp.contract.domain.service.CtSignAckService;
import io.terminus.thirdparty.sign.esign.dto.OrgAuthAckRequest;
import io.terminus.thirdparty.sign.esign.dto.SignTaskAckRequest;
import io.terminus.thirdparty.sign.esign.dto.SignTaskAckResponse;
import io.terminus.trantor2.common.dto.Response;
import javax.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Arrays;
import java.util.Map;

/**
 * e签宝结构认证回调接口
 *
 * @className: SignCallbackController
 * @author: charl
 * @date: 2023/8/2 19:17
 */

@Api(tags = "协议合同")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("api/service/erp-contract/controller/ct/")
public class SignCallbackController {

    private final CtSignAckService ctSignAckService;


    /**
     * e签宝结构认证回调接口
     *
     * @param request action入参
     * @return
     */
    @ApiOperation("e签宝结构认证回调接口")
    @PostMapping("/orgAuthAck")
    public Response orgAuthAck(@RequestBody OrgAuthAckRequest request) {
        ctSignAckService.orgAuthAck(request);
        return Response.ok();
    }

    /**
     * e签宝签署任务回调接口
     *
     * @param request action入参
     * @return
     */
    @ApiOperation("e签宝签署任务回调接口")
    @PostMapping("/signTaskAck")
    public Response<SignTaskAckResponse> eSignTaskAck(@RequestBody SignTaskAckRequest request) {
        SignTaskAckResponse signTaskAckResponse = ctSignAckService.eSignTaskAck(request);
        return Response.ok(signTaskAckResponse);
    }


    /**
     * 契约锁回调:
     * 契约锁回传的contractId实际是契约锁平台的合同ID，存储在签署任务表的的flowId字段中;
     * 这里SRM里的签署任务和契约锁平台的合同应该是一对一的，和SRM的合同是一对多的，也就是一个SRM合同发起多次签署任务，对应契约锁平台多份合同
     *
     * @param request RECALLED,REJECTED,COMPLETE,TERMINATED
     * @return
     */
    @ApiOperation("契约锁签署任务回调接口")
    @RequestMapping("/qysCallBack")
    public Response qysCallBack(HttpServletRequest request) throws IOException {
        String flowId = null;
        try {
            for (Map.Entry<String, String[]> entry : request.getParameterMap().entrySet()) {
                log.info("qys call back start,request key:{},value:{}", entry.getKey(), Arrays.toString(entry.getValue()));
            }
            flowId = request.getParameter("contractId");
            log.info("qys call back start,request:{}", flowId);
            ctSignAckService.qysCallback(flowId);
            return Response.ok();
        } catch (Exception e) {
            log.error("qysCallBack contractId:{}, error: {}", flowId, Throwables.getStackTraceAsString(e));
            log.error("qysCallBack contractId:{}, error:", flowId, e);
            return Response.error("500", e.getMessage());
        }
    }


}
