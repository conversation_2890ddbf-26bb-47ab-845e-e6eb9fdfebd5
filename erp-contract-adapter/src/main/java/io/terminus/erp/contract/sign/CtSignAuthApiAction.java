package io.terminus.erp.contract.sign;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.erp.contract.domain.service.QysSignService;
import io.terminus.erp.contract.spi.model.tp.dto.ConCtEsignatureTrDTO;
import io.terminus.trantor2.common.dto.Response;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: CtSignAuthApiAction
 * @author: charl
 * @date: 2023/10/13 19:12
 */

@Api(tags = "电子签章认证服务")
@RequiredArgsConstructor
@RestController
@RequestMapping("api/service/erp-contract/action/ct-sign-auth/")
public class CtSignAuthApiAction {

    private final QysSignService qysSignService;


    /**
     * 创建电子签章公司
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("创建电子签章公司")
    @Action(name = "创建电子签章公司", value = "CON_AUTH_CREATE_COMPANY")
    @PostMapping("/createSupplierCompany")
    public Response<ConCtEsignatureTrDTO> createSupplierCompany(@RequestBody ConCtEsignatureTrDTO request) {
        return Response.ok(qysSignService.createSupplierCompany(request));
    }

    /**
     * 提交电子签章认证
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("提交电子签章认证")
    @Action(name = "提交电子签章认证", value = "CON_AUTH_SUBMIT_COMPANY")
    @PostMapping("/submitCompanyAuthInfo")
    public Response<ConCtEsignatureTrDTO> submitCompanyAuthInfo(@RequestBody ConCtEsignatureTrDTO request) {
        return Response.ok(qysSignService.submitCompanyAuthInfo(request));
    }

    /**
     * 校验打款金额
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("校验打款金额")
    @Action(name = "校验打款金额", value = "CON_CHECK_AUTH_PAY_AMOUNT")
    @PostMapping("/checkAuthPayAmount")
    public Response<ConCtEsignatureTrDTO> checkAuthPayAmount(@RequestBody ConCtEsignatureTrDTO request) {
        return Response.ok(qysSignService.checkAuthPayAmount(request));
    }


    /**
     * 修改公司签署方式
     *
     * @param request action入参
     * @return void
     */
    @ApiOperation("修改公司签署方式")
    @Action(name = "修改公司签署方式", value = "CON_UPDATE_COMPANY_SIGN_WAY")
    @PostMapping("/updateCompanySignWay")
    public Response<ConCtEsignatureTrDTO> updateCompanySignWay(@RequestBody ConCtEsignatureTrDTO request) {
        return Response.ok(qysSignService.updateCompanySignWay(request));
    }


}
