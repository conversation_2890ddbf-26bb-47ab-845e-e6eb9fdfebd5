<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>erp-contract</artifactId>
        <groupId>io.terminus.erp</groupId>
        <version>1.0.0.WQ.UAT-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>erp-contract-adapter</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.terminus.erp</groupId>
            <artifactId>erp-contract-app</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.erp</groupId>
            <artifactId>erp-framework-runtime</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.common</groupId>
                    <artifactId>terminus-common-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.terminus.common</groupId>
                    <artifactId>terminus-spring-boot-starter-mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-common-api</artifactId>
            <version>1.0.1.RELEASE</version>
        </dependency>
    </dependencies>
</project>
